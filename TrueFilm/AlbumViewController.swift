////
////  AlbumViewController.swift
////  TrueFilm
////
////  Created on 2025/5/7.
////
//
//import UIKit
//import Photos
//import AVKit
//
//class AlbumViewController: UIViewController {
//    
//    // MARK: - Properties
//    private var collectionView: UICollectionView!
//    private var fetchResult: PHFetchResult<PHAsset>?
//    private let imageManager = PHCachingImageManager()
//    private var thumbnailSize: CGSize = CGSize(width: 200, height: 200)
//    
//    // MARK: - Lifecycle
//    
//    override func viewDidLoad() {
//        super.viewDidLoad()
//        
//        setupUI()
//        checkPhotoLibraryPermission()
//    }
//    
//    override func viewWillAppear(_ animated: Bool) {
//        super.viewWillAppear(animated)
//        
//        // 设置导航栏标题
//        title = "相册".localized
//        
//        // 添加关闭按钮
//        let closeButton = UIBarButtonItem(barButtonSystemItem: .close, target: self, action: #selector(dismissAlbum))
//        navigationItem.leftBarButtonItem = closeButton
//    }
//    
//    override func viewWillLayoutSubviews() {
//        super.viewWillLayoutSubviews()
//        
//        // 更新缩略图大小
//        let scale = UIScreen.main.scale
//        let cellSize = (collectionView.collectionViewLayout as? UICollectionViewFlowLayout)?.itemSize ?? CGSize(width: 100, height: 100)
//        thumbnailSize = CGSize(width: cellSize.width * scale, height: cellSize.height * scale)
//    }
//    
//    // MARK: - UI Setup
//    
//    private func setupUI() {
//        view.backgroundColor = .black
//        
//        // 创建集合视图布局
//        let layout = UICollectionViewFlowLayout()
//        let spacing: CGFloat = 2
//        let numberOfItemsPerRow: CGFloat = UIDevice.current.userInterfaceIdiom == .pad ? 5 : 3
//        let availableWidth = view.bounds.width - (spacing * (numberOfItemsPerRow + 1))
//        let itemWidth = availableWidth / numberOfItemsPerRow
//        layout.itemSize = CGSize(width: itemWidth, height: itemWidth)
//        layout.minimumInteritemSpacing = spacing
//        layout.minimumLineSpacing = spacing
//        layout.sectionInset = UIEdgeInsets(top: spacing, left: spacing, bottom: spacing, right: spacing)
//        
//        // 创建集合视图
//        collectionView = UICollectionView(frame: view.bounds, collectionViewLayout: layout)
//        collectionView.backgroundColor = .black
//        collectionView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
//        collectionView.delegate = self
//        collectionView.dataSource = self
//        collectionView.register(PhotoCell.self, forCellWithReuseIdentifier: "PhotoCell")
//        view.addSubview(collectionView)
//    }
//    
//    // MARK: - Actions
//    
//    @objc private func dismissAlbum() {
//        dismiss(animated: true)
//    }
//    
//    // MARK: - Photo Library Access
//    
//    private func checkPhotoLibraryPermission() {
//        let status = PHPhotoLibrary.authorizationStatus()
//        
//        switch status {
//        case .authorized, .limited:
//            fetchPhotos()
//        case .notDetermined:
//            PHPhotoLibrary.requestAuthorization { [weak self] status in
//                DispatchQueue.main.async {
//                    if status == .authorized || status == .limited {
//                        self?.fetchPhotos()
//                    } else {
//                        self?.showPermissionDeniedAlert()
//                    }
//                }
//            }
//        case .denied, .restricted:
//            showPermissionDeniedAlert()
//        @unknown default:
//            showPermissionDeniedAlert()
//        }
//    }
//    
//    private func fetchPhotos() {
//        // 创建获取选项
//        let fetchOptions = PHFetchOptions()
//        fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
//        
//        // 获取所有媒体类型的资源
//        // 这里用元组合并图片和视频查询结果
//        let imageResult = PHAsset.fetchAssets(with: .image, options: fetchOptions)
//        let videoResult = PHAsset.fetchAssets(with: .video, options: fetchOptions)
//        
//        // 合并两种类型并按时间排序
//        let combinedAssets = NSMutableArray()
//        
//        // 将结果添加到一个数组中
//        imageResult.enumerateObjects { (asset, _, _) in
//            combinedAssets.add(asset)
//        }
//        
//        videoResult.enumerateObjects { (asset, _, _) in
//            combinedAssets.add(asset)
//        }
//        
//        // 按创建时间排序
//        let sortedAssets = (combinedAssets as NSArray).sortedArray(using: [
//            NSSortDescriptor(key: "creationDate", ascending: false)
//        ])
//        
//        // 创建一个新的PHFetchResult
//        if let assets = sortedAssets as? [PHAsset], !assets.isEmpty {
//            // 使用第一个元素的本地标识符来获取资源
//            let localIDs = assets.map { $0.localIdentifier }
//            let newFetchOptions = PHFetchOptions()
//            newFetchOptions.predicate = NSPredicate(format: "localIdentifier IN %@", localIDs)
//            newFetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
//            fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: localIDs, options: newFetchOptions)
//        } else {
//            // 如果没有资源，创建一个空的结果
//            fetchResult = PHFetchResult<PHAsset>()
//        }
//        
//        DispatchQueue.main.async { [weak self] in
//            self?.collectionView.reloadData()
//        }
//    }
//    
//    private func showPermissionDeniedAlert() {
//        let alert = UIAlertController(
//            title: "无法访问相册".localized,
//            message: "请在设置中允许访问相册".localized,
//            preferredStyle: .alert
//        )
//        
//        alert.addAction(UIAlertAction(title: "取消".localized, style: .cancel))
//        alert.addAction(UIAlertAction(title: "设置".localized, style: .default) { _ in
//            if let url = URL(string: UIApplication.openSettingsURLString) {
//                UIApplication.shared.open(url)
//            }
//        })
//        
//        present(alert, animated: true)
//    }
//}
//
//// MARK: - UICollectionViewDataSource
//
//extension AlbumViewController: UICollectionViewDataSource {
//    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
//        return fetchResult?.count ?? 0
//    }
//    
//    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
//        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PhotoCell", for: indexPath) as! PhotoCell
//        
//        // 获取媒体资源
//        if let asset = fetchResult?[indexPath.item] {
//            // 根据资源类型配置单元格
//            if asset.mediaType == .video {
//                cell.configureAsVideo()
//            } else {
//                cell.configureAsImage()
//            }
//            
//            // 请求缩略图
//            imageManager.requestImage(for: asset, targetSize: thumbnailSize, contentMode: .aspectFill, options: nil) { image, _ in
//                if let image = image {
//                    cell.imageView.image = image
//                }
//            }
//        }
//        
//        return cell
//    }
//}
//
//// MARK: - UICollectionViewDelegate
//
//extension AlbumViewController: UICollectionViewDelegate {
//    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
//        guard let asset = fetchResult?[indexPath.item] else { return }
//        
//        // 判断资源类型
//        if asset.mediaType == .image {
//            // 处理图像
//            let options = PHImageRequestOptions()
//            options.isNetworkAccessAllowed = true
//            options.deliveryMode = .highQualityFormat
//            
//            imageManager.requestImage(for: asset, targetSize: PHImageManagerMaximumSize, contentMode: .aspectFit, options: options) { [weak self] image, info in
//                guard let self = self, let image = image else { return }
//                
//                DispatchQueue.main.async {
//                    // 显示全屏图像
//                    let detailVC = PhotoDetailViewController(image: image)
//                    self.navigationController?.pushViewController(detailVC, animated: true)
//                }
//            }
//        } else if asset.mediaType == .video {
//            // 处理视频
//            let options = PHVideoRequestOptions()
//            options.isNetworkAccessAllowed = true
//            options.deliveryMode = .highQualityFormat
//            
//            imageManager.requestAVAsset(forVideo: asset, options: options) { [weak self] avAsset, audioMix, info in
//                guard let self = self, let avAsset = avAsset else { return }
//                
//                // 如果是 AVURLAsset，直接获取 URL
//                if let urlAsset = avAsset as? AVURLAsset {
//                    let url = urlAsset.url
//                    DispatchQueue.main.async {
//                        // 创建一个视频播放器控制器
//                        let playerViewController = AVPlayerViewController()
//                        let player = AVPlayer(url: url)
//                        playerViewController.player = player
//                        
//                        // 确保视频播放器有标准控制和关闭按钮
//                        playerViewController.showsPlaybackControls = true
//                        
//                        // 使用一个容器视图控制器来添加我们的自定义关闭按钮
//                        let containerVC = UIViewController()
//                        self.addChild(containerVC)
//                        
//                        // 添加播放器作为子视图控制器
//                        containerVC.addChild(playerViewController)
//                        containerVC.view.addSubview(playerViewController.view)
//                        playerViewController.view.frame = containerVC.view.bounds
//                        playerViewController.view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
//                        playerViewController.didMove(toParent: containerVC)
//                        
//                        // 添加自定义关闭按钮
//                        let closeButton = UIButton(type: .system)
//                        closeButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
//                        closeButton.tintColor = .white
//                        closeButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
//                        closeButton.layer.cornerRadius = 20
//                        closeButton.frame = CGRect(x: containerVC.view.bounds.width - 50, y: 50, width: 40, height: 40)
//                        closeButton.autoresizingMask = [.flexibleLeftMargin, .flexibleBottomMargin]
//                        
//                        closeButton.addTarget(self, action: #selector(self.dismissVideoPlayer), for: .touchUpInside)
//                        containerVC.view.addSubview(closeButton)
//                        containerVC.view.bringSubviewToFront(closeButton)
//                        
//                        // 呈现包含关闭按钮的容器视图
//                        self.present(containerVC, animated: true) {
//                            player.play()
//                        }
//                        
//                        // 保存容器引用以便关闭
//                        self.currentVideoContainerVC = containerVC
//                    }
//                }
//            }
//        }
//    }
//    
//    // 保存当前播放视频的容器视图控制器
//    private var currentVideoContainerVC: UIViewController?
//    
//    @objc private func dismissVideoPlayer() {
//        currentVideoContainerVC?.dismiss(animated: true)
//        currentVideoContainerVC = nil
//    }
//}
//
//// MARK: - PhotoCell
//
//class PhotoCell: UICollectionViewCell {
//    let imageView = UIImageView()
//    let videoIndicator = UIImageView(image: UIImage(systemName: "play.fill"))
//    
//    override init(frame: CGRect) {
//        super.init(frame: frame)
//        
//        // 设置图像视图
//        imageView.contentMode = .scaleAspectFill
//        imageView.clipsToBounds = true
//        imageView.frame = contentView.bounds
//        imageView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
//        contentView.addSubview(imageView)
//        
//        // 设置视频指示器
//        videoIndicator.tintColor = .white
//        videoIndicator.contentMode = .scaleAspectFit
//        videoIndicator.backgroundColor = UIColor.black.withAlphaComponent(0.3)
//        videoIndicator.layer.cornerRadius = 12
//        videoIndicator.frame = CGRect(x: 0, y: 0, width: 24, height: 24)
//        videoIndicator.center = CGPoint(x: frame.width - 20, y: frame.height - 20)
//        videoIndicator.isHidden = true
//        contentView.addSubview(videoIndicator)
//    }
//    
//    required init?(coder: NSCoder) {
//        fatalError("init(coder:) has not been implemented")
//    }
//    
//    override func prepareForReuse() {
//        super.prepareForReuse()
//        imageView.image = nil
//        videoIndicator.isHidden = true
//    }
//    
//    // 设置为视频模式
//    func configureAsVideo() {
//        videoIndicator.isHidden = false
//    }
//    
//    // 设置为图片模式
//    func configureAsImage() {
//        videoIndicator.isHidden = true
//    }
//}
//
//// MARK: - PhotoDetailViewController
//
//class PhotoDetailViewController: UIViewController {
//    private let scrollView = UIScrollView()
//    private let imageView = UIImageView()
//    private let closeButton = UIButton(type: .system)
//    
//    init(image: UIImage) {
//        super.init(nibName: nil, bundle: nil)
//        imageView.image = image
//    }
//    
//    required init?(coder: NSCoder) {
//        fatalError("init(coder:) has not been implemented")
//    }
//    
//    override func viewDidLoad() {
//        super.viewDidLoad()
//        
//        view.backgroundColor = .black
//        
//        // 设置滚动视图
//        scrollView.frame = view.bounds
//        scrollView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
//        scrollView.delegate = self
//        scrollView.minimumZoomScale = 1.0
//        scrollView.maximumZoomScale = 3.0
//        view.addSubview(scrollView)
//        
//        // 设置图像视图
//        imageView.contentMode = .scaleAspectFit
//        imageView.frame = scrollView.bounds
//        scrollView.addSubview(imageView)
//        
//        // 添加双击手势
//        let doubleTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleDoubleTap(_:)))
//        doubleTapGesture.numberOfTapsRequired = 2
//        scrollView.addGestureRecognizer(doubleTapGesture)
//        
//        // 添加关闭按钮
//        setupCloseButton()
//        
//        // 设置导航栏的关闭按钮
//        let closeBarButton = UIBarButtonItem(barButtonSystemItem: .close, target: self, action: #selector(dismissView))
//        navigationItem.rightBarButtonItem = closeBarButton
//    }
//    
//    private func setupCloseButton() {
//        // 创建并配置浮动关闭按钮
//        closeButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
//        closeButton.tintColor = .white
//        closeButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
//        closeButton.layer.cornerRadius = 20
//        closeButton.addTarget(self, action: #selector(dismissView), for: .touchUpInside)
//        
//        // 设置按钮大小和位置
//        closeButton.frame = CGRect(x: view.bounds.width - 50, y: 50, width: 40, height: 40)
//        closeButton.autoresizingMask = [.flexibleLeftMargin, .flexibleBottomMargin]
//        
//        view.addSubview(closeButton)
//    }
//    
//    @objc private func dismissView() {
//        // 退出当前视图
//        navigationController?.popViewController(animated: true) ?? dismiss(animated: true)
//    }
//    
//    @objc private func handleDoubleTap(_ gesture: UITapGestureRecognizer) {
//        if scrollView.zoomScale > 1.0 {
//            scrollView.setZoomScale(1.0, animated: true)
//        } else {
//            let location = gesture.location(in: imageView)
//            let rect = CGRect(x: location.x - 50, y: location.y - 50, width: 100, height: 100)
//            scrollView.zoom(to: rect, animated: true)
//        }
//    }
//}
//
//// MARK: - UIScrollViewDelegate
//
//extension PhotoDetailViewController: UIScrollViewDelegate {
//    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
//        return imageView
//    }
//    
//    func scrollViewDidZoom(_ scrollView: UIScrollView) {
//        // 保持图像居中
//        let offsetX = max((scrollView.bounds.width - scrollView.contentSize.width) * 0.5, 0)
//        let offsetY = max((scrollView.bounds.height - scrollView.contentSize.height) * 0.5, 0)
//        scrollView.contentInset = UIEdgeInsets(top: offsetY, left: offsetX, bottom: 0, right: 0)
//    }
//}
//
//// 使用项目中已有的 String.localized 扩展
