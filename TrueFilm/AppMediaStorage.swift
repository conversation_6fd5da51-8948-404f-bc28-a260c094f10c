//
//  AppMediaStorage.swift
//  TrueFilm
//
//  Created on 2025/5/8.
//

import Foundation
import UIKit
import AVFoundation
import Photos

// 系统相册资源数据结构 - 用于记录应用照片与系统照片的对应关系
struct PhotoAssetMapping: Codable {
    let appPhotoFileName: String  // 应用内照片文件名
    let assetLocalIdentifier: String  // 系统相册资源标识符
    let creationDate: Date  // 创建日期
}

// 媒体文件索引数据结构
struct MediaFileIndex: Codable {
    let fileName: String
    let filePath: String
    let fileType: MediaFileType
    let creationDate: Date
    let fileSize: Int64
    let lastModified: Date

    enum MediaFileType: String, Codable {
        case image, video
    }
}

// 应用内媒体管理类
class AppMediaStorage {
    static let shared = AppMediaStorage()

    // 缓存已生成的视频缩略图，避免重复生成
    private var thumbnailCache = NSCache<NSString, UIImage>()
    // 用于保存视频URL到尺寸的映射，避免重复处理
    private var videoDimensionsCache = [URL: CGSize]()
    private let videoCacheQueue = DispatchQueue(label: "com.truefilm.videocache", attributes: .concurrent)
    // 用于存储照片资源映射关系的数组
    private var photoAssetMappings: [PhotoAssetMapping] = []
    // 媒体文件索引缓存
    private var mediaFileIndex: [MediaFileIndex] = []
    // 已加载的图片缓存
    private var imageCache = NSCache<NSString, UIImage>()
    // 文件系统变化监控
    private var directoryMonitor: DirectoryMonitor?

    // 映射数据文件路径
    private var assetMappingsFilePath: URL {
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsDirectory.appendingPathComponent("PhotoAssetMappings.json")
    }

    // 媒体文件索引路径
    private var mediaIndexFilePath: URL {
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsDirectory.appendingPathComponent("MediaFileIndex.json")
    }

    private init() {
        // 确保应用目录存在
        createAppDirectoryIfNeeded()

        // 设置缓存限制
        thumbnailCache.countLimit = 50 // 最多缓存50个缩略图
        imageCache.countLimit = 30 // 最多缓存30张图片
        imageCache.totalCostLimit = 50 * 1024 * 1024 // 50MB内存限制

        // 加载照片资源映射数据
        loadPhotoAssetMappings()

        // 加载媒体文件索引
        loadMediaFileIndex()

                 // 启动目录监控
         startDirectoryMonitoring()

         // 监听内存警告
         NotificationCenter.default.addObserver(
             self,
             selector: #selector(handleMemoryWarning),
             name: UIApplication.didReceiveMemoryWarningNotification,
             object: nil
         )
     }

     deinit {
         stopDirectoryMonitoring()
         NotificationCenter.default.removeObserver(self)
     }

     @objc private func handleMemoryWarning() {
         print("⚠️ 收到内存警告，清理媒体缓存")
         clearAllCaches()
     }

     // 清理所有缓存
     func clearAllCaches() {
         thumbnailCache.removeAllObjects()
         imageCache.removeAllObjects()
         videoCacheQueue.async(flags: .barrier) {
             self.videoDimensionsCache.removeAll()
         }
         print("🧹 已清理所有媒体缓存")
     }

         // 获取索引状态信息
    func getIndexStatus() -> (totalFiles: Int, imageCount: Int, videoCount: Int, isIndexReady: Bool) {
        let imageCount = mediaFileIndex.filter { $0.fileType == .image }.count
        let videoCount = mediaFileIndex.filter { $0.fileType == .video }.count
        let isReady = !mediaFileIndex.isEmpty

        return (mediaFileIndex.count, imageCount, videoCount, isReady)
    }

    // 获取媒体文件索引（供外部访问）
    func getMediaFileIndex() -> [MediaFileIndex] {
        return mediaFileIndex
    }

    // 调试方法：检查目录和索引状态
    func debugDirectoryStatus() {
        print("🔍 调试目录状态:")

        // 检查目录是否存在
        let directories = [appMediaDirectory, imagesDirectory, videosDirectory]
        for dir in directories {
            let exists = FileManager.default.fileExists(atPath: dir.path)
            print("📁 \(dir.lastPathComponent): \(exists ? "存在" : "不存在") -> \(dir.path)")

            if exists {
                do {
                    let files = try FileManager.default.contentsOfDirectory(atPath: dir.path)
                    print("   📄 包含 \(files.count) 个文件: \(files.prefix(3).joined(separator: ", "))")
                } catch {
                    print("   ❌ 无法读取目录内容: \(error)")
                }
            }
        }

        // 检查索引状态
        print("📋 索引状态: \(mediaFileIndex.count) 个文件")
        if !mediaFileIndex.isEmpty {
            let firstFile = mediaFileIndex[0]
            print("   🎯 第一个文件: \(firstFile.fileName) -> \(firstFile.filePath)")
            let exists = FileManager.default.fileExists(atPath: firstFile.filePath)
            print("   ✅ 第一个文件存在: \(exists)")
        }

        // 检查索引文件本身是否存在
        let indexExists = FileManager.default.fileExists(atPath: mediaIndexFilePath.path)
        print("📄 索引文件存在: \(indexExists) -> \(mediaIndexFilePath.path)")
    }

    // 清理和重建索引（调试用）
    func cleanAndRebuildIndex(showProgress: Bool = true, completion: @escaping (Bool) -> Void) {
        print("🧹 开始清理和重建索引...")

        // 清理现有索引
        mediaFileIndex.removeAll()

        // 删除索引文件
        try? FileManager.default.removeItem(at: mediaIndexFilePath)

        if showProgress {
            // 立即显示进度
            DispatchQueue.main.async {
                self.indexProgressCallback?("🔄 正在清理旧索引...", 0.0)
            }
        }

        // 重建索引
        DispatchQueue.global(qos: .userInitiated).async {
            self.rebuildMediaFileIndex {
                DispatchQueue.main.async {
                    completion(true)
                }
            }
        }
    }

         // 强制重建索引（用于调试或修复损坏的索引）
    func forceRebuildIndex(completion: @escaping () -> Void) {
        print("🔄 手动触发索引重建...")
        DispatchQueue.global(qos: .userInitiated).async {
            self.rebuildMediaFileIndex {
                DispatchQueue.main.async {
                    completion()
                }
            }
        }
    }

    // MARK: - 媒体文件索引管理

    // 加载媒体文件索引
    private func loadMediaFileIndex() {
        do {
            let data = try Data(contentsOf: mediaIndexFilePath)
            mediaFileIndex = try JSONDecoder().decode([MediaFileIndex].self, from: data)
            print("✅ 成功加载媒体文件索引: \(mediaFileIndex.count)条记录")
        } catch {
            print("⚠️ 加载媒体文件索引失败或文件不存在: \(error.localizedDescription)")
            mediaFileIndex = []
            // 不在这里自动重建索引，等待用户触发时再显示进度
        }
    }

    // 保存媒体文件索引
    private func saveMediaFileIndex() {
        do {
            let data = try JSONEncoder().encode(mediaFileIndex)
            try data.write(to: mediaIndexFilePath)
            print("✅ 成功保存媒体文件索引: \(mediaFileIndex.count)条记录")
        } catch {
            print("❌ 保存媒体文件索引失败: \(error.localizedDescription)")
        }
    }

    // 进度回调类型定义
    typealias IndexProgressCallback = (String, Double) -> Void
    private var indexProgressCallback: IndexProgressCallback?

    // 设置索引构建进度回调
    func setIndexProgressCallback(_ callback: @escaping IndexProgressCallback) {
        indexProgressCallback = callback
    }

    // 重新构建媒体文件索引
    private func rebuildMediaFileIndex(completion: (() -> Void)? = nil) {
        print("🔄 开始重新构建媒体文件索引...")

        let fileManager = FileManager.default
        var newIndex: [MediaFileIndex] = []
        var totalFiles = 0
        var processedFiles = 0

        // 先计算总文件数量
        let imageFileCount = (try? fileManager.contentsOfDirectory(at: imagesDirectory, includingPropertiesForKeys: nil))?.count ?? 0
        let videoFileCount = (try? fileManager.contentsOfDirectory(at: videosDirectory, includingPropertiesForKeys: nil))?.count ?? 0
        totalFiles = imageFileCount + videoFileCount

        print("📊 索引重建：发现 \(totalFiles) 个文件（图片: \(imageFileCount), 视频: \(videoFileCount)）")

        // 🔧 修复：如果没有文件，直接完成索引构建
        if totalFiles == 0 {
            print("📋 没有文件需要索引，直接完成")
            DispatchQueue.main.async {
                self.mediaFileIndex = []
                self.saveMediaFileIndex()
                self.indexProgressCallback?("索引构建完成", 1.0)

                // 1秒后清除进度回调
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    self.indexProgressCallback = nil
                }

                completion?()
            }
            return
        }

        DispatchQueue.main.async {
            self.indexProgressCallback?(NSLocalizedString("index_scanning_files", comment: ""), 0.1)
        }

        DispatchQueue.main.async {
            let message = String.localizedStringWithFormat(NSLocalizedString("index_found_files", comment: ""), totalFiles)
            self.indexProgressCallback?(message, 0.2)
        }

        // 扫描图片目录
        if let imageFiles = try? fileManager.contentsOfDirectory(
            at: imagesDirectory,
            includingPropertiesForKeys: [.creationDateKey, .fileSizeKey, .contentModificationDateKey]
        ) {
            for (index, imageURL) in imageFiles.enumerated() {
                if let attributes = try? fileManager.attributesOfItem(atPath: imageURL.path),
                   let creationDate = attributes[FileAttributeKey.creationDate] as? Date,
                   let fileSize = attributes[FileAttributeKey.size] as? Int64,
                   let modifiedDate = attributes[FileAttributeKey.modificationDate] as? Date {

                    let indexItem = MediaFileIndex(
                        fileName: imageURL.lastPathComponent,
                        filePath: imageURL.path,
                        fileType: .image,
                        creationDate: creationDate,
                        fileSize: fileSize,
                        lastModified: modifiedDate
                    )
                    newIndex.append(indexItem)
                    processedFiles += 1

                    // 更新进度 (图片处理占总进度的60%)
                    if index % 5 == 0 || index == imageFiles.count - 1 {
                        let progress = 0.2 + (Double(processedFiles) / Double(totalFiles)) * 0.6
                        DispatchQueue.main.async {
                            let message = String.localizedStringWithFormat(NSLocalizedString("index_processing_images", comment: ""), processedFiles, totalFiles)
                            self.indexProgressCallback?(message, progress)
                        }
                    }
                }
            }
        }

        // 扫描视频目录
        if let videoFiles = try? fileManager.contentsOfDirectory(
            at: videosDirectory,
            includingPropertiesForKeys: [.creationDateKey, .fileSizeKey, .contentModificationDateKey]
        ) {
            for (index, videoURL) in videoFiles.enumerated() {
                if let attributes = try? fileManager.attributesOfItem(atPath: videoURL.path),
                   let creationDate = attributes[FileAttributeKey.creationDate] as? Date,
                   let fileSize = attributes[FileAttributeKey.size] as? Int64,
                   let modifiedDate = attributes[FileAttributeKey.modificationDate] as? Date {

                    let indexItem = MediaFileIndex(
                        fileName: videoURL.lastPathComponent,
                        filePath: videoURL.path,
                        fileType: .video,
                        creationDate: creationDate,
                        fileSize: fileSize,
                        lastModified: modifiedDate
                    )
                    newIndex.append(indexItem)
                    processedFiles += 1

                    // 更新进度 (视频处理占总进度的20%)
                    if index % 3 == 0 || index == videoFiles.count - 1 {
                        let progress = 0.8 + (Double(index + 1) / Double(videoFiles.count)) * 0.15
                        DispatchQueue.main.async {
                            let message = String.localizedStringWithFormat(NSLocalizedString("index_processing_videos", comment: ""), processedFiles, totalFiles)
                            self.indexProgressCallback?(message, progress)
                        }
                    }
                }
            }
        }

        // 排序阶段
        DispatchQueue.main.async {
            self.indexProgressCallback?(NSLocalizedString("index_sorting_files", comment: ""), 0.95)
        }

        // 🔧 修复：按创建时间排序（最新的在前），确保排序稳定
        newIndex.sort { item1, item2 in
            // 首先按创建时间排序
            if item1.creationDate != item2.creationDate {
                return item1.creationDate > item2.creationDate
            }
            // 如果创建时间相同，按文件名排序确保稳定性
            return item1.fileName > item2.fileName
        }

        // 更新索引并保存
        DispatchQueue.main.async {
            self.indexProgressCallback?(NSLocalizedString("index_saving_index", comment: ""), 0.98)

            self.mediaFileIndex = newIndex
            self.saveMediaFileIndex()

            // 完成
            self.indexProgressCallback?(NSLocalizedString("index_build_complete", comment: ""), 1.0)

            // 1秒后清除进度回调
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.indexProgressCallback = nil
            }

            print("✅ 媒体文件索引重建完成，共 \(newIndex.count) 个文件")

            // 调用完成回调
            completion?()
        }
    }

    // MARK: - 照片资源映射管理

    // 加载照片资源映射数据
    private func loadPhotoAssetMappings() {
        do {
            let data = try Data(contentsOf: assetMappingsFilePath)
            photoAssetMappings = try JSONDecoder().decode([PhotoAssetMapping].self, from: data)
            print("成功加载照片资源映射数据: \(photoAssetMappings.count)条记录")
        } catch {
            print("加载照片资源映射数据失败或文件不存在: \(error.localizedDescription)")
            photoAssetMappings = []
        }
    }

    // 保存照片资源映射数据
    private func savePhotoAssetMappings() {
        do {
            let data = try JSONEncoder().encode(photoAssetMappings)
            try data.write(to: assetMappingsFilePath)
            print("成功保存照片资源映射数据: \(photoAssetMappings.count)条记录")
        } catch {
            print("保存照片资源映射数据失败: \(error.localizedDescription)")
        }
    }

    // 添加照片资源映射
    func addPhotoAssetMapping(fileName: String, assetIdentifier: String) {
        let mapping = PhotoAssetMapping(
            appPhotoFileName: fileName,
            assetLocalIdentifier: assetIdentifier,
            creationDate: Date()
        )

        // 移除可能存在的旧记录
        photoAssetMappings.removeAll { $0.appPhotoFileName == fileName }

        // 添加新记录
        photoAssetMappings.append(mapping)

        // 保存更新后的映射数据
        savePhotoAssetMappings()
    }

    // 获取照片对应的系统资源标识符
    private func getAssetIdentifier(for fileName: String) -> String? {
        return photoAssetMappings.first(where: { $0.appPhotoFileName == fileName })?.assetLocalIdentifier
    }

    // 移除照片资源映射
    private func removePhotoAssetMapping(for fileName: String) {
        photoAssetMappings.removeAll { $0.appPhotoFileName == fileName }
        savePhotoAssetMappings()
    }

    // MARK: - 目录监控

    // 启动目录监控
    private func startDirectoryMonitoring() {
        directoryMonitor = DirectoryMonitor(urls: [imagesDirectory, videosDirectory]) { [weak self] in
            print("📁 检测到媒体目录变化，重新构建索引...")
            DispatchQueue.global(qos: .utility).async {
                self?.rebuildMediaFileIndex()
            }
        }
        directoryMonitor?.start()
    }

    // 停止目录监控
    private func stopDirectoryMonitoring() {
        directoryMonitor?.stop()
        directoryMonitor = nil
    }

    // MARK: - 目录管理

    // 应用媒体目录
    lazy var appMediaDirectory: URL = {
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let mediaDirectory = documentsDirectory.appendingPathComponent("AppMedia", isDirectory: true)
        return mediaDirectory
    }()

    // 图片目录
    lazy var imagesDirectory: URL = {
        return appMediaDirectory.appendingPathComponent("Images", isDirectory: true)
    }()

    // 视频目录
    lazy var videosDirectory: URL = {
        return appMediaDirectory.appendingPathComponent("Videos", isDirectory: true)
    }()

    // 创建应用目录
    private func createAppDirectoryIfNeeded() {
        let fileManager = FileManager.default
        let directories = [appMediaDirectory, imagesDirectory, videosDirectory]

        for directory in directories {
            var isDir: ObjCBool = true
            if !fileManager.fileExists(atPath: directory.path, isDirectory: &isDir) {
                do {
                    try fileManager.createDirectory(at: directory, withIntermediateDirectories: true, attributes: nil)
                } catch {
                    print("无法创建目录: \(directory.path) - \(error.localizedDescription)")
                }
            }
        }
    }

    // MARK: - 媒体保存

    // 保存图片到应用内
    func saveImageToApp(_ image: UIImage, completion: @escaping (Bool, URL?) -> Void) {
        // 创建唯一文件名
        let fileName = "image_\(Date().timeIntervalSince1970).jpg"
        let fileURL = imagesDirectory.appendingPathComponent(fileName)

        // 转换为JPEG数据
        guard let imageData = image.jpegData(compressionQuality: 0.9) else {
            completion(false, nil)
            return
        }

        // 写入文件
        do {
            try imageData.write(to: fileURL)

            // 更新索引
            updateIndexForNewFile(at: fileURL, type: .image)

            completion(true, fileURL)
        } catch {
            print("保存图片到应用失败: \(error.localizedDescription)")
            completion(false, nil)
        }
    }

    // 当照片保存到系统相册后调用此方法来记录映射关系
    func recordPhotoAssetMapping(appFileName: String, assetLocalIdentifier: String) {
        addPhotoAssetMapping(fileName: appFileName, assetIdentifier: assetLocalIdentifier)
        print("记录照片资源映射: \(appFileName) -> \(assetLocalIdentifier)")
    }

    // 保存视频到应用内
    func saveVideoToApp(from sourceURL: URL, completion: @escaping (Bool, URL?) -> Void) {
        // 创建唯一文件名
        let fileName = "video_\(Date().timeIntervalSince1970).mp4"
        let destinationURL = videosDirectory.appendingPathComponent(fileName)

        // 复制文件
        do {
            try FileManager.default.copyItem(at: sourceURL, to: destinationURL)

            // 更新索引
            updateIndexForNewFile(at: destinationURL, type: .video)

            completion(true, destinationURL)
        } catch {
            print("保存视频到应用失败: \(error.localizedDescription)")
            completion(false, nil)
                 }
     }

     // 更新新文件的索引
     private func updateIndexForNewFile(at fileURL: URL, type: MediaFileIndex.MediaFileType) {
         DispatchQueue.global(qos: .utility).async { [weak self] in
             guard let self = self else { return }

             do {
                 let attributes = try FileManager.default.attributesOfItem(atPath: fileURL.path)
                 guard let creationDate = attributes[FileAttributeKey.creationDate] as? Date,
                      let fileSize = attributes[FileAttributeKey.size] as? Int64,
                      let modifiedDate = attributes[FileAttributeKey.modificationDate] as? Date else {
                     print("❌ 无法获取新文件属性: \(fileURL.lastPathComponent)")
                     return
                 }

                 let newIndex = MediaFileIndex(
                     fileName: fileURL.lastPathComponent,
                     filePath: fileURL.path,
                     fileType: type,
                     creationDate: creationDate,
                     fileSize: fileSize,
                     lastModified: modifiedDate
                 )

                 DispatchQueue.main.async {
                     // 🔧 修复：按创建时间正确插入，而不是简单插入到开头
                     var insertIndex = 0

                     // 查找正确的插入位置（按创建时间降序排列）
                     for (index, existingItem) in self.mediaFileIndex.enumerated() {
                         if newIndex.creationDate > existingItem.creationDate {
                             insertIndex = index
                             break
                         }
                         insertIndex = index + 1
                     }

                     // 插入到正确位置
                     if insertIndex >= self.mediaFileIndex.count {
                         self.mediaFileIndex.append(newIndex)
                     } else {
                         self.mediaFileIndex.insert(newIndex, at: insertIndex)
                     }

                     // 保存更新后的索引
                     DispatchQueue.global(qos: .utility).async {
                         self.saveMediaFileIndex()
                     }

                     print("✅ 已更新索引，新增文件: \(fileURL.lastPathComponent)，插入位置: \(insertIndex)")
                 }
             } catch {
                 print("❌ 更新索引失败: \(error.localizedDescription)")
             }
         }
     }

     // MARK: - 缩略图生成

    // 生成视频缩略图，带缓存功能
    func generateVideoThumbnail(for url: URL) -> UIImage? {
        // 检查缓存
        let cacheKey = url.path as NSString
        if let cachedThumbnail = thumbnailCache.object(forKey: cacheKey) {
            return cachedThumbnail
        }

        // 检查文件是否存在
        guard FileManager.default.fileExists(atPath: url.path) else {
            print("视频文件不存在: \(url.path)")
            return nil
        }

        // 创建异步操作组并设置处理优先级
        let asset = AVAsset(url: url)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true // 处理视频方向

        // 线程安全地检查和设置视频尺寸缓存
        var cachedDimensions: CGSize?
        videoCacheQueue.sync {
            cachedDimensions = videoDimensionsCache[url]
        }

        if cachedDimensions == nil {
            // 获取原始视频尺寸
            let tracks = asset.tracks(withMediaType: .video)
            if let videoTrack = tracks.first {
                let size = videoTrack.naturalSize.applying(videoTrack.preferredTransform)
                let dimensions = CGSize(width: abs(size.width), height: abs(size.height))

                // 线程安全地保存到缓存
                videoCacheQueue.async(flags: .barrier) {
                    self.videoDimensionsCache[url] = dimensions
                }
                cachedDimensions = dimensions
            }
        }

        // 设置适当尺寸，保持原始比例
        if let dimensions = cachedDimensions {
            let aspectRatio = dimensions.width / dimensions.height
            let targetHeight: CGFloat = 300 // 限制高度
            let targetWidth = targetHeight * aspectRatio
            imageGenerator.maximumSize = CGSize(width: targetWidth, height: targetHeight)
        } else {
            // 默认处理
            imageGenerator.maximumSize = CGSize(width: 400, height: 300)
        }
        imageGenerator.requestedTimeToleranceBefore = CMTime.zero
        imageGenerator.requestedTimeToleranceAfter = CMTime.zero

        // 设置生成缩略图的时间（0.1秒而不是开始，避免黑屏）
        let time = CMTime(seconds: 0.1, preferredTimescale: 600)

        do {
            let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
            let thumbnail = UIImage(cgImage: cgImage)

            // 存入缓存
            thumbnailCache.setObject(thumbnail, forKey: cacheKey)

            return thumbnail
        } catch {
            print("生成视频缩略图失败: \(error.localizedDescription)")
            return UIImage(systemName: "video")
        }
    }

    // 获取缓存的视频缩略图，如果缓存不存在则异步生成
    func getCachedVideoThumbnail(for url: URL) -> UIImage? {
        // 检查缓存
        let cacheKey = url.path as NSString
        if let cachedThumbnail = thumbnailCache.object(forKey: cacheKey) {
            return cachedThumbnail
        }

        // 如果缓存中没有，返回占位符并异步生成缩略图
        asyncGenerateVideoThumbnail(for: url)

        // 返回默认视频图标作为占位符
        return UIImage(systemName: "video.fill")
    }

    // 异步生成视频缩略图
    private func asyncGenerateVideoThumbnail(for url: URL) {
        let cacheKey = url.path as NSString

        // 检查是否已在生成中，避免重复操作
        guard thumbnailCache.object(forKey: cacheKey) == nil else { return }

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            if let thumbnail = self.generateVideoThumbnail(for: url) {
                DispatchQueue.main.async {
                    // 生成完成后，通知UI更新（可以通过NotificationCenter实现）
                    NotificationCenter.default.post(
                        name: NSNotification.Name("VideoThumbnailGenerated"),
                        object: nil,
                        userInfo: ["url": url, "thumbnail": thumbnail]
                    )
                }
            }
        }
    }

    // MARK: - 媒体加载

    // 批量加载应用内媒体 - 用于优化加载性能
    func loadBatchedAppMedia(limit: Int) -> [MediaType] {
        var mediaItems: [MediaType] = []
        let fileManager = FileManager.default

        // 加载图片
        if let imageFiles = try? fileManager.contentsOfDirectory(at: imagesDirectory, includingPropertiesForKeys: nil) {
            // 限制加载数量
            for imageURL in imageFiles.prefix(min(limit/2, imageFiles.count)) {
                if let image = UIImage(contentsOfFile: imageURL.path) {
                    mediaItems.append(.photo(image))
                }
            }
        }

        // 加载视频
        if let videoFiles = try? fileManager.contentsOfDirectory(at: videosDirectory, includingPropertiesForKeys: nil) {
            // 限制加载数量
            for videoURL in videoFiles.prefix(min(limit/2, videoFiles.count)) {
                // 预先生成缩略图并缓存
                _ = generateVideoThumbnail(for: videoURL)
                mediaItems.append(.video(videoURL))
            }
        }

        // 按创建时间排序（最新的在前面）
        mediaItems.sort { (item1, item2) -> Bool in
            let url1: URL?
            let url2: URL?

            switch item1 {
            case .photo(_): url1 = nil
            case .video(let videoURL): url1 = videoURL
            }

            switch item2 {
            case .photo(_): url2 = nil
            case .video(let videoURL): url2 = videoURL
            }

            // 比较创建时间
            if let url1 = url1, let url2 = url2,
               let attr1 = try? FileManager.default.attributesOfItem(atPath: url1.path),
               let attr2 = try? FileManager.default.attributesOfItem(atPath: url2.path),
               let date1 = attr1[.creationDate] as? Date,
               let date2 = attr2[.creationDate] as? Date {
                return date1 > date2
            }

            return false
        }

        return mediaItems
    }

    // 快速加载媒体文件列表（优化版本，使用索引）
    func loadMediaFilesAsync(completion: @escaping ([MediaType]) -> Void) {
        // 🔧 修复：首先检查目录是否真的有文件
        let actualImageCount = (try? FileManager.default.contentsOfDirectory(at: imagesDirectory, includingPropertiesForKeys: nil))?.count ?? 0
        let actualVideoCount = (try? FileManager.default.contentsOfDirectory(at: videosDirectory, includingPropertiesForKeys: nil))?.count ?? 0
        let actualTotalFiles = actualImageCount + actualVideoCount

        print("📊 实际文件数量检查: 图片=\(actualImageCount), 视频=\(actualVideoCount), 总计=\(actualTotalFiles)")

        // 🔧 修复：如果目录确实为空，直接返回空数组，不要显示索引进度
        if actualTotalFiles == 0 {
            print("📋 目录为空，直接返回空媒体列表")
            DispatchQueue.main.async {
                completion([])
            }
            return
        }

        // 如果索引为空但有文件，先尝试构建索引
        if mediaFileIndex.isEmpty {
            print("📋 媒体索引为空但有 \(actualTotalFiles) 个文件，重新构建索引...")
            DispatchQueue.global(qos: .utility).async {
                self.rebuildMediaFileIndex {
                    // 重建完成后立即调用此方法
                    DispatchQueue.main.async {
                        self.loadMediaFilesAsync(completion: completion)
                    }
                }
            }
            return
        }

        // 使用索引快速加载
        print("⚡ 使用索引快速加载媒体文件，共 \(mediaFileIndex.count) 个文件")

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 验证索引中的文件是否仍然存在，移除不存在的文件
            print("🔍 开始验证索引中的 \(self.mediaFileIndex.count) 个文件...")
            print("📁 当前目录路径:")
            print("   Images: \(self.imagesDirectory.path)")
            print("   Videos: \(self.videosDirectory.path)")

            var updatedIndexes: [MediaFileIndex] = []

            for index in self.mediaFileIndex {
                let exists = FileManager.default.fileExists(atPath: index.filePath)
                if !exists {
                    print("⚠️ 文件不存在: \(index.fileName)")
                    print("   📍 索引路径: \(index.filePath)")

                    // 尝试在正确的目录中查找文件
                    let fileName = index.fileName
                    let expectedImagePath = self.imagesDirectory.appendingPathComponent(fileName).path
                    let expectedVideoPath = self.videosDirectory.appendingPathComponent(fileName).path

                    let imageExists = FileManager.default.fileExists(atPath: expectedImagePath)
                    let videoExists = FileManager.default.fileExists(atPath: expectedVideoPath)

                    print("   🔍 在Images目录查找: \(imageExists) -> \(expectedImagePath)")
                    print("   🔍 在Videos目录查找: \(videoExists) -> \(expectedVideoPath)")

                    // 如果在正确目录中找到了文件，更新索引路径
                    if imageExists && index.fileType == .image {
                        print("   🔧 修复图片路径: \(fileName)")
                        var updatedIndex = index
                        updatedIndex = MediaFileIndex(
                            fileName: index.fileName,
                            filePath: expectedImagePath,
                            fileType: index.fileType,
                            creationDate: index.creationDate,
                            fileSize: index.fileSize,
                            lastModified: index.lastModified
                        )
                        updatedIndexes.append(updatedIndex)
                    } else if videoExists && index.fileType == .video {
                        print("   🔧 修复视频路径: \(fileName)")
                        var updatedIndex = index
                        updatedIndex = MediaFileIndex(
                            fileName: index.fileName,
                            filePath: expectedVideoPath,
                            fileType: index.fileType,
                            creationDate: index.creationDate,
                            fileSize: index.fileSize,
                            lastModified: index.lastModified
                        )
                        updatedIndexes.append(updatedIndex)
                    } else {
                        print("   ❌ 文件真的不存在: \(fileName)")
                        // 不添加到更新列表中，相当于删除
                    }
                } else {
                    print("✅ 文件存在: \(index.fileName)")
                    updatedIndexes.append(index)
                }
            }

            let validIndexes = updatedIndexes
            print("📊 验证结果: \(validIndexes.count)/\(self.mediaFileIndex.count) 个文件有效")

            // 如果有文件被删除或路径被修复，更新索引
            let removedCount = self.mediaFileIndex.count - validIndexes.count
            let pathFixedCount = updatedIndexes.count - validIndexes.count + removedCount

            if validIndexes.count != self.mediaFileIndex.count || pathFixedCount > 0 {
                if removedCount > 0 {
                    print("🔄 发现 \(removedCount) 个文件已被删除")
                }
                if pathFixedCount > 0 {
                    print("🔧 修复了 \(pathFixedCount) 个文件路径")
                }
                print("📝 更新索引：\(self.mediaFileIndex.count) -> \(validIndexes.count)")

                DispatchQueue.main.async {
                    self.mediaFileIndex = validIndexes
                    self.saveMediaFileIndex()
                }
            } else {
                print("✅ 索引验证通过，无需更新")
            }

            // 如果验证后还有有效文件，正常加载
            if !validIndexes.isEmpty {
                // 取前15个文件进行快速加载
                let filesToLoad = Array(validIndexes.prefix(15))
                print("📋 即将快速加载前 \(filesToLoad.count) 个媒体文件")

                // 使用索引信息快速加载媒体内容
                self.loadMediaFromIndex(indexes: filesToLoad) { loadedItems in
                    DispatchQueue.main.async {
                        print("✅ 快速加载完成，共 \(loadedItems.count) 个媒体项")
                        completion(loadedItems)

                        // 在后台预加载剩余内容
                        if validIndexes.count > 15 {
                            let remainingIndexes = Array(validIndexes.dropFirst(15))
                            DispatchQueue.global(qos: .utility).async {
                                self.preloadMediaFromIndex(indexes: remainingIndexes)
                            }
                        }
                    }
                }
            } else {
                // 只有在确实没有有效文件时才重建索引
                print("⚠️ 验证后没有有效文件，需要重建索引")
                DispatchQueue.global(qos: .utility).async {
                    self.rebuildMediaFileIndex {
                        // 重建完成后重新调用此方法
                        DispatchQueue.main.async {
                            self.loadMediaFilesAsync(completion: completion)
                        }
                    }
                }
            }
        }
    }

    // 从索引加载媒体内容（优化版本）
    private func loadMediaFromIndex(indexes: [MediaFileIndex], completion: @escaping ([MediaType]) -> Void) {
        var mediaItems: [MediaType] = []
        let group = DispatchGroup()
        let concurrentQueue = DispatchQueue(label: "media.index.loading", qos: .userInitiated, attributes: .concurrent)

        print("📋 使用索引加载 \(indexes.count) 个媒体文件")

        for index in indexes {
            group.enter()
            concurrentQueue.async { [weak self] in
                guard let self = self else {
                    group.leave()
                    return
                }

                switch index.fileType {
                case .image:
                    // 检查图片缓存
                    let cacheKey = index.filePath as NSString
                    if let cachedImage = self.imageCache.object(forKey: cacheKey) {
                        DispatchQueue.main.async {
                            mediaItems.append(.photo(cachedImage))
                            group.leave()
                        }
                        return
                    }

                    // 加载图片
                    if let image = UIImage(contentsOfFile: index.filePath) {
                        // 缓存图片
                        self.imageCache.setObject(image, forKey: cacheKey)

                        DispatchQueue.main.async {
                            mediaItems.append(.photo(image))
                            group.leave()
                        }
                    } else {
                        group.leave()
                    }

                case .video:
                    // 视频直接使用URL，并确保缩略图生成
                    let videoURL = URL(fileURLWithPath: index.filePath)

                    // 异步生成缩略图
                    DispatchQueue.global(qos: .utility).async {
                        _ = self.generateVideoThumbnail(for: videoURL)
                    }

                    DispatchQueue.main.async {
                        mediaItems.append(.video(videoURL))
                        group.leave()
                    }
                }
            }
        }

        // 等待所有加载完成
        group.notify(queue: .main) {
            // 按索引顺序排序（索引已经按创建时间排序）
            let sortedItems = mediaItems.sorted { item1, item2 in
                let path1 = self.getMediaPath(for: item1)
                let path2 = self.getMediaPath(for: item2)

                let index1 = indexes.firstIndex { $0.filePath == path1 } ?? 0
                let index2 = indexes.firstIndex { $0.filePath == path2 } ?? 0

                return index1 < index2
            }

            print("✅ 索引加载完成，共 \(sortedItems.count) 个媒体项")
            completion(sortedItems)
        }
    }

    // 预加载媒体内容
    private func preloadMediaFromIndex(indexes: [MediaFileIndex]) {
        print("🔄 开始预加载剩余 \(indexes.count) 个媒体文件")

        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }

            for index in indexes {
                switch index.fileType {
                case .image:
                    // 预加载图片到缓存
                    let cacheKey = index.filePath as NSString
                    if self.imageCache.object(forKey: cacheKey) == nil,
                       let image = UIImage(contentsOfFile: index.filePath) {
                        self.imageCache.setObject(image, forKey: cacheKey)
                    }

                case .video:
                    // 预生成视频缩略图
                    let videoURL = URL(fileURLWithPath: index.filePath)
                    _ = self.generateVideoThumbnail(for: videoURL)
                }
            }

            print("✅ 预加载完成")
        }
    }

         // 获取媒体项的文件路径
     private func getMediaPath(for mediaType: MediaType) -> String {
         switch mediaType {
         case .photo(let image):
             // 对于照片，通过匹配找到对应的文件路径
             return findMatchingImagePath(for: image) ?? ""
         case .video(let url):
             return url.path
         }
     }

     // 查找匹配的图片路径
     private func findMatchingImagePath(for image: UIImage) -> String? {
         // 遍历图片索引，找到最可能匹配的文件
         let imageIndexes = mediaFileIndex.filter { $0.fileType == .image }

         for index in imageIndexes.prefix(5) { // 只检查前5个图片
             if let diskImage = UIImage(contentsOfFile: index.filePath),
                diskImage.size == image.size {
                 return index.filePath
             }
         }

         return nil
     }

    // 预加载剩余媒体 (保留用于向后兼容)
    private func preloadRemainingMedia(imageURLs: [URL], videoURLs: [URL]) {
        // 异步预加载剩余图片
        for imageURL in imageURLs {
            let cacheKey = imageURL.path as NSString
            if imageCache.object(forKey: cacheKey) == nil,
               let image = UIImage(contentsOfFile: imageURL.path) {
                imageCache.setObject(image, forKey: cacheKey)
            }
        }

        // 异步预生成视频缩略图
        for videoURL in videoURLs {
            _ = generateVideoThumbnail(for: videoURL)
        }
    }

    // 获取媒体项的文件日期
    private func getFileDate(for mediaType: MediaType) -> Date {
        switch mediaType {
        case .photo(let image):
            // 对于照片，尝试通过文件匹配获取真实创建时间
            return findPhotoCreationDate(for: image) ?? Date()
        case .video(let url):
            if let attributes = try? FileManager.default.attributesOfItem(atPath: url.path),
               let date = attributes[.creationDate] as? Date {
                return date
            }
            return Date()
        }
    }

    // 查找照片的创建时间
    private func findPhotoCreationDate(for image: UIImage) -> Date? {
        // 尝试通过图片特征匹配文件
        guard let cgImage = image.cgImage else { return nil }
        let imageWidth = cgImage.width
        let imageHeight = cgImage.height

        // 生成图片的数据哈希用于更精确的匹配
        guard let imageData = image.jpegData(compressionQuality: 0.9) else { return nil }
        let imageDataSize = imageData.count

        do {
            let imageFiles = try FileManager.default.contentsOfDirectory(
                at: imagesDirectory,
                includingPropertiesForKeys: [.creationDateKey, .fileSizeKey],
                options: []
            )

            // 按修改时间降序排列，优先检查最新的文件
            let sortedFiles = imageFiles.sorted { url1, url2 in
                let date1 = (try? FileManager.default.attributesOfItem(atPath: url1.path))?[.creationDate] as? Date ?? Date.distantPast
                let date2 = (try? FileManager.default.attributesOfItem(atPath: url2.path))?[.creationDate] as? Date ?? Date.distantPast
                return date1 > date2
            }

            // 在最近的10个文件中查找匹配
            for fileURL in sortedFiles.prefix(10) {
                if let fileImage = UIImage(contentsOfFile: fileURL.path),
                   let fileCGImage = fileImage.cgImage,
                   fileCGImage.width == imageWidth && fileCGImage.height == imageHeight {

                    // 尺寸匹配后，进一步检查文件大小
                    if let fileData = fileImage.jpegData(compressionQuality: 0.9) {
                        let sizeDifference = abs(fileData.count - imageDataSize)
                        let sizeTolerance = max(imageDataSize / 10, 1000) // 允许10%差异或至少1KB

                        if sizeDifference <= sizeTolerance {
                            // 大小也匹配，获取文件创建时间
                            if let attributes = try? FileManager.default.attributesOfItem(atPath: fileURL.path),
                               let creationDate = attributes[.creationDate] as? Date {
                                print("[照片匹配] ✅ 找到匹配文件: \(fileURL.lastPathComponent), 时间: \(creationDate), 尺寸差异: \(sizeDifference) bytes")
                                return creationDate
                            }
                        }
                    }
                }
            }
        } catch {
            print("[照片匹配] 查找失败: \(error.localizedDescription)")
        }

        return nil
    }

    // 保持原方法的兼容性
    func loadAllAppMedia() -> [MediaType] {
        // 这个方法现在只返回空数组，实际加载通过异步方法进行
        return []
    }

    // 删除指定媒体
    func deleteMedia(_ media: MediaType, completion: @escaping (Bool) -> Void) {
        print("🗑️ 开始删除媒体项")

        // 1. 先从应用内存储删除
        var appDeletionSuccess = false
        var assetIdentifier: String? = nil

        // 2. 根据媒体类型进行不同的删除处理
        switch media {
        case .photo(let image):
            print("📸 删除照片，开始匹配...")

            // 改进的照片匹配逻辑
            appDeletionSuccess = deletePhotoFromAppStorage(image: image, assetIdentifier: &assetIdentifier)

            // 如果找到了对应的系统相册资源标识符，则从系统相册中删除
            if let identifier = assetIdentifier {
                print("找到照片对应的系统资源标识符: \(identifier)")
                deletePhotoAssetWithIdentifier(identifier) { (systemDeletionSuccess) in
                    let finalSuccess = appDeletionSuccess || systemDeletionSuccess
                    completion(finalSuccess)
                }
            } else {
                // 如果没有找到标识符，尝试使用旧方法从系统相册删除
                print("未找到照片对应的系统资源标识符，尝试使用内容匹配方法")
                deleteFromPhotoLibrary(image: image) { (systemDeletionSuccess) in
                    let finalSuccess = appDeletionSuccess || systemDeletionSuccess
                    completion(finalSuccess)
                }
            }

        case .video(let videoURL):
            print("🎥 删除视频: \(videoURL.lastPathComponent)")

            // 从应用内删除视频
            do {
                try FileManager.default.removeItem(at: videoURL)
                appDeletionSuccess = true
                print("✅ 成功删除应用内视频文件")

                // 从索引中移除
                removeFromIndex(filePath: videoURL.path)

            } catch {
                print("❌ 删除应用内视频失败: \(error.localizedDescription)")
            }

            // 从系统相册删除对应的视频
            deleteFromPhotoLibrary(videoURL: videoURL) { (systemDeletionSuccess) in
                let finalSuccess = appDeletionSuccess || systemDeletionSuccess
                completion(finalSuccess)
            }
        }
    }

    // 新增：从应用内存储删除照片的改进方法
    private func deletePhotoFromAppStorage(image: UIImage, assetIdentifier: inout String?) -> Bool {
        guard let cgImage = image.cgImage else {
            print("❌ 无法获取CGImage")
            return false
        }

        let targetWidth = cgImage.width
        let targetHeight = cgImage.height
        let targetDataSize = image.jpegData(compressionQuality: 0.9)?.count ?? 0

        print("🔍 精确匹配照片文件，目标特征: \(targetWidth)x\(targetHeight), 数据大小: \(targetDataSize) bytes")

        guard let imageFiles = try? FileManager.default.contentsOfDirectory(at: imagesDirectory, includingPropertiesForKeys: [.fileSizeKey, .creationDateKey]) else {
            print("❌ 无法读取图片目录")
            return false
        }

        // 按修改时间排序，优先检查最新的文件
        let sortedFiles = imageFiles.sorted { url1, url2 in
            let date1 = (try? FileManager.default.attributesOfItem(atPath: url1.path))?[.creationDate] as? Date ?? Date.distantPast
            let date2 = (try? FileManager.default.attributesOfItem(atPath: url2.path))?[.creationDate] as? Date ?? Date.distantPast
            return date1 > date2
        }

        // 首先尝试精确匹配
        var bestMatch: (url: URL, score: Double)?

        for imageURL in sortedFiles {
            guard let fileImage = UIImage(contentsOfFile: imageURL.path),
                  let fileCGImage = fileImage.cgImage else {
                continue
            }

            let fileWidth = fileCGImage.width
            let fileHeight = fileCGImage.height
            let fileDataSize = fileImage.jpegData(compressionQuality: 0.9)?.count ?? 0

            // 计算匹配度分数
            var matchScore: Double = 0

            // 尺寸精确匹配 (40分)
            if fileWidth == targetWidth && fileHeight == targetHeight {
                matchScore += 40
                print("✅ 文件 \(imageURL.lastPathComponent) 尺寸精确匹配: \(fileWidth)x\(fileHeight)")
            } else {
                // 尺寸相似度匹配
                let widthRatio = Double(min(fileWidth, targetWidth)) / Double(max(fileWidth, targetWidth))
                let heightRatio = Double(min(fileHeight, targetHeight)) / Double(max(fileHeight, targetHeight))
                matchScore += (widthRatio + heightRatio) * 10
            }

            // 数据大小匹配 (30分)
            if targetDataSize > 0 && fileDataSize > 0 {
                let sizeDiff = abs(fileDataSize - targetDataSize)
                let tolerance = max(targetDataSize / 20, 500) // 允许5%差异或至少500bytes

                if sizeDiff == 0 {
                    matchScore += 30 // 完全相同
                    print("✅ 文件 \(imageURL.lastPathComponent) 数据大小完全匹配: \(fileDataSize) bytes")
                } else if sizeDiff <= tolerance {
                    let similarity = 1.0 - (Double(sizeDiff) / Double(targetDataSize))
                    matchScore += similarity * 30
                    print("📊 文件 \(imageURL.lastPathComponent) 数据大小相近: \(fileDataSize) vs \(targetDataSize), 差异: \(sizeDiff) bytes")
                }
            }

            // 像素数匹配 (20分)
            let targetPixels = targetWidth * targetHeight
            let filePixels = fileWidth * fileHeight
            if targetPixels == filePixels {
                matchScore += 20
                print("✅ 文件 \(imageURL.lastPathComponent) 像素数匹配: \(filePixels)")
            }

            // 内容哈希匹配 (10分)
            if let targetHash = generateImageContentHash(image),
               let fileHash = generateImageContentHash(fileImage),
               targetHash == fileHash {
                matchScore += 10
                print("✅ 文件 \(imageURL.lastPathComponent) 内容哈希匹配")
            }

            print("📊 文件 \(imageURL.lastPathComponent) 总匹配分数: \(matchScore)")

            // 更新最佳匹配
            if matchScore > 80 && (bestMatch == nil || matchScore > bestMatch!.score) {
                bestMatch = (imageURL, matchScore)
                print("🎯 更新最佳匹配: \(imageURL.lastPathComponent), 分数: \(matchScore)")
            }
        }

        // 如果找到了足够好的匹配，执行删除
        if let match = bestMatch {
            let imageURL = match.url
            let fileName = imageURL.lastPathComponent

            print("✅ 确认删除最佳匹配文件: \(fileName), 匹配度: \(match.score)")

            // 查找对应的系统相册资源标识符
            assetIdentifier = getAssetIdentifier(for: fileName)

            do {
                try FileManager.default.removeItem(at: imageURL)
                print("✅ 成功删除应用内图片文件: \(fileName)")

                // 删除资源映射记录
                removePhotoAssetMapping(for: fileName)

                // 从索引中移除
                removeFromIndex(filePath: imageURL.path)

                return true
            } catch {
                print("❌ 删除应用内图片失败: \(error.localizedDescription)")
                return false
            }
        }

        print("❌ 未找到匹配度足够高的照片文件")
        return false
    }

    // 新增：生成图片内容哈希的方法（与GalleryViews中的方法保持一致）
    private func generateImageContentHash(_ image: UIImage) -> String? {
        guard let cgImage = image.cgImage else { return nil }

        // 创建一个小尺寸的图片进行哈希计算，提高性能
        let targetSize = CGSize(width: 8, height: 8)

        UIGraphicsBeginImageContextWithOptions(targetSize, true, 1.0)
        defer { UIGraphicsEndImageContext() }

        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        // 绘制缩放后的图片
        context.interpolationQuality = .none // 使用最简单的插值以保持特征
        context.draw(cgImage, in: CGRect(origin: .zero, size: targetSize))

        guard let smallCGImage = UIGraphicsGetImageFromCurrentImageContext()?.cgImage else { return nil }

        // 获取像素数据
        let width = smallCGImage.width
        let height = smallCGImage.height
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let totalBytes = height * bytesPerRow

        var pixelData = [UInt8](repeating: 0, count: totalBytes)

        guard let pixelContext = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
        ) else { return nil }

        pixelContext.draw(smallCGImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        // 计算简单的哈希值
        var hash: UInt64 = 0
        for i in stride(from: 0, to: totalBytes, by: bytesPerPixel) {
            // 只使用RGB值，忽略Alpha
            let r = UInt64(pixelData[i])
            let g = UInt64(pixelData[i + 1])
            let b = UInt64(pixelData[i + 2])

            // 创建一个简单的哈希
            hash = hash &* 31 &+ r &* 65537 &+ g &* 257 &+ b
        }

        return String(hash)
    }

    // 使用资源标识符删除照片
    private func deletePhotoAssetWithIdentifier(_ identifier: String, completion: @escaping (Bool) -> Void) {
        PHPhotoLibrary.requestAuthorization { status in
            guard status == .authorized else {
                print("无法访问系统相册，无法删除照片")
                completion(false)
                return
            }

            // 使用标识符查找资源
            let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [identifier], options: nil)

            if let asset = fetchResult.firstObject {
                // 删除找到的资产
                PHPhotoLibrary.shared().performChanges({
                    PHAssetChangeRequest.deleteAssets([asset] as NSArray)
                }, completionHandler: { success, error in
                    if success {
                        print("成功从系统相册删除照片 (使用标识符)")
                    } else if let error = error {
                        print("从系统相册删除照片出错 (使用标识符): \(error.localizedDescription)")
                    }
                    completion(success)
                })
            } else {
                print("未在系统相册找到匹配标识符的照片: \(identifier)")
                completion(false)
            }
        }
    }

    // 从索引中移除文件
    private func removeFromIndex(filePath: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if let index = self.mediaFileIndex.firstIndex(where: { $0.filePath == filePath }) {
                self.mediaFileIndex.remove(at: index)

                // 保存更新后的索引
                DispatchQueue.global(qos: .utility).async {
                    self.saveMediaFileIndex()
                }

                print("✅ 已从索引中移除文件: \(URL(fileURLWithPath: filePath).lastPathComponent)")
            }
        }
    }

    // 🔧 新增：删除后立即同步索引的方法
    func syncIndexAfterDeletion(completion: @escaping () -> Void) {
        print("🔄 删除后同步索引...")

        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }

            // 验证索引中的文件是否仍然存在，移除已删除的文件
            var validIndexes: [MediaFileIndex] = []

            for indexItem in self.mediaFileIndex {
                if FileManager.default.fileExists(atPath: indexItem.filePath) {
                    validIndexes.append(indexItem)
                } else {
                    print("🗑️ 从索引中移除已删除的文件: \(indexItem.fileName)")
                }
            }

            let removedCount = self.mediaFileIndex.count - validIndexes.count

            if removedCount > 0 {
                DispatchQueue.main.async {
                    self.mediaFileIndex = validIndexes

                    // 保存更新后的索引
                    DispatchQueue.global(qos: .utility).async {
                        self.saveMediaFileIndex()

                        DispatchQueue.main.async {
                            print("✅ 索引同步完成，移除了 \(removedCount) 个已删除文件的记录")
                            completion()
                        }
                    }
                }
            } else {
                DispatchQueue.main.async {
                    print("✅ 索引已是最新状态，无需同步")
                    completion()
                }
            }
        }
    }

    // MARK: - 系统相册删除辅助方法

    /// 从系统相册中删除照片
    /// - Parameters:
    ///   - image: 要删除的图像
    ///   - completion: 完成回调
    private func deleteFromPhotoLibrary(image: UIImage, completion: @escaping (Bool) -> Void) {
        // 获取访问权限
        PHPhotoLibrary.requestAuthorization { status in
            guard status == .authorized else {
                print("无法访问系统相册，无法删除照片")
                completion(false)
                return
            }

            // 获取所有照片资源
            let fetchOptions = PHFetchOptions()
            fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
            let assets = PHAsset.fetchAssets(with: .image, options: fetchOptions)

            // 用于存储找到的匹配资产
            var matchingAsset: PHAsset?

            // 获取要删除的图像的数据以进行比较
            guard let targetImageData = image.jpegData(compressionQuality: 1.0) else {
                print("无法获取目标图像数据进行比较")
                completion(false)
                return
            }

            // 限制搜索最近的30张照片，以提高性能（假设要删除的照片是最近拍摄的）
            let searchLimit = min(assets.count, 30)

            // 查找匹配的照片资源
            let dispatchGroup = DispatchGroup()
            let semaphore = DispatchSemaphore(value: 3) // 限制同时处理的请求数

            for i in 0..<searchLimit {
                // 如果已经找到匹配项，跳过后续搜索
                if matchingAsset != nil {
                    break
                }

                dispatchGroup.enter()
                semaphore.wait()

                let asset = assets[i]

                // 获取资产的内容
                let manager = PHImageManager.default()
                let requestOptions = PHImageRequestOptions()
                requestOptions.version = .current
                requestOptions.deliveryMode = .highQualityFormat
                requestOptions.isSynchronous = false // 异步加载，不阻塞UI
                requestOptions.isNetworkAccessAllowed = false // 不从iCloud下载

                manager.requestImageDataAndOrientation(for: asset, options: requestOptions) { (data, _, _, _) in
                    defer {
                        semaphore.signal()
                        dispatchGroup.leave()
                    }

                    // 如果已经找到匹配项，跳过比较
                    if matchingAsset != nil {
                        return
                    }

                    guard let assetData = data else { return }

                    // 对比图像数据大小，初步筛选
                    if abs(assetData.count - targetImageData.count) < targetImageData.count / 5 { // 允许20%的差异
                        // 创建图像并进行更深入的比较
                        if let assetImage = UIImage(data: assetData),
                           let assetImageData = assetImage.jpegData(compressionQuality: 1.0),
                           let similarity = self.calculateImageSimilarity(data1: targetImageData, data2: assetImageData),
                           similarity > 0.7 { // 相似度超过70%认为是同一张图片
                            matchingAsset = asset
                        }
                    }
                }
            }

            // 等待所有图像处理完成
            dispatchGroup.notify(queue: .main) {
                if let asset = matchingAsset {
                    // 删除找到的资产
                    PHPhotoLibrary.shared().performChanges({
                        PHAssetChangeRequest.deleteAssets([asset] as NSArray)
                    }, completionHandler: { success, error in
                        if success {
                            print("成功从系统相册删除照片")
                        } else if let error = error {
                            print("从系统相册删除照片出错: \(error.localizedDescription)")
                        }
                        completion(success)
                    })
                } else {
                    print("未在系统相册找到匹配的照片")
                    completion(false)
                }
            }
        }
    }

    /// 从系统相册删除视频
    /// - Parameters:
    ///   - videoURL: 视频URL
    ///   - completion: 完成回调
    private func deleteFromPhotoLibrary(videoURL: URL, completion: @escaping (Bool) -> Void) {
        // 获取访问权限
        PHPhotoLibrary.requestAuthorization { status in
            guard status == .authorized else {
                print("无法访问系统相册，无法删除视频")
                completion(false)
                return
            }

            // 获取应用内视频的创建时间用于匹配
            let fileAttributes = try? FileManager.default.attributesOfItem(atPath: videoURL.path)
            let videoCreationDate = fileAttributes?[.creationDate] as? Date

            guard let creationDate = videoCreationDate else {
                print("无法获取视频创建时间，跳过系统相册删除")
                completion(false)
                return
            }

            // 创建时间范围（前后30秒）
            let timeRange = 30.0
            let startDate = creationDate.addingTimeInterval(-timeRange)
            let endDate = creationDate.addingTimeInterval(timeRange)

            // 使用安全的时间范围查询
            let fetchOptions = PHFetchOptions()
            fetchOptions.sortDescriptors = [NSSortDescriptor(key: "creationDate", ascending: false)]
            fetchOptions.predicate = NSPredicate(format: "creationDate >= %@ AND creationDate <= %@", startDate as NSDate, endDate as NSDate)

            let assets = PHAsset.fetchAssets(with: .video, options: fetchOptions)

            // 如果找到了时间范围内的视频
            if assets.count > 0 {
                // 删除第一个匹配的视频（最有可能是目标视频）
                let assetToDelete = assets.firstObject!

                PHPhotoLibrary.shared().performChanges({
                    PHAssetChangeRequest.deleteAssets([assetToDelete] as NSArray)
                }, completionHandler: { success, error in
                    if success {
                        print("成功从系统相册删除视频（通过时间匹配）")
                    } else if let error = error {
                        print("从系统相册删除视频出错: \(error.localizedDescription)")
                    }
                    completion(success)
                })
            } else {
                print("未在系统相册找到时间匹配的视频")
                completion(false)
            }
        }
    }

    /// 计算两个图像数据的相似度
    /// - Parameters:
    ///   - data1: 第一个图像数据
    ///   - data2: 第二个图像数据
    /// - Returns: 相似度（0-1，1表示完全相同）
    private func calculateImageSimilarity(data1: Data, data2: Data) -> Float? {
        guard let image1 = UIImage(data: data1)?.cgImage,
              let image2 = UIImage(data: data2)?.cgImage else {
            return nil
        }

        // 将两张图片缩放到相同的较小尺寸进行比较
        let size = CGSize(width: 32, height: 32)
        UIGraphicsBeginImageContextWithOptions(size, true, 1)

        // 绘制第一张图片
        let context1 = UIGraphicsGetCurrentContext()
        context1?.interpolationQuality = .low
        context1?.draw(image1, in: CGRect(origin: .zero, size: size))
        let smallImage1 = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        // 绘制第二张图片
        UIGraphicsBeginImageContextWithOptions(size, true, 1)
        let context2 = UIGraphicsGetCurrentContext()
        context2?.interpolationQuality = .low
        context2?.draw(image2, in: CGRect(origin: .zero, size: size))
        let smallImage2 = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        // 转换为灰度像素数据
        guard let pixels1 = smallImage1?.pixelData(),
              let pixels2 = smallImage2?.pixelData() else {
            return nil
        }

        // 计算像素差异
        var matchingPixels = 0
        let pixelCount = min(pixels1.count, pixels2.count)

        for i in 0..<pixelCount {
            // 允许一些色值差异（8位色值中允许10的差异）
            if abs(Int(pixels1[i]) - Int(pixels2[i])) < 10 {
                matchingPixels += 1
            }
        }

        return Float(matchingPixels) / Float(pixelCount)
    }
}

// MARK: - UIImage 扩展
extension UIImage {
    /// 获取图像的像素数据
    /// - Returns: 像素数据数组
    func pixelData() -> [UInt8]? {
        let size = self.size
        let dataSize = size.width * size.height * 4
        var pixelData = [UInt8](repeating: 0, count: Int(dataSize))
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let context = CGContext(data: &pixelData,
                                width: Int(size.width),
                                height: Int(size.height),
                                bitsPerComponent: 8,
                                bytesPerRow: 4 * Int(size.width),
                                space: colorSpace,
                                bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue)

        guard let cgImage = self.cgImage else { return nil }
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: size.width, height: size.height))

        return pixelData
    }
}

// MARK: - 目录监控类
class DirectoryMonitor {
    private let urls: [URL]
    private let callback: () -> Void
    private var dispatchSource: DispatchSourceFileSystemObject?
    private var fileDescriptor: CInt = -1

    init(urls: [URL], callback: @escaping () -> Void) {
        self.urls = urls
        self.callback = callback
    }

    deinit {
        stop()
    }

    func start() {
        guard let parentURL = urls.first?.deletingLastPathComponent() else { return }

        fileDescriptor = open(parentURL.path, O_EVTONLY)
        guard fileDescriptor != -1 else {
            print("❌ 无法监控目录: \(parentURL.path)")
            return
        }

        dispatchSource = DispatchSource.makeFileSystemObjectSource(
            fileDescriptor: fileDescriptor,
            eventMask: .write,
            queue: DispatchQueue.global(qos: .utility)
        )

        dispatchSource?.setEventHandler { [weak self] in
            // 添加延迟以避免频繁触发
            DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 1.0) {
                self?.callback()
            }
        }

        dispatchSource?.setCancelHandler { [weak self] in
            if let fd = self?.fileDescriptor, fd != -1 {
                close(fd)
                self?.fileDescriptor = -1
            }
        }

        dispatchSource?.resume()
        print("✅ 开始监控媒体目录变化")
    }

    func stop() {
        dispatchSource?.cancel()
        dispatchSource = nil

        if fileDescriptor != -1 {
            close(fileDescriptor)
            fileDescriptor = -1
        }
        print("⏹️ 停止监控媒体目录")
    }
}
