//
//  AudioManager.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/16.
//

import Foundation
import AVFoundation
import TrueFilm

class AudioManager {
    // Singleton instance
    static let shared = AudioManager()

    // Audio players
    private var flashChargePlayer: AVAudioPlayer?
    private var flashOffPlayer: AVAudioPlayer?
    private var clickPlayer: AVAudioPlayer?
    private var switchPlayer: AVAudioPlayer?
    private var shutterPlayer: AVAudioPlayer?
    
    // 按钮音效开关
    private var isSoundEnabled: Bool = true

    // Private initializer for singleton
    private init() {
        // 读取用户设置
        isSoundEnabled = UserDefaults.standard.bool(forKey: "buttonSoundsEnabled")
        
        // 注册通知以监听按钮音效设置变更
        NotificationCenter.default.addObserver(self, 
                                               selector: #selector(handleButtonSoundsChanged), 
                                               name: NSNotification.Name("ButtonSoundsChanged"), 
                                               object: nil)
        
        // Load sound effects
        loadSoundEffects()
    }
    
    // 处理按钮音效设置变更
    @objc private func handleButtonSoundsChanged() {
        isSoundEnabled = UserDefaults.standard.bool(forKey: "buttonSoundsEnabled")
        print("[AudioManager] 按钮音效设置已更新: \(isSoundEnabled ? "开启" : "关闭")")
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // Load all sound effects
    private func loadSoundEffects() {
        // Load flash charge sound
        if let soundURL = Bundle.main.url(forResource: "flashcharge", withExtension: "MP3") {
            do {
                flashChargePlayer = try AVAudioPlayer(contentsOf: soundURL)
                flashChargePlayer?.prepareToPlay()
            } catch {
                print("Error loading flash charge sound: \(error.localizedDescription)")
            }
        } else {
            print("Could not find flashcharge.MP3")
        }

        // Load flash off sound
        if let soundURL = Bundle.main.url(forResource: "flashlightoff", withExtension: "MP3") {
            do {
                flashOffPlayer = try AVAudioPlayer(contentsOf: soundURL)
                flashOffPlayer?.prepareToPlay()
            } catch {
                print("Error loading flash off sound: \(error.localizedDescription)")
            }
        } else {
            print("Could not find flashlightoff.MP3")
        }

        // Load click sound
        if let soundURL = Bundle.main.url(forResource: "click", withExtension: "MP3") {
            do {
                clickPlayer = try AVAudioPlayer(contentsOf: soundURL)
                clickPlayer?.prepareToPlay()
            } catch {
                print("Error loading click sound: \(error.localizedDescription)")
            }
        } else {
            print("Could not find click.MP3")
        }

        // Load switch sound
        if let soundURL = Bundle.main.url(forResource: "switch", withExtension: "MP3") {
            do {
                switchPlayer = try AVAudioPlayer(contentsOf: soundURL)
                switchPlayer?.prepareToPlay()
            } catch {
                print("Error loading switch sound: \(error.localizedDescription)")
            }
        } else {
            print("Could not find switch.MP3")
        }

        // Load shutter sound
        if let soundURL = Bundle.main.url(forResource: "shutter", withExtension: "MP3") {
            do {
                shutterPlayer = try AVAudioPlayer(contentsOf: soundURL)
                shutterPlayer?.prepareToPlay()
            } catch {
                print("Error loading shutter sound: \(error.localizedDescription)")
            }
        } else {
            print("Could not find shutter.MP3")
        }
    }

    // Play flash charge sound
    func playFlashChargeSound() {
        if isSoundEnabled && !isRecording() {
            flashChargePlayer?.currentTime = 0
            flashChargePlayer?.play()
        }
    }

    // Play flash off sound
    func playFlashOffSound() {
        if isSoundEnabled && !isRecording() {
            flashOffPlayer?.currentTime = 0
            flashOffPlayer?.play()
        }
    }

    // Play click sound
    func playClickSound() {
        if isSoundEnabled && !isRecording() {
            clickPlayer?.currentTime = 0
            clickPlayer?.play()
        }
    }

    // Play switch sound
    func playSwitchSound() {
        if isSoundEnabled && !isRecording() {
            switchPlayer?.currentTime = 0
            switchPlayer?.play()
        }
    }

    // Play shutter sound
    func playShutterSound() {
        if isSoundEnabled && !isRecording() {
            shutterPlayer?.currentTime = 0
            shutterPlayer?.play()
        }
    }
    
    // MARK: - Private Helper Methods
    
    /// 检查当前是否正在录像
    /// 通过 CameraMediaOutputManager 检查录像状态
    private func isRecording() -> Bool {
        return CameraMediaOutputManager.shared.getRecordingMode() == .recording
    }
}
