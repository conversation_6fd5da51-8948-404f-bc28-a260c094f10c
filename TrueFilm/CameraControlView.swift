import SwiftUI
import Combine
import UIKit
import AVFoundation
import Foundation
import MediaPlayer

// 音量键管理器
class VolumeButtonManager: ObservableObject {
    private var audioSession: AVAudioSession
    private var volumeObserver: NSKeyValueObservation?
    private var originalVolume: Float = 0.5
    private var onVolumeButtonPressed: (() -> Void)?

    init() {
        self.audioSession = AVAudioSession.sharedInstance()
    }

    func setupVolumeButtonCapture(onPressed: @escaping () -> Void) {
        self.onVolumeButtonPressed = onPressed

        do {
            // 配置音频会话
            try audioSession.setActive(true)
            originalVolume = audioSession.outputVolume

            // 监听音量变化
            volumeObserver = audioSession.observe(\.outputVolume, options: [.new, .old]) { [weak self] session, change in
                guard let self = self,
                      let newVolume = change.newValue,
                      let oldVolume = change.oldValue else { return }

                // 检测音量键按下（音量发生变化）
                if abs(newVolume - oldVolume) > 0.01 {
                    // 触发拍照回调
                    DispatchQueue.main.async {
                        self.onVolumeButtonPressed?()
                    }

                    // 恢复原始音量（延迟一点确保音量键事件完成）
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        self.restoreVolume()
                    }
                }
            }
        } catch {
            print("VolumeButtonManager: 音频会话设置失败: \(error)")
        }
    }

    private func restoreVolume() {
        // 这里简单地将音量设回原值
        // 注意：在实际应用中可能需要通过MPVolumeView来设置音量
    }

    func stopCapture() {
        volumeObserver?.invalidate()
        volumeObserver = nil
        onVolumeButtonPressed = nil
    }

    deinit {
        stopCapture()
    }
}

// ViewModifier to hide home indicator
struct HideHomeIndicatorViewModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(HomeIndicatorHider())
    }

    struct HomeIndicatorHider: UIViewControllerRepresentable {
        func makeUIViewController(context: Context) -> HiddenHomeIndicatorViewController {
            return HiddenHomeIndicatorViewController()
        }

        func updateUIViewController(_ uiViewController: HiddenHomeIndicatorViewController, context: Context) {
            // Forces update of home indicator
            uiViewController.setNeedsUpdateOfHomeIndicatorAutoHidden()
        }
    }
}

extension View {
    func hideHomeIndicator() -> some View {
        self.modifier(HideHomeIndicatorViewModifier())
    }
}

// 自定义UIKit视图控制器包装器
struct CustomUIViewControllerWrapper<T: UIViewController>: UIViewControllerRepresentable {
    let makeController: () -> T

    init(makeController: @escaping () -> T) {
        self.makeController = makeController
    }

    func makeUIViewController(context: UIViewControllerRepresentableContext<CustomUIViewControllerWrapper>) -> T {
        return makeController()
    }

    func updateUIViewController(_ uiViewController: T, context: UIViewControllerRepresentableContext<CustomUIViewControllerWrapper>) {}
}

// Add custom corner mask view at the top of the file after imports
struct CornerMask: View {
    var color: Color
    var cornerRadius: CGFloat = 12
    var lineWidth: CGFloat = 15  // Reduced from 40 to 15 to make corners less obtrusive
    var lineLength: CGFloat = 25  // Increased from 20 to 25

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Top-left corner
                Path { path in
                    path.move(to: CGPoint(x: cornerRadius, y: 0))
                    path.addLine(to: CGPoint(x: 0, y: 0))
                    path.addLine(to: CGPoint(x: 0, y: lineLength))
                }
                .stroke(color, lineWidth: lineWidth)
                .position(x: lineWidth/2, y: lineWidth/2)

                // Top-right corner
                Path { path in
                    path.move(to: CGPoint(x: 0, y: 0))
                    path.addLine(to: CGPoint(x: cornerRadius, y: 0))
                    path.addLine(to: CGPoint(x: cornerRadius, y: lineLength))
                }
                .stroke(color, lineWidth: lineWidth)
                .position(x: geometry.size.width - cornerRadius - lineWidth/2, y: lineWidth/2)

                // Bottom-left corner
                Path { path in
                    path.move(to: CGPoint(x: 0, y: 0))
                    path.addLine(to: CGPoint(x: 0, y: cornerRadius))
                    path.addLine(to: CGPoint(x: lineLength, y: cornerRadius))
                }
                .stroke(color, lineWidth: lineWidth)
                .position(x: lineWidth/2, y: geometry.size.height - cornerRadius - lineWidth/2)

                // Bottom-right corner
                Path { path in
                    path.move(to: CGPoint(x: cornerRadius, y: 0))
                    path.addLine(to: CGPoint(x: cornerRadius, y: cornerRadius))
                    path.addLine(to: CGPoint(x: 0, y: cornerRadius))
                }
                .stroke(color, lineWidth: lineWidth)
                .position(x: geometry.size.width - cornerRadius - lineWidth/2, y: geometry.size.height - cornerRadius - lineWidth/2)
            }
        }
    }
}

struct CameraControlView: View {
    @State private var isBlackMistEnabled = true  // Changed default to true
    @State private var blackMistThreshold: Float = 0.2 // 降低阈值到 0.2，捕获更多的高光区域
    @State private var blackMistIntensity: Float = 0.3 // 将黑柔强度设置为 0.3
    @State private var brightness: Float = 0.0 // 将亮度调整为 0.0，不降低亮度
    @State private var redTint: Float = 0.2
    @State private var greenTint: Float = 0.0
    @State private var blueTint: Float = 0.0

    // Current selected control
    @State private var selectedControl: ControlType = .mist
    @State private var mistValue: Float = 0.3  // 将黑柔强度设置为 0.3
    @State private var thresholdValue: Float = 0.2  // 高光阈值的转盘值
    @State private var brightnessValue: Float = 0.0  // 亮度的转盘值
    @State private var filmIntensityValue: Float = 0.75  // 胶片滤镜强度的转盘值
    @State private var shutterValue: Float = 1/125
    @State private var exposureCompValue: Float = -0.17  // 曝光补偿值，默认为-0.17

    // 小肩屏显示视图模型
    @StateObject private var displayViewModel = GlassDisplayViewModel()

    // 镜头相关
    @State private var currentLensType: CameraLensType = .wide  // 当前镜头类型
    @State private var availableLensTypes: [CameraLensType] = []  // 可用的镜头类型
    @State private var lensZoomValue: Float = 0.5  // 镜头焦距值，默认为中间值

    // New flash state
    // 闪光灯和录像状态现在由 CameraMediaOutputManager 管理
    @State private var flashMode: FlashMode = .off

    // 录像状态
    @State private var recordingMode: RecordingMode = .off

    // 相册状态 - 使用下面已定义的 showGallery

    @State private var showLensSlider: Bool = false  // 控制是否显示镜头切换滑块
    @State private var showSettings: Bool = false  // 控制是否显示设置页面
    @State private var showFilmSimulation: Bool = false  // 控制是否显示胶片模拟面板

    // 摄像头类型
    @State private var isMultiLensCamera: Bool = false  // 默认为单镜头相机
    // 摄像头类型

    // For haptic feedback - 使用静态方法避免内存问题
    private let hapticFeedback = UIImpactFeedbackGenerator(style: .medium)
    private let shutterFeedback = UIImpactFeedbackGenerator(style: .rigid)



    // Dial rotation state
    @State private var dialRotation: Double = 108  // Updated initial rotation to match 0.3 (30% of the way)
    @State private var isSnapping = false

    // Edge drag gesture for settings
    @State private var dragOffset: CGFloat = 0

    // Photo capture variables
    @State private var capturedMedia: [MediaType] = []
    @State private var showGallery = false

    // Default values for reset
    private let defaultMistValue: Float = 0.3  // 将黑柔强度设置为 0.3
    private let defaultShutterValue: Float = 1/125
    private let defaultExposureCompValue: Float = -0.17
    private let defaultLensZoomValue: Float = 0.5

    // 添加存储记忆值的变量
    @State private var memorizedMistValue: Float = 0.3  // 将黑柔强度设置为 0.3
    @State private var memorizedThresholdValue: Float = 0.2 // 高光阈值的记忆值
    @State private var memorizedBrightnessValue: Float = 0.0 // 亮度的记忆值
    @State private var memorizedFilmIntensityValue: Float = 0.75 // 胶片滤镜强度的记忆值
    @State private var memorizedShutterValue: Float = 1/125
    @State private var memorizedExposureCompValue: Float = -0.17
    @State private var memorizedLensZoomValue: Float = 0.5

    // Flash mode properties are now in Models.swift

    // 添加快门帘动画状态
    @State private var isShutterAnimating: Bool = false

    // UI 模式设置
    @State private var isWhiteMode: Bool = UserDefaults.standard.bool(forKey: "whiteUIMode")

    // TipKit 已移除

    // Add orientation state
    @State private var isPortrait = UIDevice.current.orientation.isPortrait

    // Lock状态 - 手动控制锁定
    @State private var isManuallyLocked: Bool = false

    // 音量键管理器
    @StateObject private var volumeButtonManager = VolumeButtonManager()

    // 拍照功能
    private func takePhoto() {
        // 触发快门帘动画
        isShutterAnimating = true

        // 使用 CameraMediaOutputManager 拍照
        CameraMediaOutputManager.shared.takePhoto()
    }

    // MARK: - 音量键拍照相关函数

    // 设置音量键监听
    private func setupVolumeKeyCapture() {
        // 使用音量键管理器设置监听
        volumeButtonManager.setupVolumeButtonCapture {
            takePhoto()
        }
    }

    // 停止音量监听
    private func stopVolumeMonitoring() {
        volumeButtonManager.stopCapture()
    }

    // 加载默认 CineStill 800t 胶片滤镜
    private func loadDefaultCineStillFilter() {
        guard let bundlePath = Bundle.main.path(forResource: "lut", ofType: "bundle"),
              let bundle = Bundle(path: bundlePath),
              let resourcePath = bundle.resourcePath else {
            print("CameraControlView: 无法找到 lut.bundle，跳过默认滤镜加载")
            return
        }

        do {
            let fileManager = FileManager.default
            let files = try fileManager.contentsOfDirectory(atPath: resourcePath)

            // 查找 CineStill 800t 滤镜文件
            let cineStillFile = files.first { fileName in
                let displayName = fileName.replacingOccurrences(of: ".png", with: "")
                return displayName.lowercased().contains("cinestill") &&
                       displayName.lowercased().contains("800t") &&
                       fileName.hasSuffix(".png")
            }

            if let lutFileName = cineStillFile {
                print("CameraControlView: 找到默认胶片滤镜文件: \(lutFileName)")

                // 应用默认胶片滤镜
                if let cameraVC = CameraView.currentCameraViewController,
                   let metalRenderer = cameraVC.getMetalRenderer() {
                    metalRenderer.loadLUT(from: lutFileName)
                    metalRenderer.setLUTIntensity(memorizedFilmIntensityValue) // 使用默认强度 0.75

                    // 更新显示视图模型
                    displayViewModel.filmIntensity = memorizedFilmIntensityValue

                    print("CameraControlView: 成功加载默认胶片滤镜 \(lutFileName)，强度: \(Int(memorizedFilmIntensityValue * 100))%")
                } else {
                    print("CameraControlView: 无法获取相机控制器或Metal渲染器")
                }
            } else {
                print("CameraControlView: 未找到 CineStill 800t 滤镜文件")
            }
        } catch {
            print("CameraControlView: 读取 lut.bundle 文件时出错: \(error)")
        }
    }

    // 打开设置页面
    private func openSettings() {
        hapticFeedback.impactOccurred(intensity: 0.5)
        showSettings = true
    }

    // 切换闪光灯
    private func toggleFlash() {
        // 使用 CameraMediaOutputManager 切换闪光灯
        CameraMediaOutputManager.shared.toggleFlash()

        // 更新本地状态以反映在UI上
        flashMode = CameraMediaOutputManager.shared.getFlashMode()
    }

    // 打开相册
    private func openAlbum() {
        hapticFeedback.impactOccurred(intensity: 0.5)
        showGallery = true
    }

    // 切换录像
    private func toggleRecording() {
        // 使用 CameraMediaOutputManager 切换录像
        CameraMediaOutputManager.shared.toggleRecording()

        // 更新本地状态以反映在UI上
        recordingMode = CameraMediaOutputManager.shared.getRecordingMode()
    }

    // 切换Lock状态
    private func toggleLock() {
        hapticFeedback.impactOccurred(intensity: 0.5)
        // 播放点击音效
        AudioManager.shared.playClickSound()
        isManuallyLocked.toggle()
    }

    // 记忆当前值的函数
    private func memorizeCurrentValue() {
        hapticFeedback.impactOccurred(intensity: 0.8)

        switch selectedControl {
        case .mist:
            memorizedMistValue = mistValue
        case .threshold:
            memorizedThresholdValue = thresholdValue
        case .brightness:
            memorizedBrightnessValue = brightnessValue
        case .film:
            memorizedFilmIntensityValue = filmIntensityValue
        case .none:
            memorizedMistValue = mistValue
        }

        // 显示一个简单的反馈，表示已记忆值
        // TODO: 可以添加一个短暂的视觉提示
    }

    // 恢复记忆的值
    private func restoreMemorizedValue() {
        hapticFeedback.impactOccurred(intensity: 1.0)

        // 保存当前旋转的整圈数
        let fullRotations = (dialRotation / 360).rounded() * 360

        switch selectedControl {
        case .mist:
            mistValue = memorizedMistValue
            blackMistIntensity = mistValue
            isBlackMistEnabled = mistValue > 0
            // Calculate rotation based on memorized value
            let normalizedRotation = Double(mistValue) * 360.0
            dialRotation = normalizedRotation + fullRotations
        case .threshold:
            thresholdValue = memorizedThresholdValue
            blackMistThreshold = thresholdValue
            // 计算旋转角度
            let normalizedRotation = Double(thresholdValue) * 360.0
            dialRotation = normalizedRotation + fullRotations

            // 直接调用 CameraViewController 的 setBlackMistThreshold 方法
            if let cameraVC = CameraView.currentCameraViewController {
                // 确保黑柔效果已启用，这样阈值调整才能生效
                if !cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                }
                cameraVC.setBlackMistThreshold(thresholdValue)
            }

        case .brightness:
            brightnessValue = memorizedBrightnessValue
            brightness = brightnessValue
            // 计算旋转角度
            let normalizedRotation = (Double(brightnessValue) + 1.0) / 2.0 * 360.0
            dialRotation = normalizedRotation + fullRotations

            // 直接调用 CameraViewController 的 setBrightness 方法
            if let cameraVC = CameraView.currentCameraViewController {
                cameraVC.setBrightness(brightnessValue)
            }

        case .film:
            filmIntensityValue = memorizedFilmIntensityValue
            // 计算旋转角度
            let normalizedRotation = Double(filmIntensityValue) * 360.0
            dialRotation = normalizedRotation + fullRotations

            // 直接调用 MetalRenderer 的 setLUTIntensity 方法
            if let cameraVC = CameraView.currentCameraViewController,
               let metalRenderer = cameraVC.getMetalRenderer() {
                metalRenderer.setLUTIntensity(filmIntensityValue)
            }

            // 更新显示视图模型
            displayViewModel.filmIntensity = filmIntensityValue
            displayViewModel.updateDisplayText()

            // 同步当前胶片滤镜强度到胶片模拟面板（当切换到胶片控制时）
            NotificationCenter.default.post(
                name: NSNotification.Name("UpdateFilmSimulationPanelIntensity"),
                object: nil,
                userInfo: ["intensity": filmIntensityValue]
            )

        case .none:
            mistValue = memorizedMistValue
            blackMistIntensity = mistValue
            isBlackMistEnabled = mistValue > 0
            // 计算旋转角度
            let normalizedRotation = Double(mistValue) * 360.0
            dialRotation = normalizedRotation + fullRotations

            // 直接调用 CameraViewController 的 setBlackMistIntensity 方法
            if let cameraVC = CameraView.currentCameraViewController {
                // 确保黑柔效果已启用，这样强度调整才能生效
                if mistValue > 0 && !cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                } else if mistValue == 0 && cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                }
                cameraVC.setBlackMistIntensity(mistValue)
            }
        }
    }

    private func getSnappedValue(for value: Float, type: ControlType) -> Float {
        switch type {
        case .mist:
            // Snap to 0.01 increments for Mist
            let snapped = round(value * 100) / 100
            // Clamp the value between 0 and 2.0
            return min(2.0, max(0.0, snapped))

        case .threshold:
            // Snap to 0.01 increments for threshold
            let snapped = round(value * 100) / 100
            // Clamp the value between 0 and 0.99
            return min(0.99, max(0.0, snapped))

        case .brightness:
            // Snap to 0.01 increments for brightness
            let snapped = round(value * 100) / 100
            // Clamp the value between -1.0 and 1.0
            return min(1.0, max(-1.0, snapped))

        case .film:
            // Snap to 0.01 increments for film intensity
            let snapped = round(value * 100) / 100
            // Clamp the value between 0 and 1.0
            return min(1.0, max(0.0, snapped))

        case .none:
            // Default to mist behavior
            let snapped = round(value * 100) / 100
            return min(2.0, max(0.0, snapped))
        }
    }

    // 根据控制类型获取最小旋转角度
    private func getMinRotation() -> Double {
        switch selectedControl {
        case .mist, .threshold, .film, .none:
            return 0  // 黑柔强度、高光阈值、胶片滤镜强度最小为0
        case .brightness:
            return 0  // 亮度最小为-1，对应0度旋转
        }
    }

    // 根据控制类型获取最大旋转角度
    private func getMaxRotation() -> Double {
        switch selectedControl {
        case .mist, .none, .threshold, .film:
            return 356  // 黑柔强度、高光阈值、胶片滤镜强度最大为1.0，对应356度
        case .brightness:
            return 356  // 亮度最大为0.99，对应356度
        }
    }

    private func snapToValue() {
        // 如果手动锁定或没有选中控制且黑柔未启用，则转盘被锁定，不处理对齐
        if isManuallyLocked || (selectedControl == .none && !isBlackMistEnabled) {
            return
        }

        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            isSnapping = true

            switch selectedControl {
            case .mist:
                // Snap to 0.01 increments
                mistValue = round(mistValue * 100) / 100
                // Cap to max value of 1.0
                mistValue = min(1.0, max(0.0, mistValue))
                // Update black mist intensity
                blackMistIntensity = mistValue
                // Calculate rotation based on snapped value (full circle for 0-1 range)
                let normalizedRotation = Double(mistValue) * 360.0 // 0-1 range maps to 0-360 degrees
                dialRotation = normalizedRotation + (dialRotation / 360).rounded() * 360

            case .threshold:
                // Snap threshold value
                blackMistThreshold = getSnappedValue(for: thresholdValue, type: .threshold)
                // Calculate rotation based on snapped value (full circle for 0-1 range)
                let normalizedRotation = Double(blackMistThreshold) * 360.0
                dialRotation = normalizedRotation + (dialRotation / 360).rounded() * 360

            case .brightness:
                // Snap brightness value
                brightness = getSnappedValue(for: brightnessValue, type: .brightness)
                // Calculate rotation based on snapped value (-1 to 1 range maps to 0-360 degrees)
                let normalizedRotation = (Double(brightness) + 1.0) / 2.0 * 360.0
                dialRotation = normalizedRotation + (dialRotation / 360).rounded() * 360

            case .film:
                // Snap film intensity value
                filmIntensityValue = getSnappedValue(for: filmIntensityValue, type: .film)
                // Calculate rotation based on snapped value (0-1 range maps to 0-360 degrees)
                let normalizedRotation = Double(filmIntensityValue) * 360.0
                dialRotation = normalizedRotation + (dialRotation / 360).rounded() * 360

            case .none:
                // Default to mist behavior
                mistValue = round(mistValue * 100) / 100
                mistValue = min(1.0, max(0.0, mistValue))
                blackMistIntensity = mistValue
                let normalizedRotation = Double(mistValue) * 360.0 // 0-1 range maps to 0-360 degrees
                dialRotation = normalizedRotation + (dialRotation / 360).rounded() * 360
            }

            hapticFeedback.impactOccurred(intensity: 0.8)

            // Reset snapping state after animation
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                isSnapping = false
            }
        }
    }

    private func handleRotationChanged(_ newRotation: Double) {
        // 如果手动锁定或没有选中控制且黑柔未启用，则转盘被锁定，不处理旋转
        if isManuallyLocked || (selectedControl == .none && !isBlackMistEnabled) {
            return
        }

        // 在所有计算中使用标准化的旋转角度（0-360度）
        let normalizedRotation = newRotation.truncatingRemainder(dividingBy: 360)
        // 确保值始终为正
        let positiveNormalizedRotation = normalizedRotation < 0 ? normalizedRotation + 360 : normalizedRotation

        switch selectedControl {
        case .mist:
            // Map rotation directly to 0-1 range for Mist with 0.01 steps
            let newValue = Float(positiveNormalizedRotation / 360)
            // 如果超出范围，保持数值在限制内但允许继续旋转
            if newValue <= 1.0 && newValue >= 0.0 {
                let oldValue = mistValue
                mistValue = newValue
                print("CameraView: 转盘旋转改变 mistValue 从 \(oldValue) 到 \(mistValue), 旋转角度: \(positiveNormalizedRotation)")
            }
            // 只调整黑柔滤镜的强度参数，不改变阈值和亮度
            let oldIntensity = blackMistIntensity
            blackMistIntensity = mistValue
            isBlackMistEnabled = mistValue > 0
            print("CameraView: 黑柔强度从 \(oldIntensity) 变为 \(blackMistIntensity), 阈值保持为 \(blackMistThreshold), 亮度保持为 \(brightness)")

            // 直接调用 CameraViewController 的 setBlackMistIntensity 方法
            if let cameraVC = CameraView.currentCameraViewController {
                // 确保黑柔效果已启用，这样强度调整才能生效
                if mistValue > 0 && !cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                } else if mistValue == 0 && cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                }

                cameraVC.setBlackMistIntensity(mistValue)
                print("CameraView: 直接调用 CameraViewController.setBlackMistIntensity(\(mistValue))")
            }

            // 更新显示视图模型并显示当前参数值
            displayViewModel.blackMistIntensity = blackMistIntensity
            displayViewModel.showPhotoCount = false
            displayViewModel.updateDisplayText()

        case .threshold:
            // Map rotation to 0-0.99 range for threshold (extended range)
            let newValue = Float(positiveNormalizedRotation / 360 * 0.99)
            // 如果超出范围，保持数值在限制内但允许继续旋转
            if newValue <= 0.99 && newValue >= 0.0 {
                let oldValue = thresholdValue
                thresholdValue = newValue
                print("CameraView: 转盘旋转改变 thresholdValue 从 \(oldValue) 到 \(thresholdValue), 旋转角度: \(positiveNormalizedRotation)")
            }

            // 调整黑柔滤镜的阈值参数
            let oldThreshold = blackMistThreshold
            blackMistThreshold = thresholdValue
            // 确保黑柔滤镜已启用，这样才能看到效果
            isBlackMistEnabled = true

            // 更新显示视图模型并显示当前参数值
            displayViewModel.blackMistThreshold = blackMistThreshold
            displayViewModel.showPhotoCount = false
            print("CameraView: 高光阈值从 \(oldThreshold) 变为 \(blackMistThreshold), 黑柔滤镜已启用: \(isBlackMistEnabled)")

            // 直接调用 CameraViewController 的 setBlackMistThreshold 方法
            if let cameraVC = CameraView.currentCameraViewController {
                // 确保黑柔效果已启用，这样阈值调整才能生效
                if !cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                }

                cameraVC.setBlackMistThreshold(thresholdValue)
                print("CameraView: 直接调用 CameraViewController.setBlackMistThreshold(\(thresholdValue))")
            }

            // 强制更新小肩屏显示
            displayViewModel.updateDisplayText()

        case .brightness:
            // Map rotation directly to -0.99 to 0.99 range for brightness
            // 调整计算，使得最大值为0.99而不是1.0
            let normalizedValue = Float(positiveNormalizedRotation / 360 * 1.98 - 0.99)
            // 如果超出范围，保持数值在限制内但允许继续旋转
            if normalizedValue <= 0.99 && normalizedValue >= -0.99 {
                let oldValue = brightnessValue
                brightnessValue = normalizedValue
                print("CameraView: 转盘旋转改变 brightnessValue 从 \(oldValue) 到 \(brightnessValue), 旋转角度: \(positiveNormalizedRotation)")
            }

            // 调整亮度参数，不强制启用黑柔滤镜
            let oldBrightness = brightness
            brightness = brightnessValue

            // 更新显示视图模型并显示当前参数值
            displayViewModel.brightness = brightness
            displayViewModel.showPhotoCount = false
            print("CameraView: 亮度从 \(oldBrightness) 变为 \(brightness)")

            // 直接调用 CameraViewController 的 setBrightness 方法
            if let cameraVC = CameraView.currentCameraViewController {
                cameraVC.setBrightness(brightnessValue)
                print("CameraView: 直接调用 CameraViewController.setBrightness(\(brightnessValue))")
            }

            // 强制更新小肩屏显示
            displayViewModel.updateDisplayText()

        case .film:
            // Map rotation directly to 0-1 range for film intensity
            let newValue = Float(positiveNormalizedRotation / 360)
            // 如果超出范围，保持数值在限制内但允许继续旋转
            if newValue <= 1.0 && newValue >= 0.0 {
                let oldValue = filmIntensityValue
                filmIntensityValue = newValue
                print("CameraView: 转盘旋转改变 filmIntensityValue 从 \(oldValue) 到 \(filmIntensityValue), 旋转角度: \(positiveNormalizedRotation)")
            }

            // 更新显示视图模型并显示当前参数值
            displayViewModel.filmIntensity = filmIntensityValue
            displayViewModel.showPhotoCount = false
            print("CameraView: 胶片滤镜强度变为 \(filmIntensityValue)")

            // 直接调用 MetalRenderer 的 setLUTIntensity 方法
            if let cameraVC = CameraView.currentCameraViewController,
               let metalRenderer = cameraVC.getMetalRenderer() {
                metalRenderer.setLUTIntensity(filmIntensityValue)
                print("CameraView: 直接调用 MetalRenderer.setLUTIntensity(\(filmIntensityValue))")
            }

            // 强制更新小肩屏显示
            displayViewModel.updateDisplayText()

            // 同步胶片滤镜强度到胶片模拟面板
            NotificationCenter.default.post(
                name: NSNotification.Name("UpdateFilmSimulationPanelIntensity"),
                object: nil,
                userInfo: ["intensity": filmIntensityValue]
            )

        case .none:
            // 当没有控制被选中时，调整黑柔强度
            // Map rotation directly to 0-1 range for Mist with 0.01 steps
            let newValue = Float(positiveNormalizedRotation / 360)
            // 如果超出范围，保持数值在限制内但允许继续旋转
            if newValue <= 1.0 && newValue >= 0.0 {
                let oldValue = mistValue
                mistValue = newValue
                print("CameraView: 转盘旋转改变 mistValue 从 \(oldValue) 到 \(mistValue), 旋转角度: \(positiveNormalizedRotation)")
            }

            // 只调整黑柔滤镜的强度参数，不改变阈值和亮度
            let oldIntensity = blackMistIntensity
            blackMistIntensity = mistValue
            isBlackMistEnabled = mistValue > 0
            print("CameraView: 黑柔强度从 \(oldIntensity) 变为 \(blackMistIntensity), 阈值保持为 \(blackMistThreshold), 亮度保持为 \(brightness)")

            // 直接调用 CameraViewController 的 setBlackMistIntensity 方法
            if let cameraVC = CameraView.currentCameraViewController {
                // 确保黑柔效果已启用，这样强度调整才能生效
                if mistValue > 0 && !cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                } else if mistValue == 0 && cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                }

                cameraVC.setBlackMistIntensity(mistValue)
                print("CameraView: 直接调用 CameraViewController.setBlackMistIntensity(\(mistValue))")
            }

            // 更新显示视图模型并显示当前参数值
            displayViewModel.blackMistIntensity = blackMistIntensity
            displayViewModel.showPhotoCount = false
            displayViewModel.updateDisplayText()
        }
    }

    private func resetCurrentValue() {
        // 如果手动锁定或没有选中控制且黑柔未启用，则转盘被锁定，不处理重置
        if isManuallyLocked || (selectedControl == .none && !isBlackMistEnabled) {
            return
        }

        hapticFeedback.impactOccurred(intensity: 1.0)

        // 保存当前旋转的整圈数
        let fullRotations = (dialRotation / 360).rounded() * 360

        switch selectedControl {
        case .mist:
            // Reset to default Mist value (0.3)
            mistValue = defaultMistValue
            blackMistIntensity = mistValue
            isBlackMistEnabled = mistValue > 0
            // Calculate rotation based on default value
            let normalizedRotation = Double(mistValue) * 360.0
            dialRotation = normalizedRotation + fullRotations
        case .threshold:
            // 重置高光阈值为默认值 0.2
            thresholdValue = 0.2
            blackMistThreshold = thresholdValue
            // 计算旋转角度
            let normalizedRotation = Double(thresholdValue) * 360.0
            dialRotation = normalizedRotation + fullRotations

            // 直接调用 CameraViewController 的 setBlackMistThreshold 方法
            if let cameraVC = CameraView.currentCameraViewController {
                // 确保黑柔效果已启用，这样阈值调整才能生效
                if !cameraVC.isBlackMistEnabled {
                    _ = cameraVC.toggleBlackMist()
                }
                cameraVC.setBlackMistThreshold(thresholdValue)
            }

        case .brightness:
            // 重置亮度为默认值 (-0.17)
            brightnessValue = defaultExposureCompValue
            brightness = brightnessValue
            // 计算旋转角度
            let normalizedRotation = (Double(brightnessValue) + 1.0) / 2.0 * 360.0
            dialRotation = normalizedRotation + fullRotations

            // 直接调用 CameraViewController 的 setBrightness 方法
            if let cameraVC = CameraView.currentCameraViewController {
                cameraVC.setBrightness(brightnessValue)
            }

        case .film:
            // 重置胶片滤镜强度为默认值 (0.75)
            filmIntensityValue = 0.75
            // 计算旋转角度
            let normalizedRotation = Double(filmIntensityValue) * 360.0
            dialRotation = normalizedRotation + fullRotations

            // 直接调用 MetalRenderer 的 setLUTIntensity 方法
            if let cameraVC = CameraView.currentCameraViewController,
               let metalRenderer = cameraVC.getMetalRenderer() {
                metalRenderer.setLUTIntensity(filmIntensityValue)
            }

            // 更新显示视图模型
            displayViewModel.filmIntensity = filmIntensityValue
            displayViewModel.updateDisplayText()

            // 同步胶片滤镜强度重置到胶片模拟面板
            NotificationCenter.default.post(
                name: NSNotification.Name("UpdateFilmSimulationPanelIntensity"),
                object: nil,
                userInfo: ["intensity": filmIntensityValue]
            )

        case .none:
            // 重置黑柔强度为默认值
            mistValue = defaultMistValue
            blackMistIntensity = mistValue
            isBlackMistEnabled = mistValue > 0
            // 计算旋转角度
            let normalizedRotation = Double(mistValue) * 360.0
            dialRotation = normalizedRotation + fullRotations

            // 更新相机控制器
            if let cameraVC = CameraView.currentCameraViewController {
                cameraVC.setBlackMistIntensity(mistValue)
            }

            // 更新显示
            displayViewModel.blackMistIntensity = blackMistIntensity
            displayViewModel.updateDisplayText()
        }
    }

    private func updateDialRotationForCurrentControl() {
        // 保存当前旋转的整圈数
        let fullRotations = (dialRotation / 360).rounded() * 360

        switch selectedControl {
        case .mist:
            // Calculate rotation based on Mist value (0-1 range)
            let normalizedRotation = Double(mistValue) * 360.0
            dialRotation = normalizedRotation + fullRotations
        case .threshold:
            // 计算高光阈值的旋转角度
            let normalizedRotation = Double(thresholdValue) * 360.0
            dialRotation = normalizedRotation + fullRotations

        case .brightness:
            // 计算亮度的旋转角度
            let normalizedRotation = (Double(brightnessValue) + 1.0) / 2.0 * 360.0
            dialRotation = normalizedRotation + fullRotations

        case .film:
            // 计算胶片滤镜强度的旋转角度
            let normalizedRotation = Double(filmIntensityValue) * 360.0
            dialRotation = normalizedRotation + fullRotations

        case .none:
            // 计算曝光补偿的旋转角度
            let normalizedRotation = (Double(exposureCompValue) + 3.0) / 6.0 * 270.0
            dialRotation = normalizedRotation + fullRotations
        }
    }

    // 添加检查是否为多镜头相机的函数
    private func checkCameraType() {
        // 在真实应用中，这里应该检查设备的摄像头类型
        // 为了示例，我们使用一个简单的模拟
        #if targetEnvironment(simulator)
        isMultiLensCamera = false  // 在模拟器中默认为单镜头
        #else
        // 在真实设备上检查摄像头类型
        if UIDevice.current.userInterfaceIdiom == .phone {
            // 检查设备型号决定是否有多个镜头
            // 这里是简化逻辑，实际应用应该更加精确地检测
            let deviceName = UIDevice.current.name
            isMultiLensCamera = deviceName.contains("Pro") || deviceName.contains("Plus") || deviceName.contains("Max")
        } else {
            isMultiLensCamera = false  // iPad或其他设备默认为单镜头
        }
        #endif
    }

    // Function to get lens text based on current zoom value
    private func getLensText() -> String {
        if lensZoomValue < 0.3 {
            return "0.5x"
        } else if lensZoomValue < 0.7 {
            return "1x"
        } else {
            return "3x"
        }
    }

    // 获取相机焦距倍数
    private func getRealFocalLength() -> String {
        // 直接使用镜头类型的显示名称（倍数表示）
        return currentLensType.displayName
    }


    var body: some View {
        GeometryReader { geometry in
            let isPortrait = geometry.size.height > geometry.size.width

            if !isPortrait {
                // LANDSCAPE LAYOUT (Original - No changes here other than removing specific portrait adjustments from shared elements if any were made previously)
                ZStack(alignment: .trailing) {
                    HStack(spacing: 0) {
                        Rectangle()
                            .fill(isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : Color.black)
                            .frame(width: geometry.size.width * 0.1)
                            .edgesIgnoringSafeArea(.vertical)

                        ZStack {
                            (isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : .black)
                                .edgesIgnoringSafeArea(.all)
                            GeometryReader { videoGeometry in
                                let availableWidth = videoGeometry.size.width
                                let availableHeight = videoGeometry.size.height
                                let cameraAspectRatio: CGFloat = 4.0 / 3.0
                                let videoHeight = availableWidth / cameraAspectRatio
                                ZStack {
                                    (isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : .black)
                                    CameraView(
                                        isBlackMistEnabled: $isBlackMistEnabled,
                                        blackMistThreshold: $blackMistThreshold,
                                        blackMistIntensity: $blackMistIntensity,
                                        brightness: $brightness,
                                        redTint: $redTint,
                                        greenTint: $greenTint,
                                        blueTint: $blueTint,
                                        currentLensType: $currentLensType,
                                        isMultiLensCamera: $isMultiLensCamera,
                                        availableLensTypes: $availableLensTypes,
                                        flashMode: $flashMode,
                                        selectedControl: $selectedControl,
                                        displayViewModel: displayViewModel,
                                        onCapturePhoto: { success, image in if success, let img = image { capturedMedia.append(.photo(img)) } },
                                        onCaptureVideo: { success, url in if success, let videoUrl = url { capturedMedia.append(.video(videoUrl)) } },
                                        onLensChanged: { lensType in
                                            // 更新 lensZoomValue 以便与旧的镜头切换按钮兼容
                                            switch lensType {
                                            case .front: lensZoomValue = -0.5     // 自拍摄像头
                                            case .ultraWide: lensZoomValue = 0.0  // 超广角
                                            case .wide: lensZoomValue = 0.5       // 广角
                                            case .telephoto: lensZoomValue = 1.0  // 2.5倍长焦
                                            case .telephoto3: lensZoomValue = 1.5 // 3倍长焦
                                            case .telephoto5: lensZoomValue = 2.0 // 5倍长焦
                                            }
                                        }
                                    )
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: availableWidth, height: videoHeight)
                                    .clipped()
                                    .clipShape(RoundedRectangle(cornerRadius: 12))
                                    ShutterCurtainAnimation(isAnimating: $isShutterAnimating).allowsHitTesting(false)
                                    VStack { Spacer(); HStack { FocalLengthView(
                                        currentLensType: $currentLensType,
                                        isMultiLensCamera: isMultiLensCamera,
                                        availableLensTypes: availableLensTypes,
                                        onLensSwitch: { lensType in
                                            currentLensType = lensType

                                            // 更新 lensZoomValue
                                            switch lensType {
                                            case .front: lensZoomValue = -0.5     // 自拍摄像头
                                            case .ultraWide: lensZoomValue = 0.0  // 超广角
                                            case .wide: lensZoomValue = 0.5       // 广角
                                            case .telephoto: lensZoomValue = 1.0  // 2.5倍长焦
                                            case .telephoto3: lensZoomValue = 1.5 // 3倍长焦
                                            case .telephoto5: lensZoomValue = 2.0 // 5倍长焦
                                            }

                                            hapticFeedback.impactOccurred(intensity: 0.5)
                                        }
                                    ); Spacer() } }.allowsHitTesting(true)
                                }
                                .frame(width: availableWidth, height: videoHeight)
                                .overlay(RoundedRectangle(cornerRadius: 12).stroke(isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : Color.black, lineWidth: 8))
                                .position(x: availableWidth/2, y: availableHeight/2)
                            }
                        }
                        .frame(width: geometry.size.width * 0.58)

                        VStack(alignment: .center) {
                            Spacer()
                            ShoulderDisplayView(viewModel: displayViewModel, onTap: openSettings)
                                .padding(.top, 15)
                                .padding(.leading, -geometry.size.width * 0.05)
                                .frame(maxWidth: .infinity, alignment: .center)
                            ZStack {
                                ControlDialView(dialRotation: $dialRotation, isSnapping: $isSnapping, isWhiteMode: isWhiteMode, isLocked: isManuallyLocked || (selectedControl == .none && !isBlackMistEnabled), minRotation: getMinRotation(), maxRotation: getMaxRotation(), onRotationChanged: handleRotationChanged, onRotationEnded: snapToValue, onDoubleTap: resetCurrentValue, onTap: takePhoto)
                                    .frame(width: 130, height: 130)
                                                            // Lock文字 - 始终显示，可点击
                            Button(action: toggleLock) {
                                Text("Lock")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor((isManuallyLocked || (selectedControl == .none && !isBlackMistEnabled)) ? Color.orange : Color.gray)
                                    .padding(5)
                            }
                            .offset(x: 70, y: -60)
                                VStack {
                                    HStack(spacing: 40) {
                                        Button(action: openAlbum) { Image(systemName: "photo.on.rectangle").font(.system(size: 18, weight: .regular)).foregroundColor(isWhiteMode ? Color.gray : Color.white).padding(6).opacity(0.8) }
                                        Button(action: toggleFlash) { Image(systemName: flashMode.systemName).font(.system(size: 18, weight: flashMode == .on ? .semibold : .regular)).foregroundColor(flashMode.color).padding(6).opacity(flashMode == .on ? 1.0 : 0.8) }
                                    }.padding(.top, 0).frame(maxWidth: .infinity, alignment: .center)
                                    Spacer()
                                    HStack {
                                        Spacer()
                                        Button(action: toggleRecording) {
                                            if recordingMode == .recording { Image(systemName: "stop.fill").font(.system(size: 18, weight: .semibold)).foregroundColor(.red) }
                                            else { Circle().fill(Color.red).frame(width: 18, height: 18) }
                                        }.padding(.trailing, 60)
                                    }.padding(.top, -50)
                                }.frame(height: 200)
                            }
                            .padding(.vertical, 20)
                            .padding(.leading, -geometry.size.width * 0.05)
                            .frame(maxWidth: .infinity, alignment: .center)
                            ControlButtonsView(selectedControl: $selectedControl, isBlackMistEnabled: $isBlackMistEnabled, isManuallyLocked: $isManuallyLocked, hapticFeedback: hapticFeedback, displayViewModel: displayViewModel, isPortrait: false)
                                .padding(.top, -25)
                                .padding(.leading, -geometry.size.width * 0.05)
                                .frame(maxWidth: .infinity, alignment: .center)
                            Spacer()
                        }
                        .frame(width: geometry.size.width * 0.35)
                        .background(isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : Color.black)
                    }
                }
            } else {
                // PORTRAIT LAYOUT (New image-based layout)
                HStack(spacing: 0) {
                    // Left: Camera View (same as landscape)
                    Rectangle()
                        .fill(isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : Color.black)
                        .frame(width: geometry.size.width * 0.1)
                        .edgesIgnoringSafeArea(.vertical)

                    ZStack {
                        (isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : .black)
                            .edgesIgnoringSafeArea(.all)
                        GeometryReader { videoGeometry in
                            let availableWidth = videoGeometry.size.width
                            let availableHeight = videoGeometry.size.height
                            let cameraAspectRatio: CGFloat = 4.0 / 3.0
                            let videoHeight = availableWidth / cameraAspectRatio
                            ZStack {
                                (isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : .black)
                                CameraView(
                                    isBlackMistEnabled: $isBlackMistEnabled,
                                    blackMistThreshold: $blackMistThreshold,
                                    blackMistIntensity: $blackMistIntensity,
                                    brightness: $brightness,
                                    redTint: $redTint,
                                    greenTint: $greenTint,
                                    blueTint: $blueTint,
                                    currentLensType: $currentLensType,
                                    isMultiLensCamera: $isMultiLensCamera,
                                    availableLensTypes: $availableLensTypes,
                                    flashMode: $flashMode,
                                    selectedControl: $selectedControl,
                                    displayViewModel: displayViewModel,
                                    onCapturePhoto: { success, image in if success, let img = image { capturedMedia.append(.photo(img)) } },
                                    onCaptureVideo: { success, url in if success, let videoUrl = url { capturedMedia.append(.video(videoUrl)) } },
                                    onLensChanged: { lensType in
                                        // 更新 lensZoomValue 以便与旧的镜头切换按钮兼容
                                        switch lensType {
                                        case .front: lensZoomValue = -0.5     // 自拍摄像头
                                        case .ultraWide: lensZoomValue = 0.0  // 超广角
                                        case .wide: lensZoomValue = 0.5       // 广角
                                        case .telephoto: lensZoomValue = 1.0  // 2.5倍长焦
                                        case .telephoto3: lensZoomValue = 1.5 // 3倍长焦
                                        case .telephoto5: lensZoomValue = 2.0 // 5倍长焦
                                        }
                                    }
                                )
                                .aspectRatio(contentMode: .fill)
                                .frame(width: availableWidth, height: videoHeight)
                                .clipped()
                                .clipShape(RoundedRectangle(cornerRadius: 12))
                                ShutterCurtainAnimation(isAnimating: $isShutterAnimating).allowsHitTesting(false)
                                VStack { Spacer(); HStack { FocalLengthView(
                                    currentLensType: $currentLensType,
                                    isMultiLensCamera: isMultiLensCamera,
                                    availableLensTypes: availableLensTypes,
                                    onLensSwitch: { lensType in
                                        currentLensType = lensType

                                        // 更新 lensZoomValue
                                        switch lensType {
                                        case .front: lensZoomValue = -0.5     // 自拍摄像头
                                        case .ultraWide: lensZoomValue = 0.0  // 超广角
                                        case .wide: lensZoomValue = 0.5       // 广角
                                        case .telephoto: lensZoomValue = 1.0  // 2.5倍长焦
                                        case .telephoto3: lensZoomValue = 1.5 // 3倍长焦
                                        case .telephoto5: lensZoomValue = 2.0 // 5倍长焦
                                        }

                                        hapticFeedback.impactOccurred(intensity: 0.5)
                                    }
                                ); Spacer() } }.allowsHitTesting(true)
                            }
                            .frame(width: availableWidth, height: videoHeight)
                            .overlay(RoundedRectangle(cornerRadius: 12).stroke(isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : Color.black, lineWidth: 8))
                            .position(x: availableWidth/2, y: availableHeight/2)
                        }
                    }
                    .frame(width: geometry.size.width * 0.58)

                    // Right: Controls (similar to landscape but adjusted for portrait dimensions)
                    VStack(alignment: .center) {
                        Spacer()
                        ShoulderDisplayView(viewModel: displayViewModel, onTap: openSettings)
                            .padding(.top, 15)
                            .padding(.leading, -geometry.size.width * 0.05)
                            .frame(maxWidth: .infinity, alignment: .center)
                        ZStack {
                            ControlDialView(dialRotation: $dialRotation, isSnapping: $isSnapping, isWhiteMode: isWhiteMode, isLocked: isManuallyLocked || (selectedControl == .none && !isBlackMistEnabled), minRotation: getMinRotation(), maxRotation: getMaxRotation(), onRotationChanged: handleRotationChanged, onRotationEnded: snapToValue, onDoubleTap: resetCurrentValue, onTap: takePhoto)
                                .frame(width: 130, height: 130)
                            // Lock文字 - 始终显示，可点击
                            Button(action: toggleLock) {
                                Text("Lock")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor((isManuallyLocked || (selectedControl == .none && !isBlackMistEnabled)) ? Color.orange : Color.gray)
                                    .padding(5)
                            }
                            .offset(x: 70, y: -60)
                            VStack {
                                HStack(spacing: 40) {
                                    Button(action: openAlbum) { Image(systemName: "photo.on.rectangle").font(.system(size: 18, weight: .regular)).foregroundColor(isWhiteMode ? Color.gray : Color.white).padding(6).opacity(0.8) }
                                    Button(action: toggleFlash) { Image(systemName: flashMode.systemName).font(.system(size: 18, weight: flashMode == .on ? .semibold : .regular)).foregroundColor(flashMode.color).padding(6).opacity(flashMode == .on ? 1.0 : 0.8) }
                                }.padding(.top, 0).frame(maxWidth: .infinity, alignment: .center)
                                Spacer()
                                HStack {
                                    Spacer()
                                    Button(action: toggleRecording) {
                                        if recordingMode == .recording { Image(systemName: "stop.fill").font(.system(size: 18, weight: .semibold)).foregroundColor(.red) }
                                        else { Circle().fill(Color.red).frame(width: 18, height: 18) }
                                    }.padding(.trailing, 60)
                                }.padding(.top, -50)
                            }.frame(height: 200)
                        }
                        .padding(.vertical, 20)
                        .padding(.leading, -geometry.size.width * 0.05)
                        .frame(maxWidth: .infinity, alignment: .center)
                        ControlButtonsView(selectedControl: $selectedControl, isBlackMistEnabled: $isBlackMistEnabled, isManuallyLocked: $isManuallyLocked, hapticFeedback: hapticFeedback, displayViewModel: displayViewModel, isPortrait: false)
                            .padding(.top, -25)
                            .padding(.leading, -geometry.size.width * 0.05)
                            .frame(maxWidth: .infinity, alignment: .center)
                        Spacer()
                    }
                    .frame(width: geometry.size.width * 0.35)
                    .background(isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : Color.black)
                }
            }
            // Common modifiers like edge gesture, settings panel overlay, etc. remain outside the if/else for orientation
            // Edge gesture for settings - COMMENTED OUT TO DISABLE LEFT SWIPE BLANK PAGE
            /*
            GeometryReader { gestureGeometry in
                HStack(spacing: 0) {
                    Spacer()

                    Color.clear
                        .frame(width: 20)
                        .contentShape(Rectangle())
                        .gesture(
                            DragGesture()
                                .onChanged { value in
                                    if value.translation.width < 0 {
                                        dragOffset = min(300, abs(value.translation.width))
                                    }
                                }
                                .onEnded { value in
                                    if abs(value.translation.width) > 100 {
                                        showSettings = true
                                        dragOffset = 0
                                    } else {
                                        dragOffset = 0
                                    }
                                }
                        )
                }
            }
            // Settings panel overlay - COMMENTED OUT TO DISABLE LEFT SWIPE BLANK PAGE
            if dragOffset > 0 {
                Color.black.opacity(0.4 * (dragOffset / 300))
                    .edgesIgnoringSafeArea(.all)

                HStack(spacing: 0) {
                    Spacer()

                    Rectangle()
                        .fill(Color(UIColor.systemBackground))
                        .frame(width: dragOffset)
                        .edgesIgnoringSafeArea(.vertical)
                }
            }
            */
        }
        .edgesIgnoringSafeArea(.all)
        .hideHomeIndicator()
        .onAppear {

            // Prepare haptic feedback
            hapticFeedback.prepare()
            shutterFeedback.prepare()

            // 检查相机类型
            checkCameraType()

            // 初始化显示视图模型
            displayViewModel.selectedControl = .mist  // 默认为 mist 控制黑柔参数
            displayViewModel.blackMistIntensity = blackMistIntensity
            displayViewModel.blackMistThreshold = blackMistThreshold
            displayViewModel.brightness = brightness
            displayViewModel.exposureCompValue = exposureCompValue
            displayViewModel.filmIntensity = filmIntensityValue

            // 初始显示照片计数
            displayViewModel.showPhotoCount = true
            displayViewModel.updateDisplayText()

            // 几秒后自动隐藏照片计数并显示当前参数
            displayViewModel.autoHidePhotoCount(after: 3.0)

            // 设置 CameraMediaOutputManager 的显示视图模型
            CameraMediaOutputManager.shared.setDisplayViewModel(displayViewModel)

            // 确保黑柔效果默认打开
            isBlackMistEnabled = true
            blackMistIntensity = mistValue

            // 设置初始转盘位置为默认的 MIST 值
            let normalizedRotation = Double(mistValue) * 360.0
            dialRotation = normalizedRotation

            // 设置音量键拍照监听
            setupVolumeKeyCapture()

            // 延迟加载默认 CineStill 800t 胶片滤镜（确保相机已初始化）
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                loadDefaultCineStillFilter()
            }
        }
        .onDisappear {
            // 清理haptic feedback
            hapticFeedback.prepare() // 重置haptic feedback状态
            shutterFeedback.prepare()

            // 停止音量键监听
            stopVolumeMonitoring()

            // 清理相机会话（如果需要）
            print("CameraControlView: 视图消失，清理资源")
        }
        // 通知处理 - 使用 SwiftUI 推荐的 onReceive 方式
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("UIModeChanged"))) { _ in
            isWhiteMode = UserDefaults.standard.bool(forKey: "whiteUIMode")
        }
        .onReceive(NotificationCenter.default.publisher(for: .languageDidChange)) { _ in
            print("CameraControlView: 检测到语言变更，将强制重启相机")
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                guard let cameraVC = CameraView.currentCameraViewController else { return }
                cameraVC.stopSession()

                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    cameraVC.startSession()
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("TriggerShutterAnimation"))) { _ in
            isShutterAnimating = true
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ResetMistValue"))) { notification in
            guard let value = notification.userInfo?["value"] as? Float else { return }
            print("CameraControlView: 接收到重置黑柔参数通知，值为 \(value)")

            mistValue = value
            blackMistIntensity = value
            isBlackMistEnabled = value > 0

            let normalizedRotation = Double(value) * 360.0
            let fullRotations = (dialRotation / 360).rounded() * 360
            dialRotation = normalizedRotation + fullRotations

            displayViewModel.blackMistIntensity = value
            displayViewModel.updateDisplayText()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ResetThresholdValue"))) { notification in
            guard let value = notification.userInfo?["value"] as? Float else { return }
            print("CameraControlView: 接收到重置高光阈值参数通知，值为 \(value)")

            thresholdValue = value
            blackMistThreshold = value

            let normalizedRotation = Double(value) * 360.0
            let fullRotations = (dialRotation / 360).rounded() * 360
            dialRotation = normalizedRotation + fullRotations

            displayViewModel.blackMistThreshold = value
            displayViewModel.updateDisplayText()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ResetBrightnessValue"))) { notification in
            guard let value = notification.userInfo?["value"] as? Float else { return }
            print("CameraControlView: 接收到重置亮度参数通知，值为 \(value)")

            brightnessValue = value
            brightness = value

            let normalizedRotation = (Double(value) + 1.0) / 2.0 * 360.0
            let fullRotations = (dialRotation / 360).rounded() * 360
            dialRotation = normalizedRotation + fullRotations

            displayViewModel.brightness = value
            displayViewModel.updateDisplayText()
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("ResetFilmValue"))) { notification in
            guard let value = notification.userInfo?["value"] as? Float else { return }
            print("CameraControlView: 接收到重置胶片滤镜强度参数通知，值为 \(value)")

            filmIntensityValue = value

            let normalizedRotation = Double(value) * 360.0
            let fullRotations = (dialRotation / 360).rounded() * 360
            dialRotation = normalizedRotation + fullRotations

            displayViewModel.filmIntensity = value
            displayViewModel.updateDisplayText()

            // 通知 MetalRenderer
            if let cameraVC = CameraView.currentCameraViewController,
               let metalRenderer = cameraVC.getMetalRenderer() {
                metalRenderer.setLUTIntensity(value)
            }

            // 同步胶片滤镜强度重置到胶片模拟面板
            NotificationCenter.default.post(
                name: NSNotification.Name("UpdateFilmSimulationPanelIntensity"),
                object: nil,
                userInfo: ["intensity": value]
            )
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("SyncFilmIntensity"))) { notification in
            guard let value = notification.userInfo?["intensity"] as? Float else { return }
            print("CameraControlView: 接收到同步胶片滤镜强度通知，值为 \(value)")

            // 更新内部值，与FilmSimulationPanel保持一致
            filmIntensityValue = value
            memorizedFilmIntensityValue = value  // 同时更新记忆值

            // 如果当前选择的是胶片控制，同步转盘位置
            if selectedControl == .film {
                let normalizedRotation = Double(value) * 360.0
                let fullRotations = (dialRotation / 360).rounded() * 360
                dialRotation = normalizedRotation + fullRotations
            }

            // 更新显示视图模型（已在GlassDisplayViewModel中处理，这里确保一致性）
            displayViewModel.filmIntensity = value
            displayViewModel.updateDisplayText()

            // 通知 MetalRenderer（已在FilmSimulationPanel中处理，这里确保一致性）
            if let cameraVC = CameraView.currentCameraViewController,
               let metalRenderer = cameraVC.getMetalRenderer() {
                metalRenderer.setLUTIntensity(value)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIDevice.orientationDidChangeNotification)) { _ in
            isPortrait = UIDevice.current.orientation.isPortrait
        }
        // 从右侧滑出胶片模拟面板的手势
        .overlay(
            GeometryReader { gestureGeometry in
                HStack(spacing: 0) {
                    Spacer()

                    Color.clear
                        .frame(width: 20)
                        .contentShape(Rectangle())
                        .gesture(
                            DragGesture()
                                .onChanged { value in
                                    // 从右边缘向左滑动
                                    if value.translation.width < 0 {
                                        // 可以添加预览效果，暂时不实现
                                    }
                                }
                                .onEnded { value in
                                    // 如果向左滑动超过100点，显示胶片模拟面板
                                    if value.translation.width < -100 {
                                        withAnimation(.easeInOut(duration: 0.3)) {
                                            showFilmSimulation = true
                                        }
                                    }
                                }
                        )
                }
            }
        )
        // 胶片模拟面板覆盖层
        .overlay(
            FilmSimulationPanel(isPresented: $showFilmSimulation)
                .opacity(showFilmSimulation ? 1 : 0)
                .animation(.easeInOut(duration: 0.3), value: showFilmSimulation)
        )
        .sheet(isPresented: $showGallery) { AppGalleryView(isPresented: $showGallery) }
        .sheet(isPresented: $showSettings) { SettingsView(isPresented: $showSettings) }
    }
}


