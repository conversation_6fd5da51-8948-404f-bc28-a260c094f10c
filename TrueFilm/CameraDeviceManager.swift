//
//  CameraDeviceManager.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//  Extracted from CameraViewController.swift
//

import UIKit
import AVFoundation
import CoreLocation // For location-based logic if needed
import Darwin // 添加Darwin导入以支持utsname结构
// Import Models to access CameraLensType enum
import TrueFilm


class CameraDeviceManager { // Changed from UIViewController to a manager class
    // 相机捕获会话 - Assume these are passed in or managed by the main CameraViewController
    private let captureSession: AVCaptureSession
    private let videoOutput: AVCaptureVideoDataOutput
    private let sessionQueue: DispatchQueue
    
    private var videoDevice: AVCaptureDevice?

    // 镜头相关
    private(set) var currentLensType: CameraLensType = CameraLensType.wide  // 默认使用广角镜头
    private(set) var availableLensTypes: [CameraLensType] = []  // 可用的镜头类型
    private(set) var isMultiLensCamera: Bool = false  // 是否是多镜头相机

    // 焦距信息回调
    var onLensChanged: ((CameraLensType) -> Void)?

    // 预览层
    var previewLayer: AVCaptureVideoPreviewLayer?

    // 闪光灯模式
    private var flashMode: AVCaptureDevice.FlashMode = .off

    // Initializer to inject dependencies
    init(captureSession: AVCaptureSession, videoOutput: AVCaptureVideoDataOutput, sessionQueue: DispatchQueue, previewLayer: AVCaptureVideoPreviewLayer? = nil) {
        self.captureSession = captureSession
        self.videoOutput = videoOutput
        self.sessionQueue = sessionQueue
        self.previewLayer = previewLayer
    }

    // MARK: - 相机设置 (Partial)

    func setupInitialCameraDevice() {
        // 检查相机权限
        checkCameraPermission { [weak self] granted in
            guard granted, let self = self else {
                print("Camera permission not granted or self is nil")
                // Handle permission denial (e.g., show alert to user)
                return
            }

            self.sessionQueue.async {
                self.configureInitialDeviceInput()
                // Note: Starting the session would typically be done by the main controller
                // self.captureSession.startRunning()
            }
        }
    }

    private func checkCameraPermission(completion: @escaping (Bool) -> Void) {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            completion(true)
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    completion(granted)
                }
            }
        default: // .denied, .restricted
            completion(false)
        }
    }
    
    // Detect available lenses - 使用视场角比较的更精确方法
    private func detectAvailableLenses() {
        availableLensTypes = []
        
        // 添加前置摄像头（自拍）
        if AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front) != nil {
            availableLensTypes.append(CameraLensType.front)
        }

        // 创建后置镜头发现会话
        let discoverySession = AVCaptureDevice.DiscoverySession(
            deviceTypes: [
                .builtInUltraWideCamera,    // 超广角镜头 (0.5x)
                .builtInWideAngleCamera,    // 广角镜头 (1x)
                .builtInTelephotoCamera     // 长焦镜头 (2x/3x/5x等)
            ],
            mediaType: .video,
            position: .back
        )
        
        // 获取所有后置摄像头设备
        let devices = discoverySession.devices
        print("检测到 \(devices.count) 个后置摄像头")
        
                    // 获取设备型号信息
                    var systemInfo = utsname()
                    uname(&systemInfo)
                    let deviceName = Mirror(reflecting: systemInfo.machine).children.reduce("") { identifier, element in
                        guard let value = element.value as? Int8, value != 0 else { return identifier }
                        return identifier + String(UnicodeScalar(UInt8(value)))
                    }.lowercased()
                    
        print("设备型号: \(deviceName)")
        
        // 检测超广角镜头（0.5x）
        if devices.contains(where: { $0.deviceType == .builtInUltraWideCamera }) {
            availableLensTypes.append(.ultraWide)
        }
        
        // 广角镜头（1x）- 所有iPhone都有
        if devices.contains(where: { $0.deviceType == .builtInWideAngleCamera }) {
            availableLensTypes.append(.wide)
        }
        
        // 检测长焦镜头并根据设备型号确定具体配置
        if devices.contains(where: { $0.deviceType == .builtInTelephotoCamera }) {
            // 根据设备型号判断镜头配置
            if deviceName.contains("iphone16") {
                if deviceName.contains("max") {
                    // iPhone 16 Pro Max: 2x (数码), 5x (光学)
                    availableLensTypes.append(.telephoto)    // 2x 数码变焦
                    availableLensTypes.append(.telephoto5)   // 5x 光学
                } else if deviceName.contains("pro") {
                    // iPhone 16 Pro: 2x (数码), 3x (光学)
                    availableLensTypes.append(.telephoto)    // 2x 数码变焦
                    availableLensTypes.append(.telephoto3)   // 3x 光学
                }
            } else if deviceName.contains("iphone15") {
                if deviceName.contains("max") {
                    // iPhone 15 Pro Max: 2x (数码), 5x (光学)
                    availableLensTypes.append(.telephoto)    // 2x 数码变焦
                    availableLensTypes.append(.telephoto5)   // 5x 光学
                } else if deviceName.contains("pro") {
                    // iPhone 15 Pro: 2x (数码), 3x (光学)
                    availableLensTypes.append(.telephoto)    // 2x 数码变焦
                    availableLensTypes.append(.telephoto3)   // 3x 光学
                }
            } else if deviceName.contains("iphone14") {
                if deviceName.contains("pro") {
                    // iPhone 14 Pro/Pro Max: 2x (数码), 3x (光学)
                    availableLensTypes.append(.telephoto)    // 2x 数码变焦
                    availableLensTypes.append(.telephoto3)   // 3x 光学
                }
            } else if deviceName.contains("iphone13") {
                if deviceName.contains("pro") {
                    if deviceName.contains("max") {
                        // iPhone 13 Pro Max: 3x (光学)
                        availableLensTypes.append(.telephoto3)   // 3x 光学
                    } else {
                        // iPhone 13 Pro: 3x (光学)
                        availableLensTypes.append(.telephoto3)   // 3x 光学
                    }
                    }
            } else if deviceName.contains("iphone12") {
                if deviceName.contains("pro") {
                    if deviceName.contains("max") {
                        // iPhone 12 Pro Max: 2.5x (光学)
                        availableLensTypes.append(.telephoto)   // 当作 2x 处理
                } else {
                        // iPhone 12 Pro: 2x (光学)
                        availableLensTypes.append(.telephoto)   // 2x 光学
                    }
                }
            } else {
                // 默认情况：如果检测到长焦镜头但无法确定型号，假设是 2x
                availableLensTypes.append(.telephoto)
            }
        }

        // 对数组去重，以防重复添加
        availableLensTypes = Array(Set(availableLensTypes))
        
        // 排序镜头类型
        availableLensTypes.sort { lens1, lens2 in
            // 特殊处理前置摄像头，始终将其放在最前
            if lens1 == .front {
                return true
            }
            if lens2 == .front {
                return false
            }
            
            // 为其他镜头，按照倍率顺序排序: 0.5x, 1x, 2x, 3x, 5x
            return lens1.rawValue < lens2.rawValue
        }
        
        print("可用镜头类型（已排序）: \(availableLensTypes.map { "\($0.displayName) (\($0))" }.joined(separator: ", "))")
        
        print("Available lens types: \(availableLensTypes.map { $0.displayName }.joined(separator: ", "))")
    }

    private func getDeviceForLensType(_ lensType: CameraLensType) -> AVCaptureDevice? {
        // 特殊处理前置摄像头
        if lensType == .front {
            return AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front)
        }
        // 处理后置摄像头
        return AVCaptureDevice.default(lensType.deviceType, for: .video, position: .back)
    }
    
    // Part of the original configureSession, focused on initial device input
    private func configureInitialDeviceInput() {
        captureSession.beginConfiguration()

        // 设置分辨率 - 使用最高质量
        if captureSession.canSetSessionPreset(.photo) {
            captureSession.sessionPreset = .photo
        } else if captureSession.canSetSessionPreset(.high) {
            captureSession.sessionPreset = .high
        }
        
        detectAvailableLenses()
        let selectedLensType = availableLensTypes.contains(currentLensType) ? currentLensType : (availableLensTypes.first ?? CameraLensType.wide)

        guard let device = getDeviceForLensType(selectedLensType),
              let videoInput = try? AVCaptureDeviceInput(device: device),
              captureSession.canAddInput(videoInput) else {
            print("无法添加相机输入 for lens \(selectedLensType.displayName)")
            captureSession.commitConfiguration()
            return
        }

        self.videoDevice = device
        self.currentLensType = selectedLensType
        captureSession.addInput(videoInput)
        
        // Note: videoOutput configuration would also happen here or be managed by the main controller
        // if captureSession.canAddOutput(videoOutput) { ... }

        captureSession.commitConfiguration()
        
        // Notify about the initial lens
        DispatchQueue.main.async { [weak self] in
            self?.onLensChanged?(selectedLensType)
        }
    }

    // MARK: - 镜头切换

    func switchToLens(_ lensType: CameraLensType) {
        guard availableLensTypes.contains(lensType), lensType != currentLensType else {
            return
        }

        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            self.captureSession.beginConfiguration()

            // Remove current video input
            for input in self.captureSession.inputs {
                if let deviceInput = input as? AVCaptureDeviceInput, deviceInput.device.hasMediaType(.video) {
                    self.captureSession.removeInput(deviceInput)
                }
            }

            // 确定是否为前置摄像头
            let isFrontCamera = lensType == .front
            
            // 获取适当的设备
            let device: AVCaptureDevice?
            if isFrontCamera {
                device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front)
            } else {
                device = self.getDeviceForLensType(lensType)
            }

            // Add new video input
            if let device = device,
               let videoInput = try? AVCaptureDeviceInput(device: device),
               self.captureSession.canAddInput(videoInput) {
                self.captureSession.addInput(videoInput)
                self.videoDevice = device
                self.currentLensType = lensType
                
                // 更新视频连接镜像和方向
                if let connection = self.videoOutput.connection(with: .video) {
                    // 确保视频方向正确
                    if connection.isVideoOrientationSupported {
                        connection.videoOrientation = .landscapeRight
                    }
                    
                    // 设置视频镜像
                    if connection.isVideoMirroringSupported {
                        connection.automaticallyAdjustsVideoMirroring = false
                        connection.isVideoMirrored = isFrontCamera
                    }
                }
                
                // 更新预览层镜像和方向
                if let previewLayer = self.previewLayer,
                   let connection = previewLayer.connection {
                    if connection.isVideoOrientationSupported {
                        connection.videoOrientation = .landscapeRight
                    }
                    
                    if connection.isVideoMirroringSupported {
                        connection.automaticallyAdjustsVideoMirroring = false
                        connection.isVideoMirrored = isFrontCamera
                    }
                }
                
                // 重置任何变换
                DispatchQueue.main.async {
                    self.previewLayer?.transform = CATransform3DIdentity
                }
                
                print("切换到镜头: \(lensType.displayName)")
            } else {
                print("切换镜头失败: \(lensType.displayName)")
                // Re-add previous input if possible or handle error
            }

            self.captureSession.commitConfiguration()

            DispatchQueue.main.async {
                self.onLensChanged?(lensType)
            }
        }
    }

    func switchToNextLens() {
        guard isMultiLensCamera, !availableLensTypes.isEmpty else { return }
        if let currentIndex = availableLensTypes.firstIndex(of: currentLensType) {
            // 检查是否已经是最后一个镜头
            if currentIndex >= availableLensTypes.count - 1 {
                return
            }
            let nextIndex = currentIndex + 1
            switchToLens(availableLensTypes[nextIndex])
        } else if let firstLens = availableLensTypes.first {
            switchToLens(firstLens)
        }
    }

    func switchToPreviousLens() {
        guard isMultiLensCamera, !availableLensTypes.isEmpty else { return }
        if let currentIndex = availableLensTypes.firstIndex(of: currentLensType) {
            // 检查是否已经是第一个镜头
            if currentIndex <= 0 {
                return
            }
            let previousIndex = currentIndex - 1
            switchToLens(availableLensTypes[previousIndex])
        } else if let firstLens = availableLensTypes.first {
            switchToLens(firstLens)
        }
    }
    
    // MARK: - Flash Control
    func setFlashMode(_ mode: AVCaptureDevice.FlashMode) {
        self.flashMode = mode // This mode is for photo capture logic
        print("设置拍照闪光灯模式为: \(mode == .on ? "强制闪光" : mode == .auto ? "自动" : "关闭")")
        
        // Ensure torch is off for general video preview, only flash for photo
        sessionQueue.async { [weak self] in
            guard let self = self, let device = self.videoDevice else { return }
            guard device.hasTorch && device.isTorchAvailable else { return }

            do {
                try device.lockForConfiguration()
                if device.torchMode != .off { // Turn off torch if it's on
                    device.torchMode = .off
                }
                device.unlockForConfiguration()
            } catch {
                print("无法配置设备手电筒: \(error.localizedDescription)")
            }
        }
    }

    // This method would be called by the main CameraViewController before taking a photo
    func getPhotoFlashSettings() -> AVCapturePhotoSettings {
        let photoSettings = AVCapturePhotoSettings()
        if self.videoDevice?.isFlashAvailable ?? false {
            photoSettings.flashMode = self.flashMode
        }
        return photoSettings
    }
    
    // Trigger flash for photo (simplified from original, assuming it's for a photo, not general torch)
    // The actual flash for photo is handled by AVCapturePhotoOutput and AVCapturePhotoSettings
    // This is more like a pre-flash or a manual torch control if needed.
    // The original triggerFlash was complex; for photo capture, settings on AVCapturePhotoOutput are preferred.
    // For simplicity, if direct torch control is needed for a "flash effect" before photo:
    func momentarilyTriggerTorch(duration: TimeInterval = 0.1, completion: (() -> Void)? = nil) {
        guard let device = videoDevice, device.hasTorch && device.isTorchAvailable else {
            completion?()
            return
        }
        sessionQueue.async {
            do {
                try device.lockForConfiguration()
                try device.setTorchModeOn(level: AVCaptureDevice.maxAvailableTorchLevel)
                device.unlockForConfiguration()

                DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                    do {
                        try device.lockForConfiguration()
                        device.torchMode = .off
                        device.unlockForConfiguration()
                        completion?()
                    } catch {
                        print("无法关闭手电筒: \(error.localizedDescription)")
                        completion?()
                    }
                }
            } catch {
                print("无法开启手电筒: \(error.localizedDescription)")
                completion?()
            }
        }
    }
}

// 不再需要设备型号检测，已改用视场角比较方法