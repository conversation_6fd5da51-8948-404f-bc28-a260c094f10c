//
//  CameraManager.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

import AVFoundation
import Combine
import SwiftUI
import UIKit
import Metal
import MetalKit

class CameraManager: NSObject, ObservableObject {
    // Published properties for UI updates
    @Published var previewLayer: AVCaptureVideoPreviewLayer?
    @Published var isSessionRunning = false
    @Published var error: CameraError?
    @Published var processedTexture: MTLTexture?

    // Metal renderer for applying filters
    private var metalRenderer: MetalRenderer?

    // Metal device
    var metalDevice: MTLDevice? {
        return metalRenderer?.device
    }

    // 当前滤镜状态
    var currentFilterState: String {
        return isBlackMistEnabled ? "Black Mist Enabled" : "No Filter"
    }

    // Debug counter
    private var frameCount: Int = 0

    // Filter state
    private var isBlackMistEnabled = false

    // Camera session and related properties
    private let captureSession = AVCaptureSession()
    private let sessionQueue = DispatchQueue(label: "com.latenightking.TrueFilm.sessionQueue")
    private var videoOutput = AVCaptureVideoDataOutput()

    // Camera setup state
    private var isConfigured = false

    // Camera position
    private var position: AVCaptureDevice.Position = .back

    // Device orientation
    private var currentOrientation: UIDeviceOrientation = .portrait

    override init() {
        super.init()

        // Create preview layer
        let previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        previewLayer.videoGravity = .resizeAspectFill
        self.previewLayer = previewLayer

        // Initialize Metal renderer
        metalRenderer = MetalRenderer()

        // Start monitoring device orientation
        setupOrientationMonitoring()
    }

    deinit {
        // Stop device orientation notifications
        NotificationCenter.default.removeObserver(self)
        UIDevice.current.endGeneratingDeviceOrientationNotifications()

        // Stop capture session
        stopSession()
    }

    // MARK: - Public Methods

    func checkPermissions() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            self.setupCaptureSession()
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                guard granted else { return }
                DispatchQueue.main.async {
                    self?.setupCaptureSession()
                }
            }
        case .denied, .restricted:
            self.error = .deniedAuthorization
        @unknown default:
            self.error = .unknownAuthorization
        }
    }

    func startSession() {
        guard !captureSession.isRunning else { return }

        sessionQueue.async { [weak self] in
            self?.captureSession.startRunning()
            DispatchQueue.main.async {
                self?.isSessionRunning = self?.captureSession.isRunning ?? false
            }
        }
    }

    func stopSession() {
        guard captureSession.isRunning else { return }

        sessionQueue.async { [weak self] in
            self?.captureSession.stopRunning()
            DispatchQueue.main.async {
                self?.isSessionRunning = self?.captureSession.isRunning ?? false
            }
        }
    }

    // MARK: - Private Methods

    private func setupOrientationMonitoring() {
        // Start device orientation notifications
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(deviceOrientationDidChange),
            name: UIDevice.orientationDidChangeNotification,
            object: nil
        )

        // Enable device orientation notifications
        UIDevice.current.beginGeneratingDeviceOrientationNotifications()

        // Set initial orientation
        currentOrientation = UIDevice.current.orientation
        updatePreviewLayerOrientation()
    }

    @objc private func deviceOrientationDidChange() {
        // Update current orientation
        let newOrientation = UIDevice.current.orientation
        
        // Only update if orientation actually changed
        if newOrientation != currentOrientation {
            currentOrientation = newOrientation
            updatePreviewLayerOrientation()
        }
    }

    private func updatePreviewLayerOrientation() {
        // Skip face-up and face-down orientations
        guard currentOrientation != .faceUp && currentOrientation != .faceDown else { return }

        // Always force landscape mode regardless of device orientation
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // Always use landscape right orientation
            let videoOrientation: AVCaptureVideoOrientation = .landscapeRight
            
            // Update preview layer orientation (already on main thread)
            self.previewLayer?.connection?.videoOrientation = videoOrientation

            // Update video output connection orientation on session queue
            sessionQueue.async { [weak self] in
                guard let self = self else { return }
                if let connection = self.videoOutput.connection(with: .video) {
                    if connection.isVideoOrientationSupported {
                        connection.videoOrientation = videoOrientation
                    }
                    
                    // Set mirroring based on camera position
                    if connection.isVideoMirroringSupported {
                        connection.isVideoMirrored = (self.position == .front)
                    }
                }
            }
        }
    }

    private func setupCaptureSession() {
        guard !isConfigured else { return }

        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            self.captureSession.beginConfiguration()

            // Set up video input
            guard let videoDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: self.position),
                  let videoInput = try? AVCaptureDeviceInput(device: videoDevice),
                  self.captureSession.canAddInput(videoInput) else {
                self.error = .cameraUnavailable
                self.captureSession.commitConfiguration()
                return
            }

            self.captureSession.addInput(videoInput)

            // Set up video output
            if self.captureSession.canAddOutput(self.videoOutput) {
                self.captureSession.addOutput(self.videoOutput)
                self.videoOutput.videoSettings = [kCVPixelBufferPixelFormatTypeKey as String: Int(kCVPixelFormatType_32BGRA)]
                self.videoOutput.setSampleBufferDelegate(self, queue: self.sessionQueue)

                // Always force landscape orientation for video output regardless of device orientation
                if let connection = self.videoOutput.connection(with: .video), connection.isVideoOrientationSupported {
                    connection.videoOrientation = .landscapeRight
                    // 设置视频方向
                    if connection.isVideoMirroringSupported {
                        connection.automaticallyAdjustsVideoMirroring = false
                        connection.isVideoMirrored = (self.position == .front)
                    }
                }
            } else {
                self.error = .cannotAddOutput
                self.captureSession.commitConfiguration()
                return
            }

            self.captureSession.commitConfiguration()
            self.isConfigured = true

            self.startSession()
            
            // Update preview layer to also use landscape orientation
            if let previewLayer = self.previewLayer {
                previewLayer.connection?.videoOrientation = .landscapeRight
                // Set preview layer mirroring based on camera position
                if previewLayer.connection?.isVideoMirroringSupported == true {
                    previewLayer.connection?.isVideoMirrored = (self.position == .front)
                }
            }
        }
    }

    // Add method to switch camera position
    func switchCameraPosition(to position: AVCaptureDevice.Position) {
        sessionQueue.async { [weak self] in
            guard let self = self else { return }
            
            self.captureSession.beginConfiguration()
            
            // Remove existing input
            if let currentInput = self.captureSession.inputs.first as? AVCaptureDeviceInput {
                self.captureSession.removeInput(currentInput)
            }
            
            // Add new input with selected position
            if let videoDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: position),
               let videoInput = try? AVCaptureDeviceInput(device: videoDevice),
               self.captureSession.canAddInput(videoInput) {
                self.captureSession.addInput(videoInput)
                self.position = position
                
                // 更新视频连接镜像 - 先禁用自动镜像调整，再设置镜像
                if let connection = self.videoOutput.connection(with: .video),
                   connection.isVideoMirroringSupported {
                    connection.automaticallyAdjustsVideoMirroring = false
                    connection.isVideoMirrored = (position == .front)
                }
                
                // 更新预览层镜像
                if let previewLayer = self.previewLayer,
                   previewLayer.connection?.isVideoMirroringSupported == true {
                    previewLayer.connection?.automaticallyAdjustsVideoMirroring = false
                    previewLayer.connection?.isVideoMirrored = (position == .front)
                }
            }
            
            self.captureSession.commitConfiguration()
        }
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate

extension CameraManager: AVCaptureVideoDataOutputSampleBufferDelegate {
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        guard let metalRenderer = metalRenderer else { return }

        // Convert CMSampleBuffer to Metal texture
        guard let inputTexture = metalRenderer.texture(from: sampleBuffer) else { return }

        // 处理帧
        let outputTexture = metalRenderer.processFrame(texture: inputTexture)

        // 发布处理后的纹理以供显示
        DispatchQueue.main.async { [weak self] in
            if self?.isBlackMistEnabled == true {
                self?.processedTexture = outputTexture
                print("CameraManager: Published processed texture")
            } else {
                self?.processedTexture = nil
                print("CameraManager: Cleared processed texture")
            }
        }

        // 偶尔打印调试信息（每 30 帧）
        self.frameCount += 1
        if self.frameCount % 30 == 0 {
            if isBlackMistEnabled {
                print("Processing frame with Black Mist enabled")
            } else {
                print("Processing frame with Black Mist disabled")
            }
        }
    }

    // Toggle Black Mist effect
    func toggleBlackMist() -> Bool {
        isBlackMistEnabled = !isBlackMistEnabled

        if let metalRenderer = metalRenderer {
            metalRenderer.toggleBlackMist()
        }

        // 如果禁用了黑柔滤镜，则清除处理后的纹理
        if !isBlackMistEnabled {
            DispatchQueue.main.async { [weak self] in
                self?.processedTexture = nil
            }
        }

        print("Black Mist \(isBlackMistEnabled ? "enabled" : "disabled")")
        return isBlackMistEnabled
    }

    // Set Black Mist threshold
    func setBlackMistThreshold(_ value: Float) {
        metalRenderer?.setThreshold(value)
    }

    // Set Black Mist intensity
    func setBlackMistIntensity(_ value: Float) {
        metalRenderer?.setBloomIntensity(value)
    }
}

// MARK: - Camera Errors

enum CameraError: Error {
    case deniedAuthorization
    case restrictedAuthorization
    case unknownAuthorization
    case cameraUnavailable
    case cannotAddInput
    case cannotAddOutput

    var description: String {
        switch self {
        case .deniedAuthorization:
            return "Camera access was denied. Please enable it in Settings."
        case .restrictedAuthorization:
            return "Camera access is restricted."
        case .unknownAuthorization:
            return "Unknown authorization status for camera access."
        case .cameraUnavailable:
            return "Camera is unavailable."
        case .cannotAddInput:
            return "Cannot add camera input."
        case .cannotAddOutput:
            return "Cannot add camera output."
        }
    }
}
