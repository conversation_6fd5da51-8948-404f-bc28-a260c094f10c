//
//  CameraMediaOutputManager.swift
//  TrueFilm
//
//  Created on 2025/5/7.
//

import SwiftUI
import AVFoundation
import UIKit
import Foundation
import TrueFilm // For GlassDisplayViewModel, FlashMode, RecordingMode, CameraView, and AudioManager

/// 管理相机的照片和视频输出功能
class CameraMediaOutputManager {
    
    // MARK: - Singleton
    
    /// 单例实例，便于全局访问
    static let shared = CameraMediaOutputManager()
    
    /// 私有初始化方法，确保只能通过单例访问
    private init() {
        // 初始化触觉反馈
        hapticFeedback.prepare()
        shutterFeedback.prepare()
    }
    
    // MARK: - Properties
    
    /// 用于拍照时的触觉反馈
    private let shutterFeedback = UIImpactFeedbackGenerator(style: .rigid)
    
    /// 用于其他操作的触觉反馈
    private let hapticFeedback = UIImpactFeedbackGenerator(style: .medium)
    
    /// 当前的闪光灯模式
    private var flashMode: FlashMode = .off
    
    /// 当前的录像状态
    private var recordingMode: RecordingMode = .off
    
    /// 显示视图模型，用于更新UI
    private var displayViewModel: GlassDisplayViewModel?
    
    // MARK: - Public Methods
    
    /// 设置显示视图模型
    /// - Parameter viewModel: 用于更新UI的显示视图模型
    func setDisplayViewModel(_ viewModel: GlassDisplayViewModel) {
        self.displayViewModel = viewModel
    }
    
    /// 设置闪光灯模式
    /// - Parameter mode: 新的闪光灯模式
    func setFlashMode(_ mode: FlashMode) {
        self.flashMode = mode
    }
    
    /// 获取当前闪光灯模式
    /// - Returns: 当前的闪光灯模式
    func getFlashMode() -> FlashMode {
        return flashMode
    }
    
    /// 获取当前录像状态
    /// - Returns: 当前的录像状态
    func getRecordingMode() -> RecordingMode {
        return recordingMode
    }
    
    // MARK: - Photo Capture
    
    /// 拍照功能
    func takePhoto() {
        shutterFeedback.impactOccurred(intensity: 1.0)
        print("点击转盘拍照")

        // 触发快门帘动画（通过回调通知UI）
        NotificationCenter.default.post(name: NSNotification.Name("TriggerShutterAnimation"), object: nil)
        
        // 在快门关闭后拍照（0.25秒后，与快门帘动画同步）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
            // 直接使用静态方法拍照
            let success = CameraView.takePhotoWithCurrentCamera()

            if success {
                print("拍照命令已发送")
            } else {
                print("找不到相机控制器，拍照失败")
            }

            // 更新照片计数
            self.displayViewModel?.incrementPhotoCount()
        }
    }
    
    // MARK: - Flash Control
    
    /// 切换闪光灯模式
    func toggleFlash() {
        let oldMode = flashMode
        
        // 在录像模式下，只在开启和关闭之间切换
        if recordingMode == .recording {
            // 录像模式下只在开和关之间切换
            flashMode = (flashMode == .on) ? .off : .on
        } else {
            // 非录像模式下使用标准的三态切换
            flashMode = flashMode.nextMode
        }

        // 提供更强的触觉反馈，特别是在切换到强制闪光灯模式时
        if flashMode == .on {
            hapticFeedback.impactOccurred(intensity: 0.8)  // 更强的触觉反馈
        } else {
            hapticFeedback.impactOccurred(intensity: 0.5)
        }

        // 播放相应的音效
        if oldMode == .off && flashMode == .on {
            // 从关闭切换到开启时播放充电音效
            AudioManager.shared.playFlashChargeSound()

            // 打印日志，确认闪光灯已设置为强制开启模式
            print("闪光灯模式已设置为强制开启")
        } else {
            // 切换到自动或关闭时播放关闭音效
            AudioManager.shared.playFlashOffSound()
            print("闪光灯模式已设置为: \(flashMode == .auto ? "自动" : "关闭")")
        }
        
        // 更新相机控制器的闪光灯设置
        if let cameraVC = CameraView.currentCameraViewController {
            cameraVC.setFlashMode(flashMode.avFlashMode)
        }
    }
    
    // MARK: - Video Recording
    
    /// 切换录像状态
    func toggleRecording() {
        recordingMode = recordingMode.toggle
        
        if recordingMode == .recording {
            // 开始录像
            hapticFeedback.impactOccurred(intensity: 0.8)
            print("开始录像")
            
            // 如果当前是自动闪光灯模式，在录像时应切换为关闭模式
            if flashMode == .auto {
                flashMode = .off
                // 更新相机控制器的闪光灯设置
                if let cameraVC = CameraView.currentCameraViewController {
                    cameraVC.setFlashMode(flashMode.avFlashMode)
                }
            }
            
            // 开始计时
            displayViewModel?.startRecording()
            
            // 调用相机控制器开始录像
            if let cameraVC = CameraView.currentCameraViewController {
                cameraVC.startRecording()
            }
        } else {
            // 停止录像
            hapticFeedback.impactOccurred(intensity: 0.5)
            print("停止录像")
            
            // 停止计时
            displayViewModel?.stopRecording()
            
            // 调用相机控制器停止录像
            if let cameraVC = CameraView.currentCameraViewController {
                cameraVC.stopRecording()
            }
        }
    }
}
