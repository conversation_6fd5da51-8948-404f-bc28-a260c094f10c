//
//  CameraView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

import SwiftUI
import AVFoundation
import PhotosUI
import Foundation

// Import AudioManager for sound effects
import UIKit
// Import Models to access CameraLensType enum
import TrueFilm

struct CameraView: UIViewControllerRepresentable {
    // 静态引用，便于其他组件访问相机控制器
    static var currentCameraViewController: CameraViewController?

    // 添加静态标志跟踪是否正在进行语言切换
    static var isLanguageChanging: Bool = false

    @Binding var isBlackMistEnabled: Bool
    @Binding var blackMistThreshold: Float
    @Binding var blackMistIntensity: Float
    @Binding var brightness: Float
    @Binding var redTint: Float
    @Binding var greenTint: Float
    @Binding var blueTint: Float

    // 镜头相关属性
    @Binding var currentLensType: CameraLensType
    @Binding var isMultiLensCamera: Bool
    @Binding var availableLensTypes: [CameraLensType]

    // 闪光灯模式
    @Binding var flashMode: FlashMode

    // 添加控制状态属性，用于确定转盘是否锁定
    @Binding var selectedControl: ControlType

    // Add display view model
    var displayViewModel: GlassDisplayViewModel?

    var onCapturePhoto: (Bool, UIImage?) -> Void
    var onCaptureVideo: (Bool, URL?) -> Void
    var onLensChanged: ((CameraLensType) -> Void)?

    // 确保相机视图在语言切换后保持活跃
    private static let preventRecreation = UUID()

    // 添加静态通知观察器
    static func setupLanguageChangeObserver() {
        NotificationCenter.default.addObserver(forName: .languageDidChange,
                                              object: nil,
                                              queue: .main) { _ in
            print("CameraView: 语言已更改，标记相机需要重建")
            isLanguageChanging = true

            // 延迟一段时间后重置标志
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                isLanguageChanging = false
                print("CameraView: 语言更改处理完成，重置标志")
            }
        }
    }

    // 添加一个自定义视图修饰符，用于在相机视图上叠加"Lock"文本
    struct LockTextOverlay: ViewModifier {
        var isLocked: Bool
        var isWhiteMode: Bool

        func body(content: Content) -> some View {
            ZStack {
                content

                if isLocked {
                    VStack {
                        HStack {
                            Spacer()
                            Text("Lock")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(isWhiteMode ? .black : .white)
                                .padding(4)
                                .background(
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(isWhiteMode ? Color.white.opacity(0.7) : Color.black.opacity(0.7))
                                )
                                .padding(.trailing, 40)
                                .padding(.top, 40)
                            Spacer().frame(width: 20)
                        }
                        Spacer()
                    }
                }
            }
        }
    }

    // 扩展View以添加锁定文本修饰符
    static func addLockText(_ view: some View, isLocked: Bool) -> some View {
        let isWhiteMode = UserDefaults.standard.bool(forKey: "whiteUIMode")
        return view.modifier(LockTextOverlay(isLocked: isLocked, isWhiteMode: isWhiteMode))
    }

    func makeUIViewController(context: Context) -> CameraViewController {
        print("CameraView: makeUIViewController 被调用")

        // 确保设置了语言更改观察器
        CameraView.setupLanguageChangeObserver()

        // 如果正在进行语言切换，创建一个新的相机控制器
        if CameraView.isLanguageChanging {
            print("CameraView: 检测到语言切换，创建新相机控制器")
            let newController = CameraViewController()
            CameraView.currentCameraViewController = newController
            context.coordinator.viewController = newController

            setupCallbacks(for: newController)
            return newController
        }

        // 先检查是否已经有一个运行中的相机控制器
        if let existingController = CameraView.currentCameraViewController, existingController.isReady {
            print("CameraView: 复用现有的相机控制器")
            context.coordinator.viewController = existingController
            return existingController
        }

        let controller = CameraViewController()
        // 保存引用，便于其他组件访问
        CameraView.currentCameraViewController = controller
        context.coordinator.viewController = controller

        setupCallbacks(for: controller)

        print("CameraView: 创建了 CameraViewController")
        return controller
    }

    // 抽取回调设置逻辑为单独的方法
    private func setupCallbacks(for controller: CameraViewController) {
        // 滑动效果回调
        controller.onFilterStateChanged = { newState in
            DispatchQueue.main.async {
                self.isBlackMistEnabled = newState
            }
        }

        // Set the display view model
        controller.displayViewModel = self.displayViewModel

        // 拍照回调
        controller.onPhotoCaptured = { success, image in
            if success {
                print("照片已保存到相册")
                DispatchQueue.main.async {
                    self.onCapturePhoto(true, image)
                }
            } else {
                print("照片保存失败")
                DispatchQueue.main.async {
                    self.onCapturePhoto(false, nil)
                }
            }
        }

        // 视频录制回调
        controller.onVideoCaptured = { success, url in
            if success {
                print("视频已保存到相册")
                DispatchQueue.main.async {
                    self.onCaptureVideo(true, url)
                }
            } else {
                print("视频保存失败")
                DispatchQueue.main.async {
                    self.onCaptureVideo(false, nil)
                }
            }
        }

        // 镜头变化回调
        controller.onLensChanged = { lensType in
            DispatchQueue.main.async {
                self.onLensChanged?(lensType)
            }

            // 更新镜头状态
            self.isMultiLensCamera = controller.isMultiLensCamera
            self.availableLensTypes = controller.availableLensTypes
            self.currentLensType = lensType
        }

        // 在控制器初始化后，将镜头信息更新到 SwiftUI 视图
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.isMultiLensCamera = controller.isMultiLensCamera
            self.availableLensTypes = controller.availableLensTypes
            self.currentLensType = controller.currentLensType
        }
    }

    func updateUIViewController(_ uiViewController: CameraViewController, context: Context) {
        // 添加防重入保护
        guard !context.coordinator.isUpdating else { return }
        context.coordinator.isUpdating = true
        defer { context.coordinator.isUpdating = false }

        // 限制相机重启频率 - 只在确实需要且有足够间隔时才重启
        if !uiViewController.isReady {
            let now = Date()
            if context.coordinator.lastRestartAttempt == nil ||
               now.timeIntervalSince(context.coordinator.lastRestartAttempt!) > 2.0 {
                context.coordinator.lastRestartAttempt = now
                print("CameraView: 相机未就绪，尝试重启会话")
                uiViewController.startSession()
            }
            return // 如果相机未就绪，不进行其他更新
        }

        // 移除捏合手势设置以防止卡死问题
        // 捏合手势可能导致与Metal渲染的冲突和死锁

        // 获取上一次的值
        let oldBlackMistThreshold = context.coordinator.lastBlackMistThreshold
        let oldBlackMistIntensity = context.coordinator.lastBlackMistIntensity
        let oldBrightness = context.coordinator.lastBrightness
        let oldRedTint = context.coordinator.lastRedTint
        let oldGreenTint = context.coordinator.lastGreenTint
        let oldBlueTint = context.coordinator.lastBlueTint
        let oldFlashMode = context.coordinator.lastFlashMode
        let oldCurrentLensType = context.coordinator.lastCurrentLensType

        // 更新滤镜状态（仅在变化时）
        if uiViewController.isBlackMistEnabled != isBlackMistEnabled {
            print("CameraView: 切换黑柔滤镜状态为 \(isBlackMistEnabled)")
            uiViewController.setBlackMistEnabled(isBlackMistEnabled)
        }

        // 只在值发生变化时才更新和打印
        if blackMistThreshold != oldBlackMistThreshold {
            uiViewController.setBlackMistThreshold(blackMistThreshold)
            context.coordinator.lastBlackMistThreshold = blackMistThreshold

            // 同步更新肩屏显示
            if let displayVM = uiViewController.displayViewModel {
                displayVM.updateParameters(threshold: blackMistThreshold)
            }
        }

        if blackMistIntensity != oldBlackMistIntensity {
            print("CameraView updateUIViewController: 设置黑柔强度从 \(oldBlackMistIntensity) 变为 \(blackMistIntensity)")
            uiViewController.setBlackMistIntensity(blackMistIntensity)
            context.coordinator.lastBlackMistIntensity = blackMistIntensity

            // 同步更新肩屏显示
            if let displayVM = uiViewController.displayViewModel {
                displayVM.updateParameters(mistIntensity: blackMistIntensity)
            }
        }

        if brightness != oldBrightness {
            uiViewController.setBrightness(brightness)
            context.coordinator.lastBrightness = brightness

            // 同步更新肩屏显示
            if let displayVM = uiViewController.displayViewModel {
                displayVM.updateParameters(brightnessValue: brightness)
            }
        }

        if redTint != oldRedTint {
            uiViewController.setRedTint(redTint)
            context.coordinator.lastRedTint = redTint
        }

        if greenTint != oldGreenTint {
            uiViewController.setGreenTint(greenTint)
            context.coordinator.lastGreenTint = greenTint
        }

        if blueTint != oldBlueTint {
            uiViewController.setBlueTint(blueTint)
            context.coordinator.lastBlueTint = blueTint
        }

        // 更新闪光灯模式（仅在变化时）
        if flashMode.avFlashMode.rawValue != oldFlashMode {
            uiViewController.setFlashMode(flashMode.avFlashMode)
            context.coordinator.lastFlashMode = flashMode.avFlashMode.rawValue
        }

        // 镜头切换（仅在变化时）
        if currentLensType != oldCurrentLensType &&
           uiViewController.availableLensTypes.contains(currentLensType) {
            uiViewController.switchToLens(currentLensType)
            context.coordinator.lastCurrentLensType = currentLensType
        }
    }

    // 创建协调器
    func makeCoordinator() -> Coordinator {
        Coordinator(parent: self)
    }

    // 协调器类
    class Coordinator: NSObject {
        var parent: CameraView
        var viewController: CameraViewController?

        // 保存上一次的滤镜参数值，用于优化更新
        var lastBlackMistThreshold: Float = 0.03
        var lastBlackMistIntensity: Float = 0.3
        var lastBrightness: Float = 0
        var lastRedTint: Float = 0.0
        var lastGreenTint: Float = 0.0
        var lastBlueTint: Float = 0.0

        // 添加重启尝试时间
        var lastRestartAttempt: Date?

        // 添加闪光灯模式状态
        var lastFlashMode: Int = 0

        // 添加镜头类型状态
        var lastCurrentLensType: CameraLensType = .wide

        // 添加更新状态
        var isUpdating: Bool = false

        init(parent: CameraView) {
            self.parent = parent

            // 初始化上一次的值
            self.lastBlackMistThreshold = parent.blackMistThreshold
            self.lastBlackMistIntensity = parent.blackMistIntensity
            self.lastBrightness = parent.brightness
            self.lastRedTint = parent.redTint
            self.lastGreenTint = parent.greenTint
            self.lastBlueTint = parent.blueTint
            self.lastFlashMode = parent.flashMode.avFlashMode.rawValue
            self.lastCurrentLensType = parent.currentLensType

            super.init()
        }

        // 触发拍照
        func capturePhoto() {
            if let viewController = self.viewController {
                viewController.capturePhoto()
                parent.onCapturePhoto(true, nil)
            } else {
                print("相机控制器未就绪，无法拍照")
                parent.onCapturePhoto(false, nil)
            }
        }

        // 移除了捏合手势处理方法以防止卡死问题
        // 捏合缩放功能已被禁用以提高稳定性
    }

    // 确保视图控制器始终填充整个屏幕
    static func dismantleUIViewController(_ uiViewController: CameraViewController, coordinator: Coordinator) {
        print("CameraView: dismantleUIViewController 被调用")

        // 如果同一个控制器仍被引用且不在语言切换，不要销毁它
        if currentCameraViewController === uiViewController && !isLanguageChanging {
            print("CameraView: 保留相机控制器以便复用")

            // 不清除静态引用，允许下一个视图复用它
            return
        }

        print("CameraView: 释放了 CameraViewController")

        // 只有当不再复用时才清理
        if currentCameraViewController === uiViewController {
            currentCameraViewController = nil
        }

        // 清理协调器中的引用
        coordinator.viewController = nil

        // 移除通知观察者
        NotificationCenter.default.removeObserver(uiViewController)
    }

    // 公开静态方法，用于触发拍照
    static func takePhotoWithCurrentCamera() -> Bool {
        if let cameraVC = currentCameraViewController {
            // 使用isReady属性检查相机状态，避免直接访问私有属性captureSession
            if cameraVC.isReady {
                cameraVC.capturePhoto()
                return true
            } else {
                print("相机未就绪")
                return false
            }
        } else {
            print("相机控制器未初始化")
            return false
        }
    }
}

