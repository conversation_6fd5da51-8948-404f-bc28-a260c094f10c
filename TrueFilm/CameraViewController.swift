//
//  CameraViewController.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

import UIKit
import AVFoundation
import Metal
import MetalKit
import MetalPerformanceShaders
import CoreLocation
import Photos
import TrueFilm

class CameraViewController: UIViewController, AVCaptureFileOutputRecordingDelegate {
    // 相机捕获会话
    let captureSession = AVCaptureSession()
    let videoOutput = AVCaptureVideoDataOutput()
    let sessionQueue = DispatchQueue(label: "com.latenightking.TrueFilm.sessionQueue")
    private var videoDevice: AVCaptureDevice?
    // 注意：我们不再使用 photoOutput，因为我们现在直接从视频流截图

    // 镜头相关
    private(set) var currentLensType: CameraLensType = .wide  // 默认使用广角镜头
    private(set) var availableLensTypes: [CameraLensType] = []  // 可用的镜头类型
    private(set) var isMultiLensCamera: Bool = false  // 是否是多镜头相机

    // 焦距信息回调
    var onLensChanged: ((CameraLensType) -> Void)?

    // 预览层
    var previewLayer: AVCaptureVideoPreviewLayer?

    // 滤镜相关视图
    var filterBackgroundView: UIView?
    var filterImageView: UIImageView?

    // Metal 相关
    var ciContext: CIContext?
    private var _currentCIImage: CIImage?
    private let imageAccessQueue = DispatchQueue(label: "com.latenightking.TrueFilm.imageAccess", qos: .userInteractive)

    var currentCIImage: CIImage? {
        get {
            return imageAccessQueue.sync { _currentCIImage }
        }
        set {
            imageAccessQueue.async { [weak self] in
                self?._currentCIImage = newValue
            }
        }
    }

    // Remove currentSampleBuffer as it can become invalid and cause crashes

    // Metal 渲染器
    var metalRenderer: MetalRenderer?

    // Filter Manager
    var filterManager: FilterManager?

    // 相机参数
    private var currentISO: Double = 100
    private var currentAperture: Double = 2.8
    private var currentShutterSpeed: Double = 1.0/60.0

    // Display view model for updating the glass display
    var displayViewModel: GlassDisplayViewModel?

    /// Formats the shutter speed as a string in the format "1/Xs" where X is the denominator
    /// - Parameter shutterSpeed: The shutter speed value as a Double (e.g., 1/60 = 0.01666...)
    /// - Returns: A formatted string representation (e.g., "1/60s")
    private func formatShutterSpeed(_ shutterSpeed: Double) -> String {
        let formattedSpeed = String(format: "1/%.0f", 1.0/shutterSpeed)

        // Update the display view model if available
        DispatchQueue.main.async {
            self.displayViewModel?.updateShutterSpeed(speed: formattedSpeed)
        }

        return formattedSpeed
    }
    private var currentExposureCompensation: Double = -0.17  // 曝光补偿值

    // 闪光灯模式
    private var flashMode: AVCaptureDevice.FlashMode = .off

    // 帧计数器，用于调试
    var frameCount = 0

    // 内存清理计时器
    private var memoryCleanupTimer: Timer?

    // 错误计数器，用于实现断路器模式
    var errorCount = 0
    let maxErrorCount = 10
    var isCircuitBreakerOpen = false
    private var successCount = 0
    private let successResetThreshold = 100 // Reset error count after 100 successful operations

    // 死锁检测
    private var lastFrameTime: Date = Date()
    private var deadlockDetectionTimer: Timer?
    private let deadlockThreshold: TimeInterval = 5.0 // 5秒无帧处理视为死锁

    // 拍照状态保护
    private var isCapturingPhoto: Bool = false
    private let captureProtectionQueue = DispatchQueue(label: "com.latenightking.TrueFilm.captureProtection", qos: .userInteractive)

    // 回调
    var onFilterStateChanged: ((Bool) -> Void)?
    var onPhotoCaptured: ((Bool, UIImage?) -> Void)?
    var onVideoCaptured: ((Bool, URL?) -> Void)?

    // 公开属性，用于检查相机状态
    var isReady: Bool {
        return captureSession.isRunning
    }

    // 获取Metal渲染器
    func getMetalRenderer() -> MetalRenderer? {
        return metalRenderer
    }

    // 公开黑柔滤镜状态属性
    var isBlackMistEnabled: Bool {
        return filterManager?.isBlackMistEnabled ?? false
    }

    // 设置黑柔滤镜状态
    func setBlackMistEnabled(_ enabled: Bool) {
        if (enabled != isBlackMistEnabled) {
            _ = toggleBlackMist()
        }
    }

    // MARK: - Video Recording
    private var movieFileOutput = AVCaptureMovieFileOutput()
    var isRecording = false

    // MARK: - Video Recording with Filters
    var assetWriter: AVAssetWriter?
    var assetWriterVideoInput: AVAssetWriterInput?
    var assetWriterAudioInput: AVAssetWriterInput?
    var assetWriterPixelBufferAdaptor: AVAssetWriterInputPixelBufferAdaptor?
    var videoWritingQueue = DispatchQueue(label: "com.latenightking.TrueFilm.videoWritingQueue")
    var startTime: CMTime?
    private var currentVideoURL: URL?

    // 添加音频输出
    private let audioOutput = AVCaptureAudioDataOutput()
    private var audioSampleBufferQueue = DispatchQueue(label: "com.latenightking.TrueFilm.audioSampleBufferQueue")

    // 添加设备类型检测
    private var deviceType: String {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        return machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
    }

    // 初始化可用镜头类型
    private func initializeAvailableLensTypes() {
        // Reset available lens list
        availableLensTypes = []

        // Check for front camera
        if AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front) != nil {
            availableLensTypes.append(.front)
        }

        // Check for back cameras
        let discoverySession = AVCaptureDevice.DiscoverySession(
            deviceTypes: [.builtInWideAngleCamera, .builtInUltraWideCamera, .builtInTelephotoCamera],
            mediaType: .video,
            position: .back
        )
        let backCameraDevices = discoverySession.devices

        // Get device model info
        let deviceName = deviceType.lowercased()
        print("🔍 Device model: \(deviceName)")

        // Check for ultra wide camera (0.5x)
        if backCameraDevices.contains(where: { $0.deviceType == .builtInUltraWideCamera }) {
            availableLensTypes.append(.ultraWide)
            print("✅ 添加超广角镜头 (0.5x)")
        }

        // Wide angle camera (1x) - all iPhones have this
        if backCameraDevices.contains(where: { $0.deviceType == .builtInWideAngleCamera }) {
            availableLensTypes.append(.wide)
            print("✅ 添加广角镜头 (1x)")
        }

        // Check for telephoto camera and determine specific configuration based on device model
        if backCameraDevices.contains(where: { $0.deviceType == .builtInTelephotoCamera }) {
            print("🔍 检测到长焦镜头，开始配置...")

            // Determine lens configuration based on device model
            // Use correct iPhone device identifiers
            if deviceName.contains("iphone17") { // iPhone 16 series
                if deviceName.contains("iphone17,5") {
                    // iPhone 16e: 使用与iPhone 16相同的配置 - 0.5x, 1x (无长焦)
                    print("✅ iPhone 16e: 无长焦镜头")
                } else if deviceName.contains("iphone17,2") {
                    // iPhone 16 Pro Max: 2x (digital), 5x (optical)
                    availableLensTypes.append(.telephoto)    // 2x digital zoom
                    availableLensTypes.append(.telephoto5)   // 5x optical
                    print("✅ iPhone 16 Pro Max: 添加 2x (数码) + 5x (光学)")
                } else if deviceName.contains("iphone17,1") {
                    // iPhone 16 Pro: 2x (digital), 3x (optical)
                    availableLensTypes.append(.telephoto)    // 2x digital zoom
                    availableLensTypes.append(.telephoto3)   // 3x optical
                    print("✅ iPhone 16 Pro: 添加 2x (数码) + 3x (光学)")
                } else if deviceName.contains("iphone17,3") || deviceName.contains("iphone17,4") {
                    // iPhone 16/16 Plus: 使用与iPhone 15相同的配置 - 0.5x, 1x (无长焦)
                    print("✅ iPhone 16/16 Plus: 无长焦镜头")
                }
            } else if deviceName.contains("iphone16") { // iPhone 15 series
                if deviceName.contains("iphone16,2") {
                    // iPhone 15 Pro Max: 2x (digital), 5x (optical)
                    availableLensTypes.append(.telephoto)    // 2x digital zoom
                    availableLensTypes.append(.telephoto5)   // 5x optical
                    print("✅ iPhone 15 Pro Max: 添加 2x (数码) + 5x (光学)")
                } else if deviceName.contains("iphone16,1") {
                    // iPhone 15 Pro: 2x (digital), 3x (optical)
                    availableLensTypes.append(.telephoto)    // 2x digital zoom
                    availableLensTypes.append(.telephoto3)   // 3x optical
                    print("✅ iPhone 15 Pro: 添加 2x (数码) + 3x (光学)")
                }
            } else if deviceName.contains("iphone15") { // iPhone 14 series
                if deviceName.contains("iphone15,3") {
                    // iPhone 14 Pro Max: 2x (digital), 3x (optical)
                    availableLensTypes.append(.telephoto)    // 2x digital zoom
                    availableLensTypes.append(.telephoto3)   // 3x optical
                    print("✅ iPhone 14 Pro Max: 添加 2x (数码) + 3x (光学)")
                } else if deviceName.contains("iphone15,2") {
                    // iPhone 14 Pro: 2x (digital), 3x (optical)
                    availableLensTypes.append(.telephoto)    // 2x digital zoom
                    availableLensTypes.append(.telephoto3)   // 3x optical
                    print("✅ iPhone 14 Pro: 添加 2x (数码) + 3x (光学)")
                }
            } else if deviceName.contains("iphone14") { // iPhone 13 series
                if deviceName.contains("iphone14,3") {
                    // iPhone 13 Pro Max: 2x (数码) + 3x (光学)
                    availableLensTypes.append(.telephoto)    // 2x digital zoom
                    availableLensTypes.append(.telephoto3)   // 3x optical
                    print("✅ iPhone 13 Pro Max: 添加 2x (数码) + 3x (光学)")
                } else if deviceName.contains("iphone14,2") {
                    // iPhone 13 Pro: 2x (数码) + 3x (光学)
                    availableLensTypes.append(.telephoto)    // 2x digital zoom
                    availableLensTypes.append(.telephoto3)   // 3x optical
                    print("✅ iPhone 13 Pro: 添加 2x (数码) + 3x (光学)")
                }
            } else if deviceName.contains("iphone13") { // iPhone 12 series
                if deviceName.contains("iphone13,3") {
                    // iPhone 12 Pro Max: 2.5x (optical) - treat as 2x
                    availableLensTypes.append(.telephoto)   // 2.5x optical (displayed as 2x)
                    print("✅ iPhone 12 Pro Max: 添加 2x (2.5x光学)")
                } else if deviceName.contains("iphone13,2") {
                    // iPhone 12 Pro: 2x (optical)
                    availableLensTypes.append(.telephoto)   // 2x optical
                    print("✅ iPhone 12 Pro: 添加 2x (光学)")
                }
            } else {
                // Default case: if telephoto detected but can't determine model, assume 2x
                availableLensTypes.append(.telephoto)
                print("✅ 未知机型: 添加默认 2x 长焦")
            }
        } else {
            print("❌ 未检测到长焦镜头")
        }

        // Remove duplicates
        availableLensTypes = Array(Set(availableLensTypes))

        // Sort lens types
        availableLensTypes.sort { lens1, lens2 in
            // Special handling for front camera, always put it first
            if lens1 == .front {
                return true
            }
            if lens2 == .front {
                return false
            }

            // For other lenses, sort by multiplier: 0.5x, 1x, 2x, 3x, 5x
            return lens1.rawValue < lens2.rawValue
        }

        // Set isMultiLensCamera flag
        isMultiLensCamera = availableLensTypes.count > 1

        // Make sure we have at least one lens type
        if availableLensTypes.isEmpty {
            availableLensTypes = [.wide]
        }

        // Print available lens types for debugging
        print("🎯 最终可用镜头类型: \(availableLensTypes.map { "\($0.displayName) (\($0.isDigitalZoom ? "数码" : "光学"))" }.joined(separator: ", "))")
        print("📷 多镜头相机: \(isMultiLensCamera)")

        // Notify UI to update available lens types
        onLensChanged?(currentLensType)
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        // 初始化 Metal 渲染器
        metalRenderer = MetalRenderer()

        // 初始化 FilterManager
        filterManager = FilterManager(metalRenderer: metalRenderer, videoOutput: videoOutput, initialBlackMistEnabled: true)
        filterManager?.onFilterStateChanged = { [weak self] isEnabled in
            self?.handleFilterStateChange(isEnabled: isEnabled)
        }
        filterManager?.requestUIUpdateForFilter = { [weak self] in
            self?.updateFilterImageViewVisibility()
        }

        // 初始化 CIContext
        if let device = metalRenderer?.device {
            ciContext = CIContext(mtlDevice: device)
        }

        // 设置相机
        setupCamera()

        // 监听语言变更通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleLanguageChange),
            name: .languageDidChange,
            object: nil
        )

        // 监听视频方向变更通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleVideoOrientationChange),
            name: NSNotification.Name("VideoOrientationChanged"),
            object: nil
        )

        // 设置UI
        setupUI()

        // 设置内存清理计时器
        setupMemoryCleanupTimer()

        // 设置死锁检测
        setupDeadlockDetection()
    }

    @objc private func handleLanguageChange() {
        print("CameraViewController: 接收到语言变更通知")
        // 无论相机状态如何，都强制重启会话
        print("CameraViewController: 语言变更，强制重启相机会话")
    }

    @objc private func handleVideoOrientationChange() {
        print("CameraViewController: 接收到视频方向变更通知")

        // 确保预览层方向与视频输出方向一致
        DispatchQueue.main.async { [weak self] in
            guard let self = self, let previewLayer = self.previewLayer else { return }

            // 设置预览层方向为与视频输出相同的方向
            previewLayer.connection?.videoOrientation = .landscapeRight
            print("CameraViewController: 预览层方向已设置为landscapeRight")
        }

        // 在主线程上执行，确保UI更新同步
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 先停止会话
            self.stopSession()

            // 稍后重启会话，给足够时间完成停止操作
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                guard let self = self else { return }
                self.startSession()

                // 确认会话已启动
                if !self.captureSession.isRunning {
                    print("CameraViewController: 首次尝试启动失败，再次尝试")
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                        self?.startSession()
                    }
                }
            }
        }
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        startSession()

        // 重新启动内存清理计时器
        if memoryCleanupTimer == nil {
            setupMemoryCleanupTimer()
        }

        // 重新启动死锁检测计时器
        if deadlockDetectionTimer == nil {
            setupDeadlockDetection()
        }

        // 重置帧时间
        lastFrameTime = Date()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopSession()

        // 停止内存清理计时器
        memoryCleanupTimer?.invalidate()
        memoryCleanupTimer = nil

        // 停止死锁检测计时器
        deadlockDetectionTimer?.invalidate()
        deadlockDetectionTimer = nil

        // Clean up resources when view disappears
        cleanupResources()
    }

    deinit {
        print("CameraViewController 被释放")
        // 移除通知观察者
        NotificationCenter.default.removeObserver(self)
        // 停止内存清理计时器
        memoryCleanupTimer?.invalidate()
        memoryCleanupTimer = nil
        // 停止死锁检测计时器
        deadlockDetectionTimer?.invalidate()
        deadlockDetectionTimer = nil
    }

    // Clean up resources to prevent memory leaks
    private func cleanupResources() {
        // Release CIImage reference
        currentCIImage = nil

        // Clear filter image view to release any retained images
        filterImageView?.image = nil

        // Clean up filter manager
        filterManager?.cleanup()

        // Force garbage collection for Metal resources
        metalRenderer?.cleanupTextures()

        // Clear any pending operations
        sessionQueue.async { [weak self] in
            // Ensure all pending operations complete
            self?.captureSession.commitConfiguration()
        }

        print("CameraViewController: Resources cleaned up")
    }

    // 设置内存清理计时器
    private func setupMemoryCleanupTimer() {
        // 每15秒执行一次内存清理（更频繁）
        memoryCleanupTimer = Timer.scheduledTimer(withTimeInterval: 15.0, repeats: true) { [weak self] _ in
            self?.performPeriodicMemoryCleanup()
        }
    }

    // 增加错误计数并检查是否需要开启断路器
    func incrementErrorCount() {
        errorCount += 1
        successCount = 0 // Reset success count on error
        if errorCount >= maxErrorCount {
            isCircuitBreakerOpen = true
            print("CameraViewController: 断路器已开启，停止处理帧以防止崩溃")

            // 停止相机会话
            stopSession()

            // 5秒后尝试重启
            DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) { [weak self] in
                self?.resetCircuitBreaker()
            }
        }
    }

    // 记录成功操作，用于重置错误计数
    func recordSuccessfulOperation() {
        if !isCircuitBreakerOpen {
            successCount += 1
            if successCount >= successResetThreshold && errorCount > 0 {
                errorCount = max(0, errorCount - 1) // Gradually reduce error count
                successCount = 0
            }
        }
    }

    // 重置断路器
    private func resetCircuitBreaker() {
        errorCount = 0
        isCircuitBreakerOpen = false
        print("CameraViewController: 断路器已重置，重启相机会话")
        startSession()
    }

    // 执行周期性内存清理
    @objc private func performPeriodicMemoryCleanup() {
        autoreleasepool {
            // 清理Metal纹理缓存
            metalRenderer?.cleanupTextures()

            // 清理当前图像引用
            currentCIImage = nil

            // 清理滤镜图像视图
            DispatchQueue.main.async { [weak self] in
                self?.filterImageView?.image = nil
            }

            // 强制垃圾回收
            print("CameraViewController: 执行周期性内存清理")
        }
    }

    // 设置死锁检测
    private func setupDeadlockDetection() {
        deadlockDetectionTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            self?.checkForDeadlock()
        }
    }

    // 检查死锁
    @objc private func checkForDeadlock() {
        let now = Date()
        let timeSinceLastFrame = now.timeIntervalSince(lastFrameTime)

        if timeSinceLastFrame > deadlockThreshold && captureSession.isRunning {
            print("⚠️ 检测到可能的死锁: \(timeSinceLastFrame)秒无帧处理")
            handleDeadlock()
        }
    }

    // 处理死锁
    private func handleDeadlock() {
        print("🚨 处理死锁: 强制重启相机会话")

        // 强制停止所有操作
        isCircuitBreakerOpen = true

        // 在后台队列中重启
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            // 停止会话
            self.captureSession.stopRunning()

            // 清理资源
            self.cleanupResources()

            // 等待一段时间
            Thread.sleep(forTimeInterval: 1.0)

            // 重启会话
            DispatchQueue.main.async {
                self.resetCircuitBreaker()
                self.lastFrameTime = Date() // 重置帧时间
                print("🔄 死锁恢复: 相机会话已重启")
            }
        }
    }

    // 更新帧时间（在帧处理时调用）
    func updateFrameTime() {
        lastFrameTime = Date()
    }

    // MARK: - 相机设置

    private func setupCamera() {
        // 检查相机权限
        checkCameraPermission { [weak self] granted in
            guard granted, let self = self else { return }

            self.sessionQueue.async {
                self.configureSession()
                self.startSession()
            }
        }
    }

    // 获取指定镜头类型的设备
    private func getDeviceForLensType(_ lensType: CameraLensType) -> AVCaptureDevice? {
        return AVCaptureDevice.default(lensType.deviceType, for: .video, position: .back)
    }

    // 切换到指定的镜头类型
    func switchToLens(_ lensType: CameraLensType) {
        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            self.captureSession.beginConfiguration()

            // Get device type and position
            let deviceType: AVCaptureDevice.DeviceType = lensType.deviceType
            let position: AVCaptureDevice.Position = lensType == .front ? .front : .back

            // Check if we're switching between different zoom levels on the same physical camera
            let currentPhysicalCamera = self.currentLensType.deviceType
            let targetPhysicalCamera = lensType.deviceType
            let samePosition = (self.currentLensType == .front) == (lensType == .front)

            // Special handling for digital zoom vs optical zoom
            let isCurrentDigital = self.currentLensType.isDigitalZoom
            let isTargetDigital = lensType.isDigitalZoom

            // If we're switching between digital and optical zoom levels on the same device type,
            // or if we're just changing digital zoom levels
            if currentPhysicalCamera == targetPhysicalCamera && samePosition && lensType != .front {
                if let device = self.videoDevice {
                    print("🔄 相同物理镜头切换: \(self.currentLensType.displayName) -> \(lensType.displayName)")
                    print("📸 当前变焦: \(device.videoZoomFactor)x, 目标变焦: \(lensType.zoomFactor)x")

                    self.captureSession.commitConfiguration()

                    print("切换到\(lensType.displayName) - \(lensType.isDigitalZoom ? "数码变焦" : "光学镜头")")

                    // Try direct zoom setting first as a test
                    do {
                        try device.lockForConfiguration()
                        device.videoZoomFactor = CGFloat(lensType.zoomFactor)
                        device.unlockForConfiguration()
                        print("🎯 直接设置变焦成功: \(device.videoZoomFactor)x")
                    } catch {
                        print("❌ 直接设置变焦失败: \(error.localizedDescription)")
                    }

                    // Also use smooth zoom transition for visual effect
                    // self.smoothZoomTo(factor: CGFloat(lensType.zoomFactor))

                    // Update lens type
                    self.currentLensType = lensType

                    // Notify UI update
                    DispatchQueue.main.async {
                        self.onLensChanged?(lensType)
                    }

                    return
                }
            }

            // For front camera or switching between different physical cameras
            // Remove current input
            for input in self.captureSession.inputs {
                if let deviceInput = input as? AVCaptureDeviceInput, deviceInput.device.hasMediaType(.video) {
                    self.captureSession.removeInput(deviceInput)
                }
            }

            // Get appropriate device
            var device: AVCaptureDevice?

            if lensType == .front {
                device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front)
            } else {
                device = AVCaptureDevice.default(deviceType, for: .video, position: position)
            }

            guard let device = device,
                  let videoInput = try? AVCaptureDeviceInput(device: device),
                  self.captureSession.canAddInput(videoInput) else {
                print("无法添加相机输入")
                self.captureSession.commitConfiguration()
                return
            }

            self.captureSession.addInput(videoInput)
            self.videoDevice = device
            self.currentLensType = lensType

            // Set appropriate zoom factor for the new physical camera
            do {
                try device.lockForConfiguration()
                // For digital zoom lenses, set the zoom factor
                if lensType.isDigitalZoom {
                    device.videoZoomFactor = CGFloat(lensType.zoomFactor)
                    print("设置数码变焦: \(lensType.zoomFactor)x")
                } else {
                    // For optical lenses, reset to 1.0x
                    device.videoZoomFactor = 1.0
                    print("设置光学镜头: \(lensType.displayName)")
                }
                device.unlockForConfiguration()
            } catch {
                print("Error setting zoom factor: \(error.localizedDescription)")
            }

            // Configure video output connection
            if let connection = self.videoOutput.connection(with: .video) {
                if connection.isVideoOrientationSupported {
                    connection.videoOrientation = .landscapeRight
                }

                if connection.isVideoMirroringSupported {
                    connection.automaticallyAdjustsVideoMirroring = false
                    connection.isVideoMirrored = lensType == .front
                }
            }

            // Configure preview layer connection
            if let previewLayer = self.previewLayer,
               let connection = previewLayer.connection {
                if connection.isVideoOrientationSupported {
                    connection.videoOrientation = .landscapeRight
                }

                if connection.isVideoMirroringSupported {
                    connection.automaticallyAdjustsVideoMirroring = false
                    connection.isVideoMirrored = lensType == .front
                }
            }

            self.captureSession.commitConfiguration()

            // Handle front camera orientation issue
            DispatchQueue.main.async {
                if lensType == .front {
                    self.fixFrontCameraOrientation()
                } else {
                    self.resetCameraOrientation()
                }

                print("切换到\(lensType.displayName) - \(lensType.isDigitalZoom ? "数码变焦" : "光学镜头")")

                // Notify UI update
                self.onLensChanged?(lensType)
            }
        }
    }

    // 修复前置摄像头方向问题
    private func fixFrontCameraOrientation() {
        guard let previewLayer = self.previewLayer else { return }

        // 方法1：使用CATransform3D旋转180度
        previewLayer.transform = CATransform3DMakeRotation(CGFloat.pi, 0, 0, 1)

        print("应用前置摄像头方向修正")
    }

    // 重置摄像头方向
    private func resetCameraOrientation() {
        guard let previewLayer = self.previewLayer else { return }

        // 重置变换
        previewLayer.transform = CATransform3DIdentity

        print("重置摄像头方向")
    }

    // 切换到下一个镜头
    func switchToNextLens() {
        guard isMultiLensCamera, !availableLensTypes.isEmpty else { return }

        // 找到当前镜头在可用镜头列表中的索引
        if let currentIndex = availableLensTypes.firstIndex(of: currentLensType) {
            // 计算下一个镜头的索引
            let nextIndex = (currentIndex + 1) % availableLensTypes.count
            // 切换到下一个镜头
            switchToLens(availableLensTypes[nextIndex])
        } else if let firstLens = availableLensTypes.first {
            // 如果当前镜头不在列表中，切换到第一个镜头
            switchToLens(firstLens)
        }
    }

    // 切换到上一个镜头
    func switchToPreviousLens() {
        guard isMultiLensCamera, !availableLensTypes.isEmpty else { return }

        // 找到当前镜头在可用镜头列表中的索引
        if let currentIndex = availableLensTypes.firstIndex(of: currentLensType) {
            // 计算上一个镜头的索引
            let previousIndex = (currentIndex - 1 + availableLensTypes.count) % availableLensTypes.count
            // 切换到上一个镜头
            switchToLens(availableLensTypes[previousIndex])
        } else if let firstLens = availableLensTypes.first {
            // 如果当前镜头不在列表中，切换到第一个镜头
            switchToLens(firstLens)
        }
    }

    private func checkCameraPermission(completion: @escaping (Bool) -> Void) {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            completion(true)
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    completion(granted)
                }
            }
        default:
            completion(false)
        }
    }

    private func configureSession() {
        captureSession.beginConfiguration()

        // 设置分辨率 - 使用最高质量
        if captureSession.canSetSessionPreset(.photo) {
            captureSession.sessionPreset = .photo
        } else if captureSession.canSetSessionPreset(.high) {
            captureSession.sessionPreset = .high
        }

        // 初始化可用镜头类型
        initializeAvailableLensTypes()

        // 选择当前镜头类型
        let selectedLensType = availableLensTypes.contains(currentLensType) ? currentLensType : .wide

        // 添加视频输入
        guard let device = getDeviceForLensType(selectedLensType),
              let videoInput = try? AVCaptureDeviceInput(device: device),
              captureSession.canAddInput(videoInput) else {
            print("无法添加相机输入")
            captureSession.commitConfiguration()
            return
        }

        // 保存视频设备引用
        self.videoDevice = device
        self.currentLensType = selectedLensType

        captureSession.addInput(videoInput)

        // 添加音频输入设备
        if let audioDevice = AVCaptureDevice.default(for: .audio),
           let audioInput = try? AVCaptureDeviceInput(device: audioDevice),
           captureSession.canAddInput(audioInput) {
            captureSession.addInput(audioInput)
            print("音频输入已添加到捕获会话")
        } else {
            print("无法添加音频输入设备")
        }

        // 添加视频输出
        if captureSession.canAddOutput(videoOutput) {
            captureSession.addOutput(videoOutput)
            videoOutput.videoSettings = [kCVPixelBufferPixelFormatTypeKey as String: Int(kCVPixelFormatType_32BGRA)]
            videoOutput.setSampleBufferDelegate(self, queue: sessionQueue)

            // 设置视频方向为固定横屏模式
            if let connection = videoOutput.connection(with: .video) {
                if connection.isVideoOrientationSupported {
                    connection.videoOrientation = .landscapeRight
                }

                // 禁用自动视频方向，强制使用固定方向
                connection.automaticallyAdjustsVideoMirroring = false

                // 启用视频稳定（如果支持）
                if connection.isVideoStabilizationSupported {
                    connection.preferredVideoStabilizationMode = .auto
                }

                // 如果支持视频镜像，确保前置摄像头是镜像的，后置摄像头不是镜像的
                if connection.isVideoMirroringSupported {
                    if let device = self.videoDevice, device.position == .front {
                        connection.isVideoMirrored = true
                    } else {
                        connection.isVideoMirrored = false
                    }
                }
            }
        }

        // 添加视频录制输出
        setupVideoRecording()
        if let connection = movieFileOutput.connection(with: .video), connection.isVideoOrientationSupported {
            connection.videoOrientation = .landscapeRight

            // 如果支持视频镜像，确保前置摄像头是镜像的，后置摄像头不是镜像的
            if connection.isVideoMirroringSupported {
                if let device = self.videoDevice, device.position == .front {
                    connection.isVideoMirrored = true
                } else {
                    connection.isVideoMirrored = false
                }
            }
        }

        // 添加音频输出，用于AssetWriter录制
        if captureSession.canAddOutput(audioOutput) {
            captureSession.addOutput(audioOutput)
            audioOutput.setSampleBufferDelegate(self, queue: audioSampleBufferQueue)
            print("音频输出已添加到捕获会话")
        } else {
            print("无法添加音频输出到捕获会话")
        }

        // 注意：我们不再使用 photoOutput，因为我们现在直接从视频流截图

        captureSession.commitConfiguration()

        // 设置通知，在会话中断或结束时重新配置方向
        NotificationCenter.default.addObserver(self,
                                              selector: #selector(sessionRuntimeError),
                                              name: .AVCaptureSessionRuntimeError,
                                              object: captureSession)
        NotificationCenter.default.addObserver(self,
                                              selector: #selector(sessionWasInterrupted),
                                              name: .AVCaptureSessionWasInterrupted,
                                              object: captureSession)
        NotificationCenter.default.addObserver(self,
                                              selector: #selector(sessionInterruptionEnded),
                                              name: .AVCaptureSessionInterruptionEnded,
                                              object: captureSession)
    }

    // 启动会话的计数器，用于减少日志频率
    private var startSessionCount = 0

    // 公开方法，用于外部调用启动相机会话
    func startSession() {
        startSessionCount += 1

        // 减少日志输出频率 - 每5次调用打印一次
        if startSessionCount % 5 == 1 {
            print("CameraViewController: 启动相机会话 (第\(startSessionCount)次调用)")
        }

        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            if !self.captureSession.isRunning {
                self.captureSession.startRunning()

                DispatchQueue.main.async {
                    // Update the shutter speed display when the camera starts
                    self.updateShutterSpeedDisplay()
                    if self.startSessionCount % 5 == 1 {
                        print("CameraViewController: 相机会话已启动")
                    }
                }
            } else {
                if self.startSessionCount % 5 == 1 {
                    print("CameraViewController: 相机会话已经在运行")
                }
            }
        }
    }

    // 停止相机会话
    func stopSession() {
        print("CameraViewController: 停止相机会话")
        sessionQueue.async { [weak self] in
            guard let self = self else { return }

            if self.captureSession.isRunning {
                self.captureSession.stopRunning()
                DispatchQueue.main.async {
                    print("CameraViewController: 相机会话已停止")
                }
            } else {
                print("CameraViewController: 相机会话已经停止")
            }
        }
    }

    // MARK: - 滤镜控制

    func toggleBlackMist() -> Bool {
        guard let fm = filterManager else { return false }
        return fm.toggleBlackMist()
    }

    private func handleFilterStateChange(isEnabled: Bool) {
        // This method is called by FilterManager's onFilterStateChanged callback
        currentCIImage = nil // Clear current image when filter state changes
        if !isEnabled {
            // 如果禁用了滤镜，清除滤镜图像
            DispatchQueue.main.async { [weak self] in
                self?.filterImageView?.image = nil
            }
        }

        // 通知外部滤镜状态变化
        onFilterStateChanged?(isEnabled)
    }


    func setBlackMistThreshold(_ value: Float) {
        // 拍照过程中暂时跳过参数调整，防止冲突
        guard !isCapturingPhoto else {
            print("拍照中，跳过高光阈值调整")
            return
        }
        filterManager?.blackMistThreshold = value
    }

    func setBlackMistIntensity(_ value: Float) {
        // 拍照过程中暂时跳过参数调整，防止冲突
        guard !isCapturingPhoto else {
            print("拍照中，跳过黑柔强度调整")
            return
        }
        filterManager?.blackMistIntensity = value
    }

    func setBrightness(_ value: Float) {
        // 拍照过程中暂时跳过参数调整，防止冲突
        guard !isCapturingPhoto else {
            print("拍照中，跳过亮度调整")
            return
        }
        filterManager?.brightness = value
    }

    func setRedTint(_ value: Float) {
        filterManager?.redTint = value
    }

    func setGreenTint(_ value: Float) {
        filterManager?.greenTint = value
    }

    func setBlueTint(_ value: Float) {
        filterManager?.blueTint = value
    }

    // MARK: - Flash Control
    func setFlashMode(_ mode: AVCaptureDevice.FlashMode) {
        flashMode = mode
        print("设置闪光灯模式为: \(flashMode == .on ? "强制闪光" : flashMode == .auto ? "自动" : "关闭")")

        // 更新设备的闪光灯/手电筒模式
        sessionQueue.async { [weak self] in
            guard let self = self, let device = self.videoDevice else { return }

            do {
                try device.lockForConfiguration()

                // 如果正在录像，使用手电筒模式
                if self.isRecording {
                    if device.hasTorch && device.isTorchAvailable {
                        switch mode {
                        case .on:
                            try device.setTorchModeOn(level: AVCaptureDevice.maxAvailableTorchLevel)
                            print("录像中：手电筒已打开")
                        case .off:
                            device.torchMode = .off
                            print("录像中：手电筒已关闭")
                        case .auto:
                            // 自动模式下，根据环境光线决定是否打开手电筒
                            if self.shouldUseAutoFlash() {
                                try device.setTorchModeOn(level: AVCaptureDevice.maxAvailableTorchLevel)
                                print("录像中：自动模式下打开手电筒")
                            } else {
                                device.torchMode = .off
                                print("录像中：自动模式下关闭手电筒")
                            }
                        @unknown default:
                            device.torchMode = .off
                        }
                    }
                } else {
                    // 未录像时，确保手电筒关闭，只在拍照时闪一下
                    if device.hasTorch && device.isTorchAvailable {
                        device.torchMode = .off
                    }
                }

                device.unlockForConfiguration()
            } catch {
                print("无法配置设备闪光灯/手电筒: \(error.localizedDescription)")
            }
        }
    }

    // MARK: - Video Recording
    func setupVideoRecording() {
        if captureSession.canAddOutput(movieFileOutput) {
            captureSession.addOutput(movieFileOutput)
            print("视频录制输出已配置")
        }
    }

    func startRecording() {
        guard !isRecording else { return }

        // 配置音频会话
        configureAudioSession()

        // 创建临时文件URL
        let tempDir = FileManager.default.temporaryDirectory
        let fileURL = tempDir.appendingPathComponent("recording_\(Date().timeIntervalSince1970).mov")
        currentVideoURL = fileURL

        // 如果启用了黑柔效果，使用AssetWriter录制处理后的视频
        if filterManager?.isBlackMistEnabled ?? false {
            if setupAssetWriter(outputURL: fileURL) {
                startTime = nil  // 重置开始时间，让音频和视频同步
                isRecording = true
                print("开始录制带滤镜的视频：\(fileURL.path)")
            } else {
                print("设置AssetWriter失败，无法开始录制")
                // 尝试回退到标准录制方式
                movieFileOutput.startRecording(to: fileURL, recordingDelegate: self)
                isRecording = true
                print("回退到标准视频录制：\(fileURL.path)")
            }
        } else {
            // 未启用黑柔效果时使用标准录制
            movieFileOutput.startRecording(to: fileURL, recordingDelegate: self)
            isRecording = true
            print("开始录制标准视频：\(fileURL.path)")
        }

        // 录像开始后，根据当前闪光灯模式设置手电筒
        setFlashMode(flashMode)
    }

    func stopRecording() {
        guard isRecording else { return }

        isRecording = false

        // 录像停止后，确保关闭手电筒
        sessionQueue.async { [weak self] in
            guard let self = self, let device = self.videoDevice else { return }

            do {
                try device.lockForConfiguration()
                if device.hasTorch && device.isTorchAvailable && device.torchMode == .on {
                    device.torchMode = .off
                    print("录像停止：手电筒已关闭")
                }
                device.unlockForConfiguration()
            } catch {
                print("录像停止时无法关闭手电筒: \(error.localizedDescription)")
            }
        }

        if let assetWriter = assetWriter, assetWriter.status == .writing {
            // 停止使用AssetWriter录制
            assetWriterVideoInput?.markAsFinished()
            assetWriterAudioInput?.markAsFinished() // 标记音频输入为完成

            // 添加延迟以确保所有数据都写入完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                assetWriter.finishWriting { [weak self] in
                    guard let self = self, let fileURL = self.currentVideoURL else { return }
                    print("完成录制带滤镜的视频")

                    // 通知视频已完成录制
                    DispatchQueue.main.async {
                        self.onVideoCaptured?(true, fileURL)
                    }

                    // 将视频保存到相册
                    self.saveVideoToPhotoLibrary(fileURL: fileURL)

                    // 清理资源
                    self.cleanupAssetWriter()
                }
            }
        } else {
            // 停止标准录制
            movieFileOutput.stopRecording()
            print("停止录制标准视频")
        }
    }



    private func setupAssetWriter(outputURL: URL) -> Bool {
        do {
            // 创建AVAssetWriter
            assetWriter = try AVAssetWriter(outputURL: outputURL, fileType: .mov)

            // 设置共同的时间源，确保音频和视频同步
            assetWriter?.movieTimeScale = CMTimeScale(600)

            // 获取视频尺寸
            var size = CGSize(width: 1920, height: 1080) // 默认尺寸作为后备

            if let videoConnection = videoOutput.connection(with: .video) {
                // 从视频会话预设获取尺寸
                switch captureSession.sessionPreset {
                case .hd4K3840x2160: size = CGSize(width: 3840, height: 2160)
                case .hd1920x1080: size = CGSize(width: 1920, height: 1080)
                case .hd1280x720: size = CGSize(width: 1280, height: 720)
                case .vga640x480: size = CGSize(width: 640, height: 480)
                case .photo: size = CGSize(width: 4032, height: 3024) // 典型iPhone照片尺寸
                default: size = CGSize(width: 1920, height: 1080)
                }

                // 确保正确的方向
                if videoConnection.isVideoOrientationSupported {
                    let videoOrientation = videoConnection.videoOrientation
                    if videoOrientation == .landscapeLeft || videoOrientation == .landscapeRight {
                        // 保持风景模式尺寸
                    } else {
                        // 交换宽高以适应肖像模式
                        size = CGSize(width: size.height, height: size.width)
                    }
                }
            }

            print("设置视频录制尺寸: \(size.width)x\(size.height)")

            // 创建视频输入
            let videoSettings: [String: Any] = [
                AVVideoCodecKey: AVVideoCodecType.h264,
                AVVideoWidthKey: size.width,
                AVVideoHeightKey: size.height,
                AVVideoCompressionPropertiesKey: [
                    AVVideoAverageBitRateKey: 5000000,
                    AVVideoProfileLevelKey: AVVideoProfileLevelH264HighAutoLevel
                ]
            ]

            assetWriterVideoInput = AVAssetWriterInput(mediaType: .video, outputSettings: videoSettings)
            assetWriterVideoInput?.expectsMediaDataInRealTime = true

            // 确保输入可以添加到writer
            if let assetWriterVideoInput = assetWriterVideoInput, assetWriter?.canAdd(assetWriterVideoInput) == true {
                assetWriter?.add(assetWriterVideoInput)
            } else {
                print("无法添加视频输入到assetWriter")
                cleanupAssetWriter()
                return false
            }

            // 添加音频输入
            let audioSettings: [String: Any] = [
                AVFormatIDKey: kAudioFormatMPEG4AAC,
                AVSampleRateKey: 44100,
                AVNumberOfChannelsKey: 2,
                AVEncoderBitRateKey: 128000,
                AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
            ]

            let assetWriterAudioInput = AVAssetWriterInput(mediaType: .audio, outputSettings: audioSettings)
            assetWriterAudioInput.expectsMediaDataInRealTime = true

            if assetWriter?.canAdd(assetWriterAudioInput) == true {
                assetWriter?.add(assetWriterAudioInput)
                self.assetWriterAudioInput = assetWriterAudioInput
                print("音频输入已添加到AssetWriter")
            } else {
                print("无法添加音频输入到AssetWriter，视频将没有声音")
            }

            // 创建像素缓冲适配器
            let sourcePixelBufferAttributes: [String: Any] = [
                kCVPixelBufferPixelFormatTypeKey as String: Int(kCVPixelFormatType_32BGRA),
                kCVPixelBufferWidthKey as String: size.width,
                kCVPixelBufferHeightKey as String: size.height,
                kCVPixelBufferCGImageCompatibilityKey as String: true,
                kCVPixelBufferCGBitmapContextCompatibilityKey as String: true
            ]

            assetWriterPixelBufferAdaptor = AVAssetWriterInputPixelBufferAdaptor(
                assetWriterInput: assetWriterVideoInput!,
                sourcePixelBufferAttributes: sourcePixelBufferAttributes
            )

            // 开始写入
            assetWriter?.startWriting()
            assetWriter?.startSession(atSourceTime: .zero)

            return true

        } catch {
            print("设置AVAssetWriter失败: \(error.localizedDescription)")
            cleanupAssetWriter()
            return false
        }
    }

    private func cleanupAssetWriter() {
        print("清理AssetWriter资源")
        assetWriter = nil
        assetWriterVideoInput = nil
        assetWriterAudioInput = nil
        assetWriterPixelBufferAdaptor = nil
        currentVideoURL = nil
        startTime = nil
    }

    // MARK: - 拍照功能

    func capturePhoto() {
        guard captureSession.isRunning, isReady else {
            print("相机会话未运行或相机未就绪")
            onPhotoCaptured?(false, nil)
            return
        }

        // 检查是否正在拍照，防止重复调用
        guard !isCapturingPhoto else {
            print("正在拍照中，忽略重复请求")
            return
        }

        // 设置拍照状态保护
        captureProtectionQueue.async { [weak self] in
            self?.isCapturingPhoto = true
        }

        print("开始拍照 - 使用视频流截图")

        // 处理闪光灯
        let shouldUseFlash = flashMode == .on || (flashMode == .auto && shouldUseAutoFlash())

        // 如果需要使用闪光灯，先闪一下再拍照
        if shouldUseFlash {
            triggerFlash { [weak self] in
                self?.completePhotoCapture()
            }
        } else {
            // 不使用闪光灯，直接拍照
            completePhotoCapture()
        }
    }

    // 判断自动模式下是否应该使用闪光灯
    private func shouldUseAutoFlash() -> Bool {
        // 这里可以添加光线检测逻辑，目前简化处理
        // 如果光线低，则返回true
        print("自动闪光灯模式: 根据光线决定是否使用闪光灯")
        return false // 简化处理，默认不使用
    }

    // 触发闪光灯效果
    private func triggerFlash(completion: @escaping () -> Void) {
        guard let device = videoDevice, device.hasTorch && device.isTorchAvailable else {
            print("设备不支持闪光灯")
            completion()
            return
        }

        sessionQueue.async { [weak self] in
            do {
                // 锁定设备配置
                try device.lockForConfiguration()

                // 开启闪光灯
                try device.setTorchModeOn(level: AVCaptureDevice.maxAvailableTorchLevel)
                print("闪光灯已开启，准备拍照")

                // 播放快门声音
                DispatchQueue.main.async {
                    AudioManager.shared.playShutterSound()
                }

                // 短暂延迟后关闭闪光灯
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    do {
                        try device.lockForConfiguration()
                        device.torchMode = .off
                        device.unlockForConfiguration()
                        print("闪光灯已关闭")

                        // 完成拍照
                        completion()
                    } catch {
                        print("无法关闭闪光灯: \(error.localizedDescription)")
                        completion()
                    }
                }

                device.unlockForConfiguration()
            } catch {
                print("无法开启闪光灯: \(error.localizedDescription)")
                completion()
            }
        }
    }

    // 完成拍照过程
    private func completePhotoCapture() {
        // 播放快门声音
        DispatchQueue.main.async {
            AudioManager.shared.playShutterSound()
        }

        // 获取当前视频帧作为照片
        var photoImage: UIImage? = nil

        // 如果有滤镜效果，使用当前处理后的图像
        if filterManager?.isBlackMistEnabled ?? false, let currentCIImage = self.currentCIImage {
            print("使用滤镜效果拍照")

            // 将CIImage转换为UIImage
            if let cgImage = ciContext?.createCGImage(currentCIImage, from: currentCIImage.extent) {
                photoImage = UIImage(cgImage: cgImage, scale: 1.0, orientation: .downMirrored)
            } else {
                print("拍照失败: 无法从CIImage创建CGImage")
                onPhotoCaptured?(false, nil)
                return
            }
        } else if let currentCIImage = self.currentCIImage { // 无滤镜效果，但有当前图像
            print("使用当前图像拍照")

            // 将CIImage转换为UIImage
            if let cgImage = ciContext?.createCGImage(currentCIImage, from: currentCIImage.extent) {
                photoImage = UIImage(cgImage: cgImage, scale: 1.0, orientation: .downMirrored)
            } else {
                print("拍照失败: 无法从CIImage创建CGImage")
                onPhotoCaptured?(false, nil)
                return
            }
        } else {
            // 如果没有滤镜效果，直接从预览层捕获
            print("从预览层捕获图像")
            photoImage = getCurrentUIImage()
        }

        // 检查是否成功获取图像
        guard let finalImage = photoImage else {
            print("无法获取照片图像")
            onPhotoCaptured?(false, nil)
            return
        }

        // 获取当前照片质量设置
        let photoQuality = UserDefaults.standard.integer(forKey: "photoQuality")
        let quality = PhotoQuality(rawValue: photoQuality) ?? .maximum

        // 获取当前位置（如果启用了位置服务）
        let location = LocationManager.shared.locationEnabled ? LocationManager.shared.currentLocation : nil

        // 获取当前相机设备的 ISO 和光圈值（如果可用）
        var deviceISO: Double = 100.0
        var deviceAperture: Double = 2.8

        if let device = videoDevice {
            // 获取实际的 ISO 值
            deviceISO = Double(device.iso)

            // 如果 ISO 值太低，使用基于滤镜强度的动态值
            if deviceISO < 50 {
                deviceISO = (filterManager?.isBlackMistEnabled ?? false) ? 100.0 + (Double((filterManager?.blackMistIntensity ?? 0)) * 400.0) : 100.0
            }
        } else {
            // 如果无法获取设备信息，使用基于滤镜强度的动态值
            deviceISO = (filterManager?.isBlackMistEnabled ?? false) ? 100.0 + (Double((filterManager?.blackMistIntensity ?? 0)) * 400.0) : 100.0
        }

        // 设置快门速度
        let dynamicShutterSpeed = (filterManager?.isBlackMistEnabled ?? false) ? 1.0/(60.0 + Double((filterManager?.blackMistIntensity ?? 0)) * 60.0) : 1.0/60.0

        // 根据镜头类型调整光圈值 - 使用 iPhone 的真实光圈值
        let dynamicAperture: Double
        switch currentLensType {
        case .front:
            dynamicAperture = 2.2  // iPhone 前置摄像头的光圈值
        case .ultraWide:
            dynamicAperture = 2.4  // iPhone 超广角镜头的光圈值
        case .wide:
            dynamicAperture = 1.8  // iPhone 广角镜头的光圈值
        case .telephoto:
            dynamicAperture = 2.0  // iPhone 2.5倍长焦镜头的光圈值
        case .telephoto3:
            dynamicAperture = 2.0  // iPhone 3倍长焦镜头的光圈值
        case .telephoto5:
            dynamicAperture = 2.8  // iPhone 5倍长焦镜头的光圈值
        }

        // 计算曝光补偿值 - 根据亮度参数计算
        // 亮度范围从 -0.17 到 +0.17，映射到 -2.0 到 **** EV
        let dynamicExposureComp = Double((filterManager?.brightness ?? 0)) * (2.0 / 0.17)

        // 更新当前相机参数
        currentISO = deviceISO
        currentAperture = dynamicAperture
        currentShutterSpeed = dynamicShutterSpeed
        currentExposureCompensation = dynamicExposureComp

        // Update the display with current shutter speed
        updateShutterSpeedDisplay()

        // 创建唯一文件名用于应用内存储
        let appFileName = "image_\(Date().timeIntervalSince1970).jpg"

        // 1. 首先保存到应用内存储
        AppMediaStorage.shared.saveImageToApp(finalImage) { [weak self] appSuccess, appFileURL in
            guard let self = self else { return }

            if appSuccess, let appFileURL = appFileURL {
                print("照片已保存到应用内存储: \(appFileURL.path)")

                // 获取文件名
                let fileName = appFileURL.lastPathComponent

                // 2. 然后保存到系统相册（与原来的功能保持一致）
                MediaSaver.shared.saveHighQualityPhoto(
                    image: finalImage,
                    location: location,
                    lensType: self.currentLensType,
                    quality: quality,
                    iso: self.currentISO,
                    aperture: self.currentAperture,
                    shutterSpeed: self.currentShutterSpeed,
                    exposureCompensation: self.currentExposureCompensation,
                    // 添加额外的闭包参数，接收文件名和标识符
                    onAssetSaved: { (assetIdentifier) in
                        // 使用应用内文件名和系统相册标识符建立映射关系
                        if let assetId = assetIdentifier {
                            print("建立照片映射关系: \(fileName) -> \(assetId)")
                            AppMediaStorage.shared.recordPhotoAssetMapping(appFileName: fileName, assetLocalIdentifier: assetId)
                        }
                    }
                ) { success, fileURL in
                    if success {
                        print("照片已保存到系统相册")
                        print("ISO: \(Int(self.currentISO))")
                        print("光圈: \(String(format: "f/%.1f", self.currentAperture))")
                        print("快门速度: \(self.formatShutterSpeed(self.currentShutterSpeed))")
                        print("曝光补偿: \(String(format: "%.1f EV", self.currentExposureCompensation))")
                        print("焦距: \(self.currentLensType.focalLength)mm")
                        print("质量: \(quality)")

                        // 回调通知照片已成功捕获
                        self.onPhotoCaptured?(true, finalImage)
                    } else {
                        print("保存照片失败")
                        self.onPhotoCaptured?(false, nil)
                    }

                    // 重置拍照状态保护
                    self.captureProtectionQueue.async {
                        self.isCapturingPhoto = false
                    }
                }
            } else {
                print("保存照片到应用内存储失败")

                // 如果应用内保存失败，仍然尝试保存到系统相册
                MediaSaver.shared.saveHighQualityPhoto(
                    image: finalImage,
                    location: location,
                    lensType: self.currentLensType,
                    quality: quality,
                    iso: self.currentISO,
                    aperture: self.currentAperture,
                    shutterSpeed: self.currentShutterSpeed,
                    exposureCompensation: self.currentExposureCompensation
                ) { success, fileURL in
                    if success {
                        print("照片已保存到系统相册（应用内保存失败）")
                        self.onPhotoCaptured?(true, finalImage)
                    } else {
                        print("保存照片失败")
                        self.onPhotoCaptured?(false, nil)
                    }

                    // 重置拍照状态保护
                    self.captureProtectionQueue.async {
                        self.isCapturingPhoto = false
                    }
                }
            }
        }
    }

    // 布局相关代码已移至 CameraViewControllerLayout.swift

    // 方向控制相关代码已移至 CameraViewControllerLayout.swift

    // 添加会话中断相关处理方法
    @objc private func sessionRuntimeError(notification: NSNotification) {
        // 处理会话运行时错误
        if let error = notification.userInfo?[AVCaptureSessionErrorKey] as? Error {
            print("相机会话运行时错误: \(error.localizedDescription)")
        } else {
            print("相机会话运行时错误: 未知错误")
        }

        // 尝试重新启动会话
        sessionQueue.async { [weak self] in
            self?.captureSession.startRunning()
        }

        // 尝试重新设置视频方向
        resetVideoOrientation()
    }

    @objc private func sessionWasInterrupted(notification: NSNotification) {
        // 处理会话中断
        print("相机会话被中断")
    }

    @objc private func sessionInterruptionEnded(notification: NSNotification) {
        // 处理会话中断结束
        print("相机会话中断结束")

        // 恢复后确保视频方向正确
        resetVideoOrientation()
    }

    // 视频方向重置方法已移至 CameraViewControllerLayout.swift

    // 获取当前屏幕图像
    private func getCurrentUIImage() -> UIImage? {
        // 方法1: 如果有最近的CIImage(即使滤镜未启用)，使用它
        if let ciImage = self.currentCIImage {
            let context = CIContext()
            if let cgImage = context.createCGImage(ciImage, from: ciImage.extent) {
                return UIImage(cgImage: cgImage, scale: 1.0, orientation: currentLensType == .front ? .upMirrored : .down)
            }
        }

        // 方法2: 从预览层捕获(这种方法可能无法在某些设备上正常工作)
        if let layer = self.previewLayer {
            UIGraphicsBeginImageContextWithOptions(layer.frame.size, false, UIScreen.main.scale)
            if let context = UIGraphicsGetCurrentContext() {
                layer.render(in: context)
                let image = UIGraphicsGetImageFromCurrentImageContext()
                UIGraphicsEndImageContext()

                if let image = image, !isImageEmpty(image) {
                    return image
                }
            }
            UIGraphicsEndImageContext()
        }

        // 方法3: 如果上述方法都失败，尝试生成一个临时测试图像(防止返回空图像)
        print("警告: 无法从预览层捕获图像，生成临时图像")
        return createPlaceholderImage()
    }

    // 检查图像是否为空白图像
    private func isImageEmpty(_ image: UIImage) -> Bool {
        // 将图像转换为灰度
        let context = CIContext()
        guard let cgImage = image.cgImage,
              let grayImage = context.createCGImage(CIImage(cgImage: cgImage), from: CGRect(origin: .zero, size: image.size)) else {
            return true
        }

        // 采样检查像素值
        let bytesPerRow = grayImage.bytesPerRow
        let width = grayImage.width
        let height = grayImage.height
        let bytesPerPixel = 4
        let totalBytes = width * height * bytesPerPixel

        // 采样点数(检查整个图像太慢，只采样部分)
        let samplingPercent = 0.01
        let samplingPoints = Int(Double(width * height) * samplingPercent)

        guard let data = CFDataCreateMutableCopy(nil, totalBytes, grayImage.dataProvider?.data) else {
            return true
        }

        var byteData = [UInt8](repeating: 0, count: totalBytes)
        CFDataGetBytes(data, CFRange(location: 0, length: totalBytes), &byteData)

        // 计算非黑色像素的比例
        var nonBlackPixelCount = 0
        for _ in 0..<samplingPoints {
            let randomX = Int.random(in: 0..<width)
            let randomY = Int.random(in: 0..<height)
            let offset = randomY * bytesPerRow + randomX * bytesPerPixel

            // 检查BGRA数据的亮度
            if offset + 2 < byteData.count {
                let b = Float(byteData[offset])
                let g = Float(byteData[offset + 1])
                let r = Float(byteData[offset + 2])

                // 如果像素不是纯黑色(给一些容差)
                if r > 10 || g > 10 || b > 10 {
                    nonBlackPixelCount += 1
                }
            }
        }

        // 如果非黑色像素比例低于10%，认为是空白图像
        return Double(nonBlackPixelCount) / Double(samplingPoints) < 0.1
    }

    // 创建一个包含当前相机参数的占位图像
    private func createPlaceholderImage() -> UIImage {
        let size = CGSize(width: 2048, height: 1536) // 3MP图像
        UIGraphicsBeginImageContextWithOptions(size, true, 1.0)
        let context = UIGraphicsGetCurrentContext()!

        // 绘制当前相机的视图
        context.setFillColor(UIColor.black.cgColor)
        context.fill(CGRect(origin: .zero, size: size))

        // 添加时间戳和镜头信息
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center

        let attrs: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 24),
            .foregroundColor: UIColor.white,
            .paragraphStyle: paragraphStyle
        ]

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        let dateString = dateFormatter.string(from: Date())
        let infoString = "镜头: \(currentLensType.displayName)\n时间: \(dateString)"

        let textRect = CGRect(x: 0, y: size.height/2 - 50, width: size.width, height: 100)
        infoString.draw(in: textRect, withAttributes: attrs)

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return image ?? UIImage()
    }

    // 配置音频会话
    private func configureAudioSession() {
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playAndRecord, mode: .videoRecording, options: [.allowBluetooth, .defaultToSpeaker])
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
            print("音频会话已配置为录制模式")
        } catch {
            print("[音频] 配置音频会话失败: \(error.localizedDescription)")
        }
    }

    // Add this method to update the shutter speed display
    func updateShutterSpeedDisplay() {
        // Format and update the shutter speed display
        _ = formatShutterSpeed(currentShutterSpeed)
    }

    // Add these properties to the CameraViewController class
    private var zoomTimer: Timer?
    private var targetZoomFactor: CGFloat = 1.0
    private var startZoomFactor: CGFloat = 1.0
    private var zoomDuration: TimeInterval = 0.3
    private var zoomStartTime: Date?

    // Add a method for smooth zooming
    private func smoothZoomTo(factor: CGFloat, duration: TimeInterval = 0.3) {
        guard let device = videoDevice else {
            print("❌ smoothZoomTo: 没有视频设备")
            return
        }

        print("🎯 开始平滑变焦: 从 \(device.videoZoomFactor)x 到 \(factor)x")

        do {
            try device.lockForConfiguration()

            // Store the current zoom factor as starting point
            startZoomFactor = device.videoZoomFactor
            targetZoomFactor = factor
            zoomDuration = duration
            zoomStartTime = Date()

            // Invalidate existing timer if any
            zoomTimer?.invalidate()

            device.unlockForConfiguration()

            // Create new timer for smooth animation
            zoomTimer = Timer.scheduledTimer(withTimeInterval: 0.01, repeats: true) { [weak self] timer in
                guard let self = self, let device = self.videoDevice, let startTime = self.zoomStartTime else {
                    timer.invalidate()
                    return
                }

                let elapsedTime = Date().timeIntervalSince(startTime)

                // Calculate progress (0 to 1)
                let progress = min(1.0, elapsedTime / self.zoomDuration)

                // Use easing function for smoother transition
                let easedProgress = self.easeInOutQuad(progress)

                // Calculate current zoom factor based on progress
                let currentZoom = self.startZoomFactor + (self.targetZoomFactor - self.startZoomFactor) * CGFloat(easedProgress)

                // Apply zoom factor with proper locking
                do {
                    try device.lockForConfiguration()
                    device.videoZoomFactor = currentZoom
                    device.unlockForConfiguration()
                } catch {
                    print("Error setting zoom factor in timer: \(error.localizedDescription)")
                }

                // If zoom animation is complete, invalidate timer
                if progress >= 1.0 {
                    timer.invalidate()
                    self.zoomTimer = nil
                    print("✅ 变焦完成: \(currentZoom)x")
                }
            }

        } catch {
            print("Error setting up zoom transition: \(error.localizedDescription)")
        }
    }

    // Add easing function for smoother zoom transition
    private func easeInOutQuad(_ x: Double) -> Double {
        return x < 0.5 ? 2 * x * x : 1 - pow(-2 * x + 2, 2) / 2
    }

    // 移除了捏合缩放手势处理方法以防止卡死问题
    // 捏合缩放功能已被禁用以提高稳定性和防止与Metal渲染的冲突
}
