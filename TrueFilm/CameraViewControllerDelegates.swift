//
//  CameraViewControllerDelegates.swift
//  TrueFilm
//
//  Created on 2025/5/7.
//

import UIKit
import AVFoundation
import Metal
import MetalKit
import MetalPerformanceShaders
import CoreLocation
import Photos

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate

extension CameraViewController: AVCaptureVideoDataOutputSampleBufferDelegate, AVCaptureAudioDataOutputSampleBufferDelegate {
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        // 区分视频和音频样本缓冲区
        if output is AVCaptureVideoDataOutput {
            // 处理视频样本缓冲区
            processVideoSampleBuffer(sampleBuffer, from: connection)
        } else if output is AVCaptureAudioDataOutput {
            // 处理音频样本缓冲区
            processAudioSampleBuffer(sampleBuffer)
        }
    }

    // 处理视频样本缓冲区
    private func processVideoSampleBuffer(_ sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        autoreleasepool {
            // 检查断路器状态
            if isCircuitBreakerOpen {
                return
            }

            // 确保 metalRenderer 存在
            guard let renderer = metalRenderer else {
                // 减少日志频率 - 每 300 帧打印一次
                frameCount += 1
                if frameCount % 300 == 0 {
                    print("CameraViewController captureOutput: metalRenderer 为空")
                }
                incrementErrorCount()
                return
            }

        // 更新帧计数器（减少日志输出频率）
        frameCount += 1

        // 减少调试信息输出频率 - 每 300 帧打印一次（约5秒一次）
        if frameCount % 300 == 0 {
            print("CameraViewController captureOutput: 当前黑柔强度为 \(filterManager?.blackMistIntensity ?? 0), 高光阈值为 \(filterManager?.blackMistThreshold ?? 0), 亮度为 \(filterManager?.brightness ?? 0)")

            // Update the shutter speed display every 300 frames
            updateShutterSpeedDisplay()
        }

        // 将 CMSampleBuffer 转换为 Metal 纹理
        guard let inputTexture = renderer.texture(from: sampleBuffer) else {
            if frameCount % 300 == 0 {
                print("CameraViewController captureOutput: 无法从样本缓冲区创建纹理")
            }
            incrementErrorCount()
            return
        }

        // 如果正在录制并且启用了黑柔滤镜，将处理后的帧写入到视频文件
        if isRecording && filterManager?.isBlackMistEnabled ?? false && assetWriterVideoInput?.isReadyForMoreMediaData == true {
            processAndRecordVideoFrame(sampleBuffer: sampleBuffer, metalTexture: inputTexture, metalRenderer: renderer)
        }

        // 处理帧
        guard let outputTexture = renderer.processFrame(texture: inputTexture) else {
            if frameCount % 300 == 0 {
                print("CameraViewController captureOutput: Metal渲染器处理帧失败")
            }
            incrementErrorCount()
            return
        }

        // 将处理后的纹理转换为 CIImage
        guard let ciImage = CIImage(mtlTexture: outputTexture, options: [CIImageOption.colorSpace: CGColorSpaceCreateDeviceRGB()]) else {
            if frameCount % 300 == 0 {
                print("CameraViewController captureOutput: 无法从Metal纹理创建CIImage")
            }
            incrementErrorCount()
            return
        }

        // 保存当前图像以供预览层和拍照使用(无论是否启用黑柔滤镜)
        autoreleasepool {
            self.currentCIImage = ciImage
        }

        // 记录成功操作
        recordSuccessfulOperation()

        // 更新帧时间以防止死锁检测误报
        updateFrameTime()

        // 无论是否启用黑柔滤镜，都显示处理后的图像以确保方向正确
        // 使用低优先级队列避免阻塞主线程
        DispatchQueue.main.async(qos: .userInteractive) { [weak self] in
            guard let self = self else { return }

            // 检查断路器状态
            if self.isCircuitBreakerOpen {
                return
            }

            if self.filterManager?.isBlackMistEnabled ?? false {
                // 启用黑柔滤镜时，显示滤镜视图
                self.filterBackgroundView?.isHidden = false
                self.filterImageView?.isHidden = false

                // 减少调试信息输出频率 - 每 300 帧打印一次
                if self.frameCount % 300 == 0 {
                    print("CameraViewController captureOutput: 显示滤镜视图, filterBackgroundView.isHidden=\(self.filterBackgroundView?.isHidden ?? true), filterImageView.isHidden=\(self.filterImageView?.isHidden ?? true)")
                }

                // 将 CIImage 转换为 UIImage
                if let ciImage = self.currentCIImage, let ciContext = self.ciContext {
                    // 使用后台队列处理图像转换，避免阻塞主线程
                    DispatchQueue.global(qos: .userInteractive).async {
                        autoreleasepool {
                            guard let cgImage = ciContext.createCGImage(ciImage, from: ciImage.extent) else { return }

                            // 使用上下颠倒并水平镜像的方向
                            let uiOrientation: UIImage.Orientation = .downMirrored

                            // 创建带有正确方向的 UIImage
                            let uiImage = UIImage(cgImage: cgImage, scale: 1.0, orientation: uiOrientation)

                            // 回到主线程设置图像
                            DispatchQueue.main.async { [weak self] in
                                guard let self = self, !self.isCircuitBreakerOpen else { return }

                                // 设置滤镜图像视图的图像
                                self.filterImageView?.image = uiImage

                                // 减少调试信息输出频率 - 每 300 帧打印一次
                                if self.frameCount % 300 == 0 {
                                    print("CameraViewController: 滤镜图像方向设置为 .downMirrored, 黑柔强度为 \(self.filterManager?.blackMistIntensity ?? 0)")
                                }
                            }
                        }
                    }
                }
            } else {
                // 黑柔滤镜未启用，但仍然需要显示正确方向的视频
                // 使用相同的方法处理图像，但放在预览层上方
                if let ciImage = self.currentCIImage, let ciContext = self.ciContext {
                    // 使用后台队列处理图像转换，避免阻塞主线程
                    DispatchQueue.global(qos: .userInteractive).async {
                        autoreleasepool {
                            guard let cgImage = ciContext.createCGImage(ciImage, from: ciImage.extent) else { return }

                            // 使用上下颠倒并水平镜像的方向
                            let uiOrientation: UIImage.Orientation = .downMirrored

                            // 创建带有正确方向的 UIImage
                            let uiImage = UIImage(cgImage: cgImage, scale: 1.0, orientation: uiOrientation)

                            // 回到主线程设置图像
                            DispatchQueue.main.async { [weak self] in
                                guard let self = self, !self.isCircuitBreakerOpen else { return }

                                // 确保滤镜背景视图显示但透明
                                self.filterBackgroundView?.isHidden = false
                                self.filterBackgroundView?.backgroundColor = UIColor.clear
                                self.filterImageView?.isHidden = false

                                // 设置滤镜图像视图的图像
                                self.filterImageView?.image = uiImage

                                // 减少调试信息输出频率 - 每 300 帧打印一次
                                if self.frameCount % 300 == 0 {
                                    print("CameraViewController: 无滤镜模式下图像方向设置为 .downMirrored")
                                }
                            }
                        }
                    }
                }
            }
        }
        }
    }

    // 处理音频样本缓冲区
    private func processAudioSampleBuffer(_ sampleBuffer: CMSampleBuffer) {
        // 如果正在录制并且使用AssetWriter，将音频样本缓冲区添加到音频输入
        if isRecording && filterManager?.isBlackMistEnabled ?? false,
           let assetWriter = self.assetWriter,
           let assetWriterAudioInput = self.assetWriterAudioInput,
           assetWriter.status == .writing && assetWriterAudioInput.isReadyForMoreMediaData {

            // 获取时间戳
            let timestamp = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)

            // 初始化开始时间（如果尚未设置）
            if self.startTime == nil {
                self.startTime = timestamp
                assetWriter.startSession(atSourceTime: .zero)
                print("音频录制开始，时间戳: \(timestamp.seconds)")
            }

            // 计算相对时间
            guard let startTime = self.startTime else { return }
            let relativeTime = CMTimeSubtract(timestamp, startTime)

            // 创建一个新的样本缓冲区，确保时间戳正确
            var adjustedSampleBuffer: CMSampleBuffer?

            // 尝试调整样本缓冲区的时间戳
            if let audioDesc = CMSampleBufferGetFormatDescription(sampleBuffer) {

                // 获取原始的时间信息
                var timingInfo = CMSampleTimingInfo()
                timingInfo.duration = CMSampleBufferGetDuration(sampleBuffer)
                timingInfo.presentationTimeStamp = relativeTime
                timingInfo.decodeTimeStamp = CMTime.invalid

                // 创建带有调整时间戳的新样本缓冲区
                let status = CMSampleBufferCreateCopyWithNewTiming(
                    allocator: kCFAllocatorDefault,
                    sampleBuffer: sampleBuffer,
                    sampleTimingEntryCount: 1,
                    sampleTimingArray: [timingInfo],
                    sampleBufferOut: &adjustedSampleBuffer
                )

                if status != noErr {
                    print("无法创建带有新时间戳的样本缓冲区，状态码: \(status)")
                }
            }

            // 使用调整后的样本缓冲区（如果创建成功），否则使用原始样本缓冲区
            let bufferToAppend = adjustedSampleBuffer ?? sampleBuffer

            // 将音频样本缓冲区添加到音频输入
            if !assetWriterAudioInput.append(bufferToAppend) {
                let error = assetWriter.error
                print("无法添加音频样本缓冲区到视频: \(error?.localizedDescription ?? "未知错误")")
            }
        }
    }

    // MARK: - 视频处理和录制相关方法

    /// 处理并记录视频帧
    /// - Parameters:
    ///   - sampleBuffer: 输入的样本缓冲区
    ///   - metalTexture: Metal纹理
    ///   - metalRenderer: Metal渲染器
    func processAndRecordVideoFrame(sampleBuffer: CMSampleBuffer, metalTexture: MTLTexture, metalRenderer: MetalRenderer) {
        // 在视频写入队列中异步处理
        videoWritingQueue.async { [weak self] in
            guard let self = self,
                  let assetWriter = self.assetWriter,
                  let assetWriterInput = self.assetWriterVideoInput,
                  let pixelBufferAdaptor = self.assetWriterPixelBufferAdaptor,
                  assetWriter.status == .writing && assetWriterInput.isReadyForMoreMediaData else {
                return
            }

            // 获取时间戳
            let timestamp = CMSampleBufferGetPresentationTimeStamp(sampleBuffer)

            // 初始化开始时间
            if self.startTime == nil {
                self.startTime = timestamp
                // 注意：现在在processAudioSampleBuffer中启动会话，避免重复启动
                print("视频录制开始，时间戳: \(timestamp.seconds)")
            }

            // 计算相对时间
            guard let startTime = self.startTime else { return }
            let relativeTime = CMTimeSubtract(timestamp, startTime)

            // 使用线程局部变量
            autoreleasepool {
                // 处理Metal纹理
                if let processedTexture = metalRenderer.processFrame(texture: metalTexture) {
                    // 将Metal纹理转换为CVPixelBuffer
                    if let pixelBuffer = self.createPixelBuffer(from: processedTexture, pixelBufferPool: pixelBufferAdaptor.pixelBufferPool) {
                        // 将像素缓冲区添加到视频
                        if !pixelBufferAdaptor.append(pixelBuffer, withPresentationTime: relativeTime) {
                            let error = assetWriter.error
                            print("无法添加像素缓冲区到视频: \(error?.localizedDescription ?? "未知错误")")

                            // 如果出错，尝试重置录制状态
                            if error != nil {
                                DispatchQueue.main.async {
                                    print("由于错误，正在停止录制")
                                    self.stopRecording()
                                }
                            }
                        }
                    } else {
                        print("创建像素缓冲区失败")
                    }
                } else {
                    print("从Metal获取处理后的纹理失败")
                }
            }
        }
    }

    /// 从Metal纹理创建像素缓冲区
    /// - Parameters:
    ///   - texture: Metal纹理
    ///   - pixelBufferPool: 像素缓冲池
    /// - Returns: 创建的像素缓冲区
    func createPixelBuffer(from texture: MTLTexture, pixelBufferPool: CVPixelBufferPool?) -> CVPixelBuffer? {
        guard let pixelBufferPool = pixelBufferPool else {
            print("像素缓冲池为空")
            return nil
        }

        var pixelBuffer: CVPixelBuffer?
        let status = CVPixelBufferPoolCreatePixelBuffer(nil, pixelBufferPool, &pixelBuffer)

        guard status == kCVReturnSuccess, let pixelBuffer = pixelBuffer else {
            print("无法创建像素缓冲区")
            return nil
        }

        // 使用Metal的blitCommandEncoder复制纹理，比直接getBytes更安全
        if let metalRenderer = self.metalRenderer,
           let commandQueue = metalRenderer.getCommandQueue() {

            // 锁定像素缓冲区
            CVPixelBufferLockBaseAddress(pixelBuffer, CVPixelBufferLockFlags(rawValue: 0))

            let width = CVPixelBufferGetWidth(pixelBuffer)
            let height = CVPixelBufferGetHeight(pixelBuffer)

            // 创建一个桥接纹理来访问像素缓冲区
            var textureOut: CVMetalTexture?

            // 创建Metal纹理缓存
            var textureCache: CVMetalTextureCache?
            if CVMetalTextureCacheCreate(kCFAllocatorDefault, nil, metalRenderer.device, nil, &textureCache) == kCVReturnSuccess,
               let cache = textureCache {

                // 从像素缓冲区创建Metal纹理
                if CVMetalTextureCacheCreateTextureFromImage(kCFAllocatorDefault, cache, pixelBuffer, nil, .bgra8Unorm, width, height, 0, &textureOut) == kCVReturnSuccess,
                   let textureOut = textureOut,
                   let destinationTexture = CVMetalTextureGetTexture(textureOut) {

                    // 创建命令缓冲区和编码器
                    if let commandBuffer = commandQueue.makeCommandBuffer(),
                       let blitEncoder = commandBuffer.makeBlitCommandEncoder() {

                        // 复制源纹理到目标纹理
                        let region = MTLRegionMake2D(0, 0, min(texture.width, width), min(texture.height, height))
                        blitEncoder.copy(from: texture,
                                         sourceSlice: 0,
                                         sourceLevel: 0,
                                         sourceOrigin: MTLOrigin(x: 0, y: 0, z: 0),
                                         sourceSize: MTLSize(width: region.size.width, height: region.size.height, depth: 1),
                                         to: destinationTexture,
                                         destinationSlice: 0,
                                         destinationLevel: 0,
                                         destinationOrigin: MTLOrigin(x: 0, y: 0, z: 0))

                        blitEncoder.endEncoding()
                        commandBuffer.commit()

                        // 等待复制完成
                        commandBuffer.waitUntilCompleted()
                    }
                }

                // 释放纹理缓存
                CVMetalTextureCacheFlush(cache, 0)
            }

            // 解锁像素缓冲区
            CVPixelBufferUnlockBaseAddress(pixelBuffer, CVPixelBufferLockFlags(rawValue: 0))

            return pixelBuffer
        }

        // 如果Metal方法失败，回退到更简单的方法
        CVPixelBufferLockBaseAddress(pixelBuffer, [])

        // 获取像素缓冲区的基地址
        if let pixelData = CVPixelBufferGetBaseAddress(pixelBuffer) {
            let bytesPerRow = CVPixelBufferGetBytesPerRow(pixelBuffer)
            let width = min(CVPixelBufferGetWidth(pixelBuffer), texture.width)
            let height = min(CVPixelBufferGetHeight(pixelBuffer), texture.height)
            let region = MTLRegionMake2D(0, 0, width, height)

            // 只对存储模式允许的纹理使用getBytes
            if texture.storageMode == .shared {
                // 使用安全的方式调用getBytes
                texture.getBytes(pixelData, bytesPerRow: bytesPerRow, from: region, mipmapLevel: 0)
            } else {
                print("警告: 纹理存储模式不支持直接读取")
            }
        }

        CVPixelBufferUnlockBaseAddress(pixelBuffer, [])

        return pixelBuffer
    }

    /// 保存视频到应用内和相册
    /// - Parameter fileURL: 视频文件URL
    func saveVideoToPhotoLibrary(fileURL: URL) {
        // 保存到应用内相册
        AppMediaStorage.shared.saveVideoToApp(from: fileURL) { success, savedURL in
            if success, let savedURL = savedURL {
                print("视频已保存到应用内相册: \(savedURL.path)")

                // 获取视频文件名
                let fileName = savedURL.lastPathComponent

                // 保存到系统相册 - 这里只保存一次
                MediaSaver.shared.saveVideoToPhotoLibrary(fileURL: fileURL) { [weak self] success, error, assetIdentifier in
                    if success, let assetId = assetIdentifier {
                        // 记录视频与系统相册资源的映射关系
                        print("记录视频映射关系: \(fileName) -> \(assetId)")
                        AppMediaStorage.shared.recordPhotoAssetMapping(appFileName: fileName, assetLocalIdentifier: assetId)

                        print("视频已保存到系统相册，标识符: \(assetId)")
                    } else if let error = error {
                        print("保存视频到系统相册失败: \(error.localizedDescription)")
                    } else {
                        print("视频已保存到系统相册")
                    }
                }
            } else {
                print("保存视频到应用内相册失败")

                // 如果应用内保存失败，仍然尝试保存到系统相册
                MediaSaver.shared.saveVideoToPhotoLibrary(fileURL: fileURL) { success, error, _ in
                    if success {
                        print("视频已保存到系统相册（应用内保存失败）")
                    } else if let error = error {
                        print("保存视频到系统相册失败: \(error.localizedDescription)")
                    }
                }
            }
        }
    }
}

// MARK: - AVCaptureFileOutputRecordingDelegate Methods

// These methods are implemented here but the protocol conformance is declared in the main class
extension CameraViewController {
    func fileOutput(_ output: AVCaptureFileOutput, didStartRecordingTo fileURL: URL, from connections: [AVCaptureConnection]) {
        // 录制开始回调
        print("开始录制视频到: \(fileURL.path)")
    }

    func fileOutput(_ output: AVCaptureFileOutput, didFinishRecordingTo outputFileURL: URL, from connections: [AVCaptureConnection], error: Error?) {
        // 录制完成回调
        if let error = error {
            print("录制视频错误: \(error.localizedDescription)")
            DispatchQueue.main.async {
                self.onVideoCaptured?(false, nil)
            }
            return
        }

        print("录制视频完成: \(outputFileURL.path)")

        // 通知视频已完成录制
        DispatchQueue.main.async {
            self.onVideoCaptured?(true, outputFileURL)
        }

        // 将视频保存到相册
        self.saveVideoToPhotoLibrary(fileURL: outputFileURL)
    }
}
