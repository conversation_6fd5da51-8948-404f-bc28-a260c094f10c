//
//  CameraViewControllerLayout.swift
//  TrueFilm
//
//  Created by Augment on 2025/5/7.
//

import UIKit
import AVFoundation

// MARK: - Layout Extension
extension CameraViewController {
    
    // MARK: - 视图布局
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // 确保预览层始终填充整个屏幕，但保持视频正确的纵横比
        if let previewLayer = previewLayer {
            // 使用 resizeAspect 以确保不会裁剪视频
            previewLayer.videoGravity = AVLayerVideoGravity.resizeAspect
            previewLayer.frame = view.bounds
            print("更新预览层大小: \(previewLayer.frame)")
        }

        // 更新滤镜背景视图的大小
        if let filterBackgroundView = filterBackgroundView {
            filterBackgroundView.frame = view.bounds
            print("更新滤镜背景视图大小: \(filterBackgroundView.frame)")

            // 更新滤镜图像视图的大小
            if let filterImageView = filterImageView {
                filterImageView.frame = filterBackgroundView.bounds
                // 同样使用 scaleAspectFit 模式确保图像不被裁剪
                filterImageView.contentMode = UIView.ContentMode.scaleAspectFit
                print("更新滤镜图像视图大小: \(filterImageView.frame)")
            }
        }
    }
    
    // MARK: - 旋转处理
    
    override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)

        // 处理旋转
        coordinator.animate(alongsideTransition: { [weak self] _ in
            guard let self = self else { return }

            // 始终保持横屏模式，无论设备如何旋转
            self.resetVideoOrientation()

            // 更新预览层大小
            self.previewLayer?.frame = self.view.bounds
            // 确保保持 resizeAspect 模式
            self.previewLayer?.videoGravity = AVLayerVideoGravity.resizeAspect

            // 更新滤镜背景视图的大小
            if let filterBackgroundView = self.filterBackgroundView {
                filterBackgroundView.frame = self.view.bounds

                // 更新滤镜图像视图的大小
                self.filterImageView?.frame = filterBackgroundView.bounds
                // 确保保持 scaleAspectFit 模式
                self.filterImageView?.contentMode = UIView.ContentMode.scaleAspectFit
            }

            // 如果有当前图像，重新应用滤镜效果以更新方向
            if self.filterManager?.isBlackMistEnabled ?? false, let ciImage = self.currentCIImage, let ciContext = self.ciContext {
                guard let cgImage = ciContext.createCGImage(ciImage, from: ciImage.extent) else {
                    print("无法从CIImage创建CGImage")
                    return
                }

                // 使用上下颠倒方向
                let uiOrientation: UIImage.Orientation = UIImage.Orientation.downMirrored

                // 创建带有正确方向的 UIImage
                let uiImage = UIImage(cgImage: cgImage, scale: 1.0, orientation: uiOrientation)

                // 不再进行额外的翻转，直接使用原始图像
                let finalImage = uiImage

                // 设置滤镜图像视图的图像
                self.filterImageView?.image = finalImage

                // 打印调试信息
                print("viewWillTransition: 滤镜图像方向设置为 .downMirrored")
            }
        })
    }
    
    // MARK: - 方向控制
    
    // 在视图控制器出现时强制横屏
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 确保在视图出现时方向是正确的
        resetVideoOrientation()

        // 强制设备方向为横屏（如果应用支持）
        let value = UIInterfaceOrientation.landscapeRight.rawValue
        UIDevice.current.setValue(value, forKey: "orientation")
    }

    // 重写支持的方向
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        return UIInterfaceOrientationMask.landscape
    }
    
    // MARK: - UI 设置
    
    /// 设置UI组件
    func setupUI() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 创建预览层
            let previewLayer = AVCaptureVideoPreviewLayer(session: self.captureSession)
            previewLayer.videoGravity = AVLayerVideoGravity.resizeAspect

            // 设置预览层为横屏模式
            if let connection = previewLayer.connection, connection.isVideoOrientationSupported {
                connection.videoOrientation = AVCaptureVideoOrientation.landscapeRight
            }

            previewLayer.frame = self.view.bounds
            self.view.layer.insertSublayer(previewLayer, at: 0)
            self.previewLayer = previewLayer
            
            // 检查当前设备是否为前置摄像头
            let isFrontCamera = self.currentLensType == .front
            
            // 如果是前置摄像头，设置镜像
            if let connection = previewLayer.connection, connection.isVideoMirroringSupported {
                connection.automaticallyAdjustsVideoMirroring = false
                connection.isVideoMirrored = isFrontCamera
            }

            // 创建滤镜背景视图
            let filterBackgroundView = UIView(frame: self.view.bounds)
            // 根据滤镜状态设置背景色
            filterBackgroundView.backgroundColor = (self.filterManager?.isBlackMistEnabled ?? false) ? UIColor.black : UIColor.clear
            filterBackgroundView.isHidden = false // 始终显示滤镜视图，但在未启用滤镜时使其透明
            self.view.addSubview(filterBackgroundView)
            self.filterBackgroundView = filterBackgroundView

            // 创建滤镜图像视图
            let filterImageView = UIImageView(frame: self.view.bounds)
            filterImageView.contentMode = UIView.ContentMode.scaleAspectFit // 使用 scaleAspectFit 模式，确保图像不被裁剪
            filterImageView.backgroundColor = UIColor.clear
            filterImageView.isHidden = false // 始终显示滤镜图像视图，以确保方向正确
            filterImageView.clipsToBounds = true // 裁剪超出边界的内容
            filterBackgroundView.addSubview(filterImageView) // 添加到背景视图上
            self.filterImageView = filterImageView

            // 打印滤镜初始状态
            print("CameraViewController: 滤镜初始状态为 \(self.filterManager?.isBlackMistEnabled ?? false ? "启用" : "禁用")")

            // 设置背景颜色
            self.view.backgroundColor = UIColor.black

            print("预览层和滤镜视图设置完成")
        }
    }
    
    // 重置视频方向的方法
    func resetVideoOrientation() {
        // 确保所有相关连接都设置为横屏
        if let connection = previewLayer?.connection, connection.isVideoOrientationSupported {
            connection.videoOrientation = AVCaptureVideoOrientation.landscapeRight
        }

        if let connection = videoOutput.connection(with: AVMediaType.video), connection.isVideoOrientationSupported {
            connection.videoOrientation = AVCaptureVideoOrientation.landscapeRight
        }
    }
    
    // MARK: - 滤镜视图更新
    
    /// 更新滤镜图像视图的可见性
    func updateFilterImageViewVisibility() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            let isEnabled = self.filterManager?.isBlackMistEnabled ?? false
            
            // 不再隐藏滤镜视图，而是根据滤镜状态调整背景色
            self.filterBackgroundView?.backgroundColor = isEnabled ? UIColor.black : UIColor.clear
            self.filterImageView?.isHidden = false
            
            print("更新滤镜视图: \(isEnabled ? "黑柔滤镜启用，背景设为黑色" : "黑柔滤镜禁用，背景设为透明")")
        }
    }
}
