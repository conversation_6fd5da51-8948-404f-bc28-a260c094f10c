//
//  ContentView.swift
//  TrueFilm
//
//  Created by late night king on 2025/4/12.
//

import SwiftUI

struct ContentView: View {
    @AppStorage("firstAppLaunch") private var firstAppLaunch: Bool = true
    
    var body: some View {
        ZStack {
            if firstAppLaunch {
                // Show onboarding view on first launch
                OnboardingView(isFirstLaunch: $firstAppLaunch)
                    .transition(.opacity)
            } else {
                // Display the camera control view as the main view
                CameraControlView()
                    .edgesIgnoringSafeArea(.all)  // Extend to edges
                    .statusBar(hidden: true)      // Hide status bar
                    .modifier(PersistentSystemOverlaysModifier())
                    .transition(.opacity)
            }
        }
        .animation(.easeInOut(duration: 0.5), value: firstAppLaunch)
    }
}

// Custom modifier to handle iOS version compatibility
struct PersistentSystemOverlaysModifier: ViewModifier {
    func body(content: Content) -> some View {
        if #available(iOS 16.0, *) {
            content.persistentSystemOverlays(.hidden)
        } else {
            content
        }
    }
}

#Preview {
    ContentView()
}
