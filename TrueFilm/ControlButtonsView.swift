//
//  ControlButtonsView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/15.
//

import SwiftUI
import Foundation
import AVFoundation

// 使用Models.swift中定义的ControlType
struct ControlButtonsView: View {
    @Binding var selectedControl: ControlType
    @Binding var isBlackMistEnabled: Bool
    @Binding var isManuallyLocked: Bool
    let hapticFeedback: UIImpactFeedbackGenerator
    @State private var isMultiLensCamera: Bool = false
    @ObservedObject var displayViewModel: GlassDisplayViewModel
    @State private var isWhiteMode: Bool = UserDefaults.standard.bool(forKey: "whiteUIMode")
    // Add portrait mode parameter
    var isPortrait: Bool = false

    // 按钮音效设置
    @State private var buttonSoundsEnabled: Bool = UserDefaults.standard.bool(forKey: "buttonSoundsEnabled")

    // Add language refresh ID to force view updates
    @State private var languageRefreshID = UUID()

    // 计算属性：当没有选中任何控制按钮时，转盘应该被锁定
    var isDialLocked: Bool {
        return selectedControl == .none && !isBlackMistEnabled
    }

    // 默认参数值（与FilmSimulationPanel.swift保持一致）
    private let defaultMistValue: Float = 0.3
    private let defaultThresholdValue: Float = 0.48
    private let defaultBrightnessValue: Float = -0.17
    private let defaultFilmIntensity: Float = 0.75  // 与FilmSimulationPanel、GlassDisplayViewModel、MetalRenderer保持一致

    var body: some View {
        HStack(spacing: isPortrait ? 12 : 18) {
            // MIST 按钮
            ModeButton(
                title: "MIST",
                isSelected: selectedControl == .mist || isBlackMistEnabled,
                isWhiteMode: isWhiteMode,
                isPortrait: isPortrait,
                hapticFeedback: hapticFeedback,
                action: {
                    hapticFeedback.impactOccurred(intensity: 0.5)

                    // Play click sound
                    AudioManager.shared.playClickSound()

                    // 如果处于手动锁定状态，先取消锁定
                    if isManuallyLocked {
                        isManuallyLocked = false
                        print("MIST按钮: 检测到手动锁定，已自动取消锁定")
                    }

                    // Debug: Print current state before toggle
                    print("MIST按钮: 点击前 - isBlackMistEnabled: \(isBlackMistEnabled), selectedControl: \(selectedControl)")

                    // Toggle black mist effect
                    isBlackMistEnabled.toggle()

                    // Debug: Print state after toggle
                    print("MIST按钮: 切换后 - isBlackMistEnabled: \(isBlackMistEnabled)")

                    // If enabling mist, select it
                    if isBlackMistEnabled {
                        // Only change to mist if no other control is selected or mist is already selected
                        if selectedControl == .none || selectedControl == .mist {
                            selectedControl = .mist
                            // 强制更新小肩屏显示
                            displayViewModel.selectedControl = selectedControl
                            displayViewModel.updateDisplayText()
                            print("MIST按钮: 启用黑柔 - 已选择MIST控制")
                        }

                        // Ensure the effect is applied to the camera
                        if let cameraVC = CameraView.currentCameraViewController {
                            // Make sure black mist is enabled in the camera
                            if !cameraVC.isBlackMistEnabled {
                                _ = cameraVC.toggleBlackMist()
                                print("MIST按钮: 已在相机中启用黑柔效果")
                            }
                        }
                    } else {
                        // If disabling mist when it's selected, change to none
                        if selectedControl == .mist {
                            selectedControl = .none
                            print("MIST按钮: 禁用黑柔 - 已清除控制选择")
                        }

                        // 强制更新小肩屏显示
                        displayViewModel.selectedControl = selectedControl
                        displayViewModel.updateDisplayText()

                        // Disable the effect in the camera
                        if let cameraVC = CameraView.currentCameraViewController {
                            if cameraVC.isBlackMistEnabled {
                                _ = cameraVC.toggleBlackMist()
                                print("MIST按钮: 已在相机中禁用黑柔效果")
                            }
                        }
                    }

                    // Debug: Print final state
                    print("MIST按钮: 最终状态 - isBlackMistEnabled: \(isBlackMistEnabled), selectedControl: \(selectedControl)")
                },
                longPressAction: {
                    resetMistParameter()
                },
                languageRefreshID: languageRefreshID
            )

            // THRESHOLD 按钮
            ModeButton(
                title: "Highlight",
                isSelected: selectedControl == .threshold,
                isWhiteMode: isWhiteMode,
                isPortrait: isPortrait,
                hapticFeedback: hapticFeedback,
                action: {
                    hapticFeedback.impactOccurred(intensity: 0.5)
                    // Play click sound
                    AudioManager.shared.playClickSound()

                    // 如果处于手动锁定状态，先取消锁定
                    if isManuallyLocked {
                        isManuallyLocked = false
                        print("Highlight按钮: 检测到手动锁定，已自动取消锁定")
                    }

                    // 切换选择状态
                    selectedControl = selectedControl == .threshold ? .none : .threshold

                    // 不再自动启用黑柔效果
                    // 只更新显示视图模型
                    displayViewModel.selectedControl = selectedControl
                    displayViewModel.updateDisplayText()
                },
                longPressAction: {
                    resetThresholdParameter()
                },
                languageRefreshID: languageRefreshID
            )

            // BRIGHTNESS 按钮
            ModeButton(
                title: "EV",
                isSelected: selectedControl == .brightness,
                isWhiteMode: isWhiteMode,
                isPortrait: isPortrait,
                hapticFeedback: hapticFeedback,
                action: {
                    hapticFeedback.impactOccurred(intensity: 0.5)
                    // Play click sound
                    AudioManager.shared.playClickSound()

                    // 如果处于手动锁定状态，先取消锁定
                    if isManuallyLocked {
                        isManuallyLocked = false
                        print("EV按钮: 检测到手动锁定，已自动取消锁定")
                    }

                    // 切换选择状态
                    selectedControl = selectedControl == .brightness ? .none : .brightness

                    // 不再自动启用黑柔效果
                    // 只更新显示视图模型
                    displayViewModel.selectedControl = selectedControl
                    displayViewModel.showPhotoCount = false

                    // Toggle between showing shutter speed and brightness
                    if selectedControl == .brightness {
                        displayViewModel.showShutterSpeed = false
                        // 不需要每次都设置为默认值，保留用户的设置
                        // 只有长按重置时才会调用resetBrightnessParameter()恢复默认值
                    } else {
                        // When deselected, show shutter speed again
                        displayViewModel.showShutterSpeed = true
                    }

                    displayViewModel.updateDisplayText()
                },
                longPressAction: {
                    resetBrightnessParameter()
                },
                languageRefreshID: languageRefreshID
            )

            // FILM 按钮
            ModeButton(
                title: "FILM",
                isSelected: selectedControl == .film,
                isWhiteMode: isWhiteMode,
                isPortrait: isPortrait,
                hapticFeedback: hapticFeedback,
                action: {
                    hapticFeedback.impactOccurred(intensity: 0.5)
                    // Play click sound
                    AudioManager.shared.playClickSound()

                    // 如果处于手动锁定状态，先取消锁定
                    if isManuallyLocked {
                        isManuallyLocked = false
                        print("Film按钮: 检测到手动锁定，已自动取消锁定")
                    }

                    // 切换选择状态
                    selectedControl = selectedControl == .film ? .none : .film

                    // 更新显示视图模型
                    displayViewModel.selectedControl = selectedControl
                    displayViewModel.updateDisplayText()

                    print("Film按钮: 选择状态 - selectedControl: \(selectedControl)")
                },
                longPressAction: {
                    resetFilmParameter()
                },
                languageRefreshID: languageRefreshID
            )
        }
        // Add ID to force view refresh when language changes
        .id("control_buttons_\(languageRefreshID)")
        .onAppear {
            // 检查相机类型
            checkCameraType()

            // Subscribe to UI mode changes
            NotificationCenter.default.addObserver(forName: NSNotification.Name("UIModeChanged"), object: nil, queue: .main) { [self] _ in
                // Update UI mode from UserDefaults
                self.isWhiteMode = UserDefaults.standard.bool(forKey: "whiteUIMode")
            }

            // Subscribe to language changes
            NotificationCenter.default.addObserver(forName: .languageDidChange, object: nil, queue: .main) { [self] _ in
                print("ControlButtonsView: 检测到语言变更，刷新UI")
                // Force a view refresh by updating the ID
                DispatchQueue.main.async {
                    // Use small delay to ensure language resources are loaded
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        self.languageRefreshID = UUID()
                    }
                }
            }

            // Subscribe to button sounds setting changes
            NotificationCenter.default.addObserver(forName: NSNotification.Name("ButtonSoundsChanged"), object: nil, queue: .main) { [self] _ in
                buttonSoundsEnabled = UserDefaults.standard.bool(forKey: "buttonSoundsEnabled")
                print("[ControlButtonsView] 按钮音效设置已更新: \(buttonSoundsEnabled ? "开启" : "关闭")")
            }
        }
        .onDisappear {
            NotificationCenter.default.removeObserver(self)
        }
    }

    // 重置黑柔参数为默认值 0.3
    private func resetMistParameter() {
        print("重置黑柔参数为默认值: \(defaultMistValue)")

        // 确保黑柔效果已启用
        if !isBlackMistEnabled {
            isBlackMistEnabled = true
        }

        // 选择MIST控制
        selectedControl = .mist

        // 更新显示视图模型
        displayViewModel.selectedControl = .mist
        displayViewModel.blackMistIntensity = defaultMistValue
        displayViewModel.updateDisplayText()

        // 更新相机控制器
        if let cameraVC = CameraView.currentCameraViewController {
            // 确保黑柔效果已启用
            if !cameraVC.isBlackMistEnabled {
                _ = cameraVC.toggleBlackMist()
            }

            // 设置黑柔强度为默认值
            cameraVC.setBlackMistIntensity(defaultMistValue)
        }

        // 通知转盘更新
        NotificationCenter.default.post(name: NSNotification.Name("ResetMistValue"), object: nil, userInfo: ["value": defaultMistValue])
    }

    // 重置高光阈值参数为默认值 0.48
    private func resetThresholdParameter() {
        print("重置高光阈值参数为默认值: \(defaultThresholdValue)")

        // 确保黑柔效果已启用
        if !isBlackMistEnabled {
            isBlackMistEnabled = true
        }

        // 选择Threshold控制
        selectedControl = .threshold

        // 更新显示视图模型
        displayViewModel.selectedControl = .threshold
        displayViewModel.blackMistThreshold = defaultThresholdValue
        displayViewModel.updateDisplayText()

        // 更新相机控制器
        if let cameraVC = CameraView.currentCameraViewController {
            // 确保黑柔效果已启用
            if !cameraVC.isBlackMistEnabled {
                _ = cameraVC.toggleBlackMist()
            }

            // 设置高光阈值为默认值
            cameraVC.setBlackMistThreshold(defaultThresholdValue)
        }

        // 通知转盘更新
        NotificationCenter.default.post(name: NSNotification.Name("ResetThresholdValue"), object: nil, userInfo: ["value": defaultThresholdValue])
    }

    // 重置亮度参数为默认值 0.0
    private func resetBrightnessParameter() {
        print("重置亮度参数为默认值: \(defaultBrightnessValue)")

        // 选择Brightness控制
        selectedControl = .brightness

        // 更新显示视图模型
        displayViewModel.selectedControl = .brightness
        displayViewModel.brightness = defaultBrightnessValue
        displayViewModel.updateDisplayText()

        // 更新相机控制器
        if let cameraVC = CameraView.currentCameraViewController {
            // 设置亮度为默认值
            cameraVC.setBrightness(defaultBrightnessValue)
        }

        // 通知转盘更新
        NotificationCenter.default.post(name: NSNotification.Name("ResetBrightnessValue"), object: nil, userInfo: ["value": defaultBrightnessValue])
    }

    // 重置胶片滤镜强度为默认值 0.75
    private func resetFilmParameter() {
        print("重置胶片滤镜强度为默认值: \(defaultFilmIntensity)")

        // 选择Film控制
        selectedControl = .film

        // 更新显示视图模型
        displayViewModel.selectedControl = .film
        displayViewModel.filmIntensity = defaultFilmIntensity
        displayViewModel.updateDisplayText()

        // 更新相机控制器中的胶片滤镜强度
        if let cameraVC = CameraView.currentCameraViewController,
           let metalRenderer = cameraVC.getMetalRenderer() {
            metalRenderer.setLUTIntensity(defaultFilmIntensity)
        }

        // 通知转盘更新
        NotificationCenter.default.post(name: NSNotification.Name("ResetFilmValue"), object: nil, userInfo: ["value": defaultFilmIntensity])
    }

    // 检查是否为多镜头相机的函数
    private func checkCameraType() {
        #if targetEnvironment(simulator)
        isMultiLensCamera = false
        #else
        // 在真实设备上检查摄像头类型
        if UIDevice.current.userInterfaceIdiom == .phone {
            let deviceName = UIDevice.current.name
            isMultiLensCamera = deviceName.contains("Pro") || deviceName.contains("Plus") || deviceName.contains("Max")
        } else {
            isMultiLensCamera = false
        }
        #endif
    }
}

// 模式按钮
struct ModeButton: View {
    let title: String
    let isSelected: Bool
    var isDisabled: Bool = false
    var isWhiteMode: Bool = false
    var isPortrait: Bool = false
    let hapticFeedback: UIImpactFeedbackGenerator
    let action: () -> Void
    // Add long press action for parameter reset
    var longPressAction: (() -> Void)? = nil
    // Add language refresh ID parameter to force refresh of localized text
    var languageRefreshID: UUID = UUID()  // Add default value to avoid breaking preview

    @State private var isPressed: Bool = false
    @State private var isLongPressed: Bool = false

    // 定义橙色强调色
    private var accentOrange: Color {
        Color(red: 1.0, green: 0.58, blue: 0.0) // 橙色 #FF9500
    }

    var body: some View {
        VStack(spacing: isPortrait ? 3 : 4) {
            // 标题文字，选中时变为橙色
            Text(LocalizedString(title))
                .font(.system(size: isPortrait ? 9 : 10, weight: isSelected ? .medium : .regular))
                .foregroundColor(
                    isSelected ? accentOrange :
                               (isDisabled ? .gray.opacity(0.3) : .gray.opacity(0.7))
                )
                // Add ID to ensure text refreshes when language changes
                .id("button_text_\(title)_\(languageRefreshID)")

            // 圆形按钮，更小的尺寸
            Button(action: {
                // Prepare haptic feedback before use
                hapticFeedback.prepare()
                action()
            }) {
                ZStack {
                    // 外层阴影 - 创建凹陷效果
                    Circle()
                        .fill(Color.black.opacity(0.8))
                        .overlay(
                            Circle()
                                .stroke(LinearGradient(
                                    gradient: Gradient(colors: [
                                        Color.black.opacity(0.9),
                                        Color.gray.opacity(isDisabled ? 0.1 : 0.15)
                                    ]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ), lineWidth: 1.5)
                        )
                        .shadow(color: Color.black.opacity(0.8), radius: 2, x: 1, y: 1)
                        .shadow(color: Color.gray.opacity(0.2), radius: 1, x: -1, y: -1)

                    // 内层圆形 - 凹陷表面
                    Circle()
                        .fill(Color(red: isDisabled ? 0.08 : 0.12, green: isDisabled ? 0.08 : 0.12, blue: isDisabled ? 0.08 : 0.12))
                        .frame(width: isPortrait ? 32 : 36, height: isPortrait ? 32 : 36)
                        .overlay(
                            Circle()
                                .stroke(Color.black.opacity(0.8), lineWidth: 1)
                        )
                        .shadow(color: Color.black.opacity(0.6), radius: 2, x: 0, y: 0)

                    // 按下效果 - 更深的凹陷
                    if isPressed || isLongPressed {
                        Circle()
                            .fill(Color.black.opacity(0.6))
                            .frame(width: isPortrait ? 28 : 32, height: isPortrait ? 28 : 32)
                            .blur(radius: 2)
                    }

                    // 选中时的橙色高亮效果
                    if isSelected {
                        // 内部橙色光晕
                        Circle()
                            .fill(accentOrange.opacity(0.15))
                            .frame(width: isPortrait ? 30 : 34, height: isPortrait ? 30 : 34)
                            .blur(radius: 1)

                        // 橙色边框高亮
                        Circle()
                            .fill(Color.clear)
                            .frame(width: isPortrait ? 32 : 36, height: isPortrait ? 32 : 36)
                            .overlay(
                                Circle()
                                    .stroke(accentOrange.opacity(0.8), lineWidth: 1.0)
                            )

                        // 外层橙色光晕
                        Circle()
                            .fill(Color.clear)
                            .frame(width: isPortrait ? 34 : 38, height: isPortrait ? 34 : 38)
                            .overlay(
                                Circle()
                                    .stroke(accentOrange.opacity(0.4), lineWidth: 0.5)
                            )
                    }

                    // 禁用状态显示叉号
                    if isDisabled {
                        Image(systemName: "xmark")
                            .font(.system(size: isPortrait ? 10 : 12, weight: .light))
                            .foregroundColor(.gray.opacity(0.5))
                    }
                }
                .frame(width: isPortrait ? 36 : 40, height: isPortrait ? 36 : 40)
                .opacity(isDisabled ? 0.7 : 1.0)
            }
            .buttonStyle(PlainButtonStyle())
            .simultaneousGesture(
                DragGesture(minimumDistance: 0)
                    .onChanged { _ in
                        if !isPressed && !isDisabled {
                            // Prepare haptic feedback before use
                            hapticFeedback.prepare()
                            hapticFeedback.impactOccurred(intensity: 0.3)
                            isPressed = true
                        }
                    }
                    .onEnded { _ in
                        isPressed = false
                    }
            )
            // Add long press gesture for parameter reset
            .simultaneousGesture(
                LongPressGesture(minimumDuration: 0.8)
                    .onEnded { _ in
                        if !isDisabled && longPressAction != nil {
                            // Prepare haptic feedback before use
                            hapticFeedback.prepare()
                            // First haptic feedback
                            hapticFeedback.impactOccurred(intensity: 0.8)

                            // Set long press state
                            isLongPressed = true

                            // Second haptic feedback after a short delay
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                                hapticFeedback.prepare()
                                hapticFeedback.impactOccurred(intensity: 0.8)

                                // Call the long press action (parameter reset)
                                longPressAction?()

                                // Reset long press state after a delay
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                                    isLongPressed = false
                                }
                            }
                        }
                    }
            )
            .onAppear {
                // Prepare haptic feedback when view appears
                hapticFeedback.prepare()
            }
        }
    }
}

#Preview {
    struct PreviewWrapper: View {
        @State private var selectedControl: ControlType = .mist // 默认选中MIST按钮来展示选中效果
        @State private var isBlackMistEnabled: Bool = true
        @State private var isManuallyLocked: Bool = false
        @StateObject private var displayViewModel = GlassDisplayViewModel()

        var body: some View {
            ZStack {
                Color.black.edgesIgnoringSafeArea(.all)

                VStack {
                    Text("选中状态预览 (MIST按钮)")
                        .foregroundColor(.white)
                        .font(.headline)
                        .padding()

                    // Portrait preview
                    ControlButtonsView(
                        selectedControl: $selectedControl,
                        isBlackMistEnabled: $isBlackMistEnabled,
                        isManuallyLocked: $isManuallyLocked,
                        hapticFeedback: UIImpactFeedbackGenerator(style: .medium),
                        displayViewModel: displayViewModel,
                        isPortrait: true
                    )
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
                    .padding(.bottom, 20)

                    Text("横屏模式")
                        .foregroundColor(.white)
                        .font(.subheadline)

                    // Landscape preview
                    ControlButtonsView(
                        selectedControl: $selectedControl,
                        isBlackMistEnabled: $isBlackMistEnabled,
                        isManuallyLocked: $isManuallyLocked,
                        hapticFeedback: UIImpactFeedbackGenerator(style: .medium),
                        displayViewModel: displayViewModel,
                        isPortrait: false
                    )
                    .padding()
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(8)
                }
                .padding()
            }
        }
    }

    return PreviewWrapper()
}