//
//  CubeLUTParser.swift
//  TrueFilm
//
//  Created by Augment on 2025/1/15.
//

import Foundation
import UIKit
import Metal

// MARK: - Cube LUT Parser
class CubeLUTParser {

    // Cube LUT 数据结构
    struct CubeLUT {
        let dimension: Int
        let data: Data
        let title: String?
        let domainMin: (Float, Float, Float)
        let domainMax: (Float, Float, Float)
    }

    // 解析 .cube 文件
    static func parse(from url: URL) throws -> CubeLUT {
        print("开始解析CUBE文件: \(url.lastPathComponent)")

        // 尝试不同的编码格式
        var content: String
        if let utf8Content = try? String(contentsOf: url, encoding: .utf8) {
            content = utf8Content
            print("使用UTF-8编码成功读取文件")
        } else if let asciiContent = try? String(contentsOf: url, encoding: .ascii) {
            content = asciiContent
            print("使用ASCII编码成功读取文件")
        } else {
            throw CubeLUTError.invalidFormat("无法读取文件内容，不支持的编码格式")
        }

        return try parse(from: content, fileName: url.lastPathComponent)
    }

    // 解析 .cube 文件内容
    static func parse(from content: String, fileName: String = "") throws -> CubeLUT {
        let lines = content.components(separatedBy: .newlines)

        var dimension: Int = 0
        var title: String?
        var domainMin: (Float, Float, Float) = (0.0, 0.0, 0.0)
        var domainMax: (Float, Float, Float) = (1.0, 1.0, 1.0)
        var lutData: [Float] = []

        print("开始解析CUBE文件，总行数: \(lines.count)")

        for (lineIndex, line) in lines.enumerated() {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // 跳过空行和注释
            if trimmedLine.isEmpty || trimmedLine.hasPrefix("#") {
                continue
            }

            let components = trimmedLine.components(separatedBy: .whitespaces)
                .filter { !$0.isEmpty }

            if components.isEmpty {
                continue
            }

            // 调试：打印前10行和后10行的内容
            if lineIndex < 10 || lineIndex >= lines.count - 10 {
                print("  行 \(lineIndex): '\(trimmedLine)'")
            }

            let keyword = components[0].uppercased()

            switch keyword {
            case "TITLE":
                if components.count > 1 {
                    title = components.dropFirst().joined(separator: " ")
                        .trimmingCharacters(in: CharacterSet(charactersIn: "\""))
                }

            case "LUT_3D_SIZE":
                if components.count > 1, let size = Int(components[1]) {
                    dimension = size
                } else {
                    throw CubeLUTError.invalidFormat("Invalid LUT_3D_SIZE")
                }

            case "DOMAIN_MIN":
                if components.count >= 4,
                   let r = Float(components[1]),
                   let g = Float(components[2]),
                   let b = Float(components[3]) {
                    domainMin = (r, g, b)
                }

            case "DOMAIN_MAX":
                if components.count >= 4,
                   let r = Float(components[1]),
                   let g = Float(components[2]),
                   let b = Float(components[3]) {
                    domainMax = (r, g, b)
                }

            default:
                // 尝试解析为RGB数据
                if components.count >= 3,
                   let r = Float(components[0]),
                   let g = Float(components[1]),
                   let b = Float(components[2]) {

                    // 验证RGB值在合理范围内
                    let clampedR = max(0.0, min(1.0, r))
                    let clampedG = max(0.0, min(1.0, g))
                    let clampedB = max(0.0, min(1.0, b))

                    lutData.append(clampedR)
                    lutData.append(clampedG)
                    lutData.append(clampedB)
                    lutData.append(1.0) // Alpha channel

                    // 调试：每1000个数据点打印一次进度
                    if lutData.count % 4000 == 0 {
                        let dataPoints = lutData.count / 4
                        print("  已解析数据点: \(dataPoints)")
                    }
                } else if !trimmedLine.isEmpty {
                    // 记录无法解析的行（用于调试）
                    print("  无法解析的行: '\(trimmedLine)'")
                }
            }
        }

        // 验证数据
        guard dimension > 0 else {
            throw CubeLUTError.invalidFormat("Missing or invalid LUT_3D_SIZE")
        }

        // 计算实际RGB数据点数量（不包括Alpha通道）
        let actualRGBDataCount = lutData.count / 4 * 3  // 移除添加的Alpha通道
        let expectedRGBDataCount = dimension * dimension * dimension * 3 // RGB only
        let actualDataPoints = lutData.count / 4  // 实际数据点数量
        let expectedDataPoints = dimension * dimension * dimension

        print("CUBE LUT 解析调试信息:")
        print("  声明的维度: \(dimension)")
        print("  期望的数据点: \(expectedDataPoints)")
        print("  实际的数据点: \(actualDataPoints)")
        print("  期望的RGB数据: \(expectedRGBDataCount)")
        print("  实际的RGBA数据: \(lutData.count)")

        // 如果数据点数量不匹配，尝试重新计算维度
        if actualDataPoints != expectedDataPoints {
            let calculatedDimension = Int(round(pow(Double(actualDataPoints), 1.0/3.0)))
            print("  重新计算的维度: \(calculatedDimension)")

            if calculatedDimension * calculatedDimension * calculatedDimension == actualDataPoints {
                print("  使用重新计算的维度: \(calculatedDimension)")
                dimension = calculatedDimension
            } else {
                throw CubeLUTError.invalidFormat("Data count mismatch. Declared dimension: \(dimension), Calculated dimension: \(calculatedDimension), Data points: \(actualDataPoints)")
            }
        }

        let finalExpectedDataCount = dimension * dimension * dimension * 4 // RGBA
        guard lutData.count == finalExpectedDataCount else {
            throw CubeLUTError.invalidFormat("Final data count mismatch. Expected: \(finalExpectedDataCount), Got: \(lutData.count)")
        }

        // 将Float数组转换为Data
        let data = Data(bytes: lutData, count: lutData.count * MemoryLayout<Float>.size)

        return CubeLUT(
            dimension: dimension,
            data: data,
            title: title ?? fileName,
            domainMin: domainMin,
            domainMax: domainMax
        )
    }

    // 将 CubeLUT 转换为 PNG 图像（用于与现有系统兼容）
    static func convertToImage(cubeLUT: CubeLUT) -> UIImage? {
        let dimension = cubeLUT.dimension

        // 计算图像尺寸
        let imageSize: (width: Int, height: Int)
        if dimension <= 16 {
            imageSize = (64, 64)  // 16³ -> 64x64
        } else if dimension <= 32 {
            imageSize = (256, 256)  // 32³ -> 256x256
        } else {
            imageSize = (512, 512)  // 64³ -> 512x512
        }

        let width = imageSize.width
        let height = imageSize.height
        let slicesPerRow = width / dimension

        // 创建图像数据
        var imageData = [UInt8]()
        imageData.reserveCapacity(width * height * 4)

        cubeLUT.data.withUnsafeBytes { bytes in
            let floatData = bytes.bindMemory(to: Float.self)

            for y in 0..<height {
                for x in 0..<width {
                    let sliceY = y / dimension
                    let sliceX = x / dimension
                    let sliceIndex = sliceY * slicesPerRow + sliceX

                    let localX = x % dimension
                    let localY = y % dimension

                    if sliceIndex < dimension {
                        let lutIndex = (sliceIndex * dimension * dimension + localY * dimension + localX) * 4

                        if lutIndex + 3 < floatData.count {
                            let r = UInt8(clamp(floatData[lutIndex] * 255.0, min: 0.0, max: 255.0))
                            let g = UInt8(clamp(floatData[lutIndex + 1] * 255.0, min: 0.0, max: 255.0))
                            let b = UInt8(clamp(floatData[lutIndex + 2] * 255.0, min: 0.0, max: 255.0))
                            let a = UInt8(255) // Full alpha

                            imageData.append(r)
                            imageData.append(g)
                            imageData.append(b)
                            imageData.append(a)
                        } else {
                            // 填充默认值
                            imageData.append(0)
                            imageData.append(0)
                            imageData.append(0)
                            imageData.append(255)
                        }
                    } else {
                        // 超出范围，填充黑色
                        imageData.append(0)
                        imageData.append(0)
                        imageData.append(0)
                        imageData.append(255)
                    }
                }
            }
        }

        // 创建 CGImage
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)

        guard let dataProvider = CGDataProvider(data: Data(imageData) as CFData),
              let cgImage = CGImage(
                width: width,
                height: height,
                bitsPerComponent: 8,
                bitsPerPixel: 32,
                bytesPerRow: width * 4,
                space: colorSpace,
                bitmapInfo: bitmapInfo,
                provider: dataProvider,
                decode: nil,
                shouldInterpolate: false,
                intent: .defaultIntent
              ) else {
            return nil
        }

        return UIImage(cgImage: cgImage)
    }

    // 辅助函数：限制值在指定范围内
    private static func clamp(_ value: Float, min: Float, max: Float) -> Float {
        return Swift.max(min, Swift.min(max, value))
    }
}

// MARK: - Cube LUT 错误类型
enum CubeLUTError: Error, LocalizedError {
    case invalidFormat(String)
    case fileNotFound
    case unsupportedDimension(Int)

    var errorDescription: String? {
        switch self {
        case .invalidFormat(let message):
            return "Invalid cube LUT format: \(message)"
        case .fileNotFound:
            return "Cube LUT file not found"
        case .unsupportedDimension(let dimension):
            return "Unsupported LUT dimension: \(dimension)"
        }
    }
}
