import SwiftUI
import UniformTypeIdentifiers

// MARK: - 胶片模拟滤镜面板
struct FilmSimulationPanel: View {
    @Binding var isPresented: Bool
    @State private var dragOffset: CGFloat = 0
    @State private var selectedFilter: FilmFilter? = nil
    @State private var filterIntensity: Float = 0.75  // 默认强度 75%，与ControlButtonsView保持一致

    // 胶片滤镜数据模型
    struct FilmFilter: Identifiable, Hashable {
        let id = UUID()
        let name: String
        var displayName: String
        let lutFileName: String
        let previewImage: String
        let description: String
        let isBuiltIn: Bool // 是否为内置滤镜（不可删除）
        var sortOrder: Int // 排序顺序

        init(name: String, displayName: String, lutFileName: String, previewImage: String, description: String, isBuiltIn: Bool = false, sortOrder: Int = 0) {
            self.name = name
            self.displayName = displayName
            self.lutFileName = lutFileName
            self.previewImage = previewImage
            self.description = description
            self.isBuiltIn = isBuiltIn
            self.sortOrder = sortOrder
        }
    }

    // 动态加载的胶片滤镜列表
    @State private var filmFilters: [FilmFilter] = []
    @State private var hasSetDefaultFilter: Bool = false  // 标记是否已设置默认滤镜

    // 导入LUT文件相关状态
    @State private var showingDocumentPicker = false
    @State private var showingImportAlert = false
    @State private var importAlertMessage = ""
    @State private var showingTitleEditor = false
    @State private var pendingImportData: (data: Data, fileName: String, isFromCube: Bool)?
    @State private var pendingCubeLUT: CubeLUTParser.CubeLUT?
    @State private var customTitle = ""

    // 滤镜编辑相关状态
    @State private var showingFilterActionSheet = false
    @State private var showingRenameDialog = false
    @State private var showingDeleteAlert = false
    @State private var editingFilter: FilmFilter?
    @State private var editingFilterName = ""

    var body: some View {
        GeometryReader { geometry in
            let panelWidth = geometry.size.width * 0.35 // 与右侧面板相同宽度

            ZStack(alignment: .trailing) {
                // 背景遮罩
                if isPresented {
                    Color.black.opacity(0.3)
                        .edgesIgnoringSafeArea(.all)
                        .onTapGesture {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                isPresented = false
                            }
                        }
                }

                // 滑动面板
                VStack(spacing: 0) {
                    // 面板头部
                    HStack {
                        Text(LocalizedString("FILM_SIMULATION"))
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.white)

                        Spacer()

                        // 编辑按钮（仅在选中滤镜且非内置滤镜时显示）
                        if let selectedFilter = selectedFilter, !selectedFilter.isBuiltIn {
                            Button(action: {
                                editingFilter = selectedFilter
                                editingFilterName = selectedFilter.displayName
                                showingFilterActionSheet = true
                            }) {
                                Image(systemName: "pencil.circle")
                                    .font(.system(size: 16, weight: .medium))
                                    .foregroundColor(.white)
                                    .frame(width: 30, height: 30)
                                    .background(Color.orange.opacity(0.3))
                                    .clipShape(Circle())
                            }
                        }

                        // 导入LUT按钮
                        Button(action: {
                            showingDocumentPicker = true
                        }) {
                            Image(systemName: "plus.circle")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .frame(width: 30, height: 30)
                                .background(Color.blue.opacity(0.3))
                                .clipShape(Circle())
                        }

                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .frame(width: 30, height: 30)
                                .background(Color.white.opacity(0.2))
                                .clipShape(Circle())
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 20)
                    .padding(.bottom, 15)

                    // 强度调节滑块
                    VStack(spacing: 8) {
                        HStack {
                            Text(LocalizedString("FILM_INTENSITY"))
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white)

                            Spacer()

                            Text("\(Int(filterIntensity * 100))%")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.white.opacity(0.8))
                        }

                        HStack(spacing: 12) {
                            Text("0%")
                                .font(.system(size: 12))
                                .foregroundColor(.white.opacity(0.6))

                            Slider(value: Binding(
                                get: { filterIntensity },
                                set: { newValue in
                                    filterIntensity = newValue
                                    updateFilterIntensity(newValue)
                                    // 同步更新 ControlButtonsView 和 GlassDisplayViewModel
                                    syncIntensityWithControlButtons(newValue)
                                }
                            ), in: 0...1)
                            .accentColor(.blue)

                            Text("100%")
                                .font(.system(size: 12))
                                .foregroundColor(.white.opacity(0.6))
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 15)

                    // 滤镜列表
                    ScrollView(.vertical, showsIndicators: false) {
                        LazyVStack(spacing: 8) {
                            ForEach(filmFilters.sorted(by: { $0.sortOrder < $1.sortOrder })) { filter in
                                FilmFilterRow(
                                    filter: filter,
                                    isSelected: selectedFilter?.id == filter.id,
                                    onTap: {
                                        withAnimation(.easeInOut(duration: 0.2)) {
                                            selectedFilter = selectedFilter?.id == filter.id ? nil : filter
                                        }
                                        applyFilmFilter(filter)
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, 16)
                        .padding(.bottom, 20)
                    }
                }
                .frame(width: panelWidth)
                .background(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color.black.opacity(0.95),
                            Color.black.opacity(0.9)
                        ]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .offset(x: isPresented ? -dragOffset : panelWidth)
                .gesture(
                    DragGesture()
                        .onChanged { value in
                            // 只允许向右拖动（关闭方向）
                            if value.translation.width > 0 {
                                dragOffset = min(panelWidth, value.translation.width)
                            }
                        }
                        .onEnded { value in
                            withAnimation(.easeInOut(duration: 0.3)) {
                                if value.translation.width > panelWidth * 0.3 {
                                    // 拖动超过30%，关闭面板
                                    isPresented = false
                                    dragOffset = 0
                                } else {
                                    // 否则回弹
                                    dragOffset = 0
                                }
                            }
                        }
                )
            }
        }
        .edgesIgnoringSafeArea(.all)
        .onAppear {
            loadLUTFilters()
            // 监听来自控制转盘的胶片滤镜强度更新
            NotificationCenter.default.addObserver(
                forName: NSNotification.Name("UpdateFilmSimulationPanelIntensity"),
                object: nil,
                queue: .main
            ) { [self] notification in
                if let userInfo = notification.userInfo,
                   let intensity = userInfo["intensity"] as? Float {
                    print("FilmSimulationPanel: 接收到来自控制转盘的强度更新: \(intensity)")
                    // 更新滑块值，但不触发同步回调（避免循环）
                    self.filterIntensity = intensity
                }
            }
        }
        .onDisappear {
            // 清理通知监听
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("UpdateFilmSimulationPanelIntensity"), object: nil)
        }
        // DocumentPicker for LUT import (支持PNG和CUBE格式)
        .sheet(isPresented: $showingDocumentPicker) {
            DocumentPicker(
                allowedContentTypes: [.png, .image, UTType(filenameExtension: "cube") ?? .data],
                onDocumentPicked: { url in
                    importLUTFromURL(url)
                }
            )
        }
        // Title editor for imported LUTs
        .sheet(isPresented: $showingTitleEditor) {
            TitleEditorView(
                title: $customTitle,
                onSave: {
                    if let pendingData = pendingImportData {
                        finalizeLUTImport(data: pendingData.data, fileName: pendingData.fileName, customTitle: customTitle, isFromCube: pendingData.isFromCube)
                    } else if let pendingCube = pendingCubeLUT {
                        finalizeCubeLUTImport(cubeLUT: pendingCube, customTitle: customTitle)
                    }
                    showingTitleEditor = false
                },
                onCancel: {
                    pendingImportData = nil
                    pendingCubeLUT = nil
                    customTitle = ""
                    showingTitleEditor = false
                }
            )
        }
        // Filter action sheet
        .actionSheet(isPresented: $showingFilterActionSheet) {
            ActionSheet(
                title: Text(editingFilter?.displayName ?? ""),
                buttons: [
                    .default(Text(LocalizedString("EDIT_NAME"))) {
                        showingRenameDialog = true
                    },
                    .destructive(Text(LocalizedString("DELETE_FILTER"))) {
                        showingDeleteAlert = true
                    },
                    .cancel()
                ]
            )
        }
        // Rename dialog
        .alert(LocalizedString("EDIT_NAME"), isPresented: $showingRenameDialog, actions: {
            TextField(LocalizedString("ENTER_FILTER_NAME"), text: $editingFilterName)
            Button(LocalizedString("SAVE")) {
                handleFilterEditAction(.rename(editingFilterName))
            }
            Button(LocalizedString("CANCEL"), role: .cancel) { }
        }, message: {
            Text(LocalizedString("FILTER_NAME"))
        })
        // Delete confirmation alert
        .alert(LocalizedString("CONFIRM_DELETE"), isPresented: $showingDeleteAlert) {
            Button(LocalizedString("CANCEL"), role: .cancel) { }
            Button(LocalizedString("DELETE"), role: .destructive) {
                handleFilterEditAction(.delete)
            }
        } message: {
            Text(String(format: LocalizedString("DELETE_FILTER_MESSAGE"), editingFilterName))
        }
        // Import result alert
        .alert(LocalizedString("IMPORT_FAILED"), isPresented: $showingImportAlert) {
            Button(LocalizedString("CANCEL"), role: .cancel) { }
        } message: {
            Text(importAlertMessage)
        }
    }

    // 从 lut.bundle 加载 LUT 滤镜
    private func loadLUTFilters() {
        guard let bundlePath = Bundle.main.path(forResource: "lut", ofType: "bundle"),
              let bundle = Bundle(path: bundlePath) else {
            print("无法找到 lut.bundle")
            return
        }

        guard let resourcePath = bundle.resourcePath else {
            print("无法获取 bundle 资源路径")
            return
        }

        do {
            let fileManager = FileManager.default
            let files = try fileManager.contentsOfDirectory(atPath: resourcePath)

            let lutFiles = files.filter { $0.hasSuffix(".png") }
                .sorted() // 按字母顺序排序

            var loadedFilters: [FilmFilter] = []

            for fileName in lutFiles {
                let displayName = fileName.replacingOccurrences(of: ".png", with: "")
                let identifier = displayName.lowercased()
                    .replacingOccurrences(of: " ", with: "_")
                    .replacingOccurrences(of: "-", with: "_")

                let description = generateDescription(for: displayName)

                let filter = FilmFilter(
                    name: identifier,
                    displayName: displayName,
                    lutFileName: fileName,
                    previewImage: "film_preview_\(identifier)",
                    description: description,
                    isBuiltIn: true,
                    sortOrder: loadedFilters.count
                )

                loadedFilters.append(filter)
            }

            DispatchQueue.main.async {
                // 在列表最前面添加"无"选项
                let noneFilter = FilmFilter(
                    name: "none",
                    displayName: LocalizedString("NONE_FILTER"),
                    lutFileName: "",
                    previewImage: "film_preview_none",
                    description: "不应用任何胶片滤镜",
                    isBuiltIn: true,
                    sortOrder: -1
                )

                var allFilters = [noneFilter]
                allFilters.append(contentsOf: loadedFilters)

                self.filmFilters = allFilters
                print("成功加载 \(loadedFilters.count) 个 LUT 滤镜 + 1 个无滤镜选项:")
                print("  - 无 (不应用滤镜)")
                for filter in loadedFilters {
                    print("  - \(filter.displayName) (\(filter.lutFileName))")
                }

                // 加载用户导入的LUT文件
                self.loadImportedLUTFilters(into: &allFilters)
                self.filmFilters = allFilters

                // 自动设置 CineStill 800t 为默认滤镜（仅在首次加载时）
                if !self.hasSetDefaultFilter {
                    self.setDefaultCineStillFilter(from: allFilters)
                    self.hasSetDefaultFilter = true
                }
            }

        } catch {
            print("读取 lut.bundle 文件时出错: \(error)")
        }
    }

    // 加载用户导入的LUT文件
    private func loadImportedLUTFilters(into allFilters: inout [FilmFilter]) {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let importedLUTsPath = documentsPath.appendingPathComponent("ImportedLUTs")

        guard FileManager.default.fileExists(atPath: importedLUTsPath.path) else {
            print("ImportedLUTs目录不存在，跳过导入的LUT加载")
            return
        }

        do {
            let importedFiles = try FileManager.default.contentsOfDirectory(atPath: importedLUTsPath.path)
            let lutFiles = importedFiles.filter { $0.hasSuffix(".png") }.sorted()

            for fileName in lutFiles {
                let filePath = importedLUTsPath.appendingPathComponent(fileName).path

                // 从文件名解析尺寸信息
                let components = fileName.replacingOccurrences(of: ".png", with: "").components(separatedBy: "_")
                var sizeInfo = ""
                if let sizeComponent = components.last, sizeComponent.contains("x") {
                    sizeInfo = " (\(sizeComponent))"
                }

                let displayName = "导入的LUT\(sizeInfo)"
                let importedFilter = FilmFilter(
                    name: "imported_\(fileName)",
                    displayName: displayName,
                    lutFileName: fileName,
                    previewImage: "film_preview_imported",
                    description: "用户导入的LUT滤镜",
                    isBuiltIn: false,
                    sortOrder: 1000 + allFilters.count
                )

                allFilters.append(importedFilter)
                print("加载导入的LUT: \(displayName) (\(fileName))")
            }

            if !lutFiles.isEmpty {
                print("成功加载 \(lutFiles.count) 个导入的LUT文件")
            }

        } catch {
            print("读取导入的LUT文件时出错: \(error)")
        }
    }

    // 根据滤镜名称生成描述
    private func generateDescription(for name: String) -> String {
        let lowercaseName = name.lowercased()

        if lowercaseName.contains("pink") {
            return "温暖粉色调，适合人像摄影"
        } else if lowercaseName.contains("portra") {
            return "经典胶片色彩，自然肤色"
        } else if lowercaseName.contains("fuji") || lowercaseName.contains("eterna") {
            return "柔和色调，优雅质感"
        } else if lowercaseName.contains("cinestill") {
            return "电影感色调，夜景专用"
        } else if lowercaseName.contains("classic") {
            if lowercaseName.contains("noir") {
                return "经典黑白胶片质感"
            } else if lowercaseName.contains("chrome") {
                return "高饱和度，鲜艳色彩"
            } else {
                return "经典胶片风格"
            }
        } else if lowercaseName.contains("nostalgic") {
            return "怀旧复古色调"
        } else if lowercaseName.contains("red") {
            return "暖色调，增强红色表现"
        } else if lowercaseName.contains("ricoh") {
            return "理光胶片风格"
        } else if lowercaseName.contains("500t") || lowercaseName.contains("5203") {
            return "专业电影胶片色彩"
        } else {
            return "独特胶片色彩风格"
        }
    }

    // 更新滤镜强度
    private func updateFilterIntensity(_ intensity: Float) {
        guard let cameraVC = CameraView.currentCameraViewController,
              let metalRenderer = cameraVC.getMetalRenderer() else {
            return
        }

        metalRenderer.setLUTIntensity(intensity)
        print("更新胶片滤镜强度: \(Int(intensity * 100))%")
    }

    // 同步强度值到 ControlButtonsView 和 GlassDisplayViewModel
    private func syncIntensityWithControlButtons(_ intensity: Float) {
        // 通过通知更新 GlassDisplayViewModel 的胶片强度
        NotificationCenter.default.post(
            name: NSNotification.Name("UpdateFilmIntensity"),
            object: nil,
            userInfo: ["intensity": intensity]
        )

        // 通过通知更新 ControlButtonsView 的胶片强度（用于转盘同步）
        NotificationCenter.default.post(
            name: NSNotification.Name("SyncFilmIntensity"),
            object: nil,
            userInfo: ["intensity": intensity]
        )

        print("同步胶片滤镜强度到控制按钮: \(Int(intensity * 100))%")
    }

    // 设置默认 CineStill 800t 滤镜
    private func setDefaultCineStillFilter(from filters: [FilmFilter]) {
        // 查找 CineStill 800t 滤镜
        let cineStillFilter = filters.first { filter in
            filter.displayName.lowercased().contains("cinestill") &&
            filter.displayName.lowercased().contains("800t")
        }

        if let defaultFilter = cineStillFilter {
            print("设置默认胶片滤镜: \(defaultFilter.displayName)")
            selectedFilter = defaultFilter
            applyFilmFilter(defaultFilter)

            // 同步更新 GlassDisplayViewModel 的胶片强度
            if let cameraVC = CameraView.currentCameraViewController {
                // 通过通知更新显示视图模型
                NotificationCenter.default.post(
                    name: NSNotification.Name("UpdateFilmIntensity"),
                    object: nil,
                    userInfo: ["intensity": filterIntensity]
                )
            }
        } else {
            print("未找到 CineStill 800t 滤镜，使用默认无滤镜状态")
        }
    }

    // 从DocumentPicker导入LUT文件
    private func importLUTFromURL(_ url: URL) {
        // 确保可以访问文件
        guard url.startAccessingSecurityScopedResource() else {
            showImportError("无法访问选择的文件")
            return
        }

        defer {
            url.stopAccessingSecurityScopedResource()
        }

        let fileName = url.lastPathComponent
        let fileExtension = url.pathExtension.lowercased()

        do {
            if fileExtension == "cube" {
                // 处理 .cube 文件
                processCubeLUTFile(url: url, fileName: fileName)
            } else {
                // 处理 PNG 文件
                let data = try Data(contentsOf: url)
                // 显示标题编辑器
                pendingImportData = (data: data, fileName: fileName, isFromCube: false)
                customTitle = fileName.replacingOccurrences(of: ".png", with: "")
                showingTitleEditor = true
            }
        } catch {
            showImportError("读取文件失败: \(error.localizedDescription)")
        }
    }

    // 处理 .cube LUT 文件
    private func processCubeLUTFile(url: URL, fileName: String) {
        do {
            // 解析 .cube 文件
            let cubeLUT = try CubeLUTParser.parse(from: url)

            // 验证支持的维度
            let supportedDimensions = [16, 32, 64]
            guard supportedDimensions.contains(cubeLUT.dimension) else {
                var errorMessage = "不支持的LUT维度: \(cubeLUT.dimension)\n支持的维度: 16, 32, 64"

                // 对于65维度的特殊提示
                if cubeLUT.dimension == 65 {
                    errorMessage += "\n\n提示: 检测到65维度，这通常表示CUBE文件格式有问题。\n请确认文件是标准的3D LUT格式，且LUT_3D_SIZE声明正确。"
                } else if cubeLUT.dimension > 64 {
                    errorMessage += "\n\n提示: 维度过大可能导致性能问题。\n建议使用64维度或更小的LUT文件。"
                }

                showImportError(errorMessage)
                return
            }

            // 显示标题编辑器
            pendingCubeLUT = cubeLUT
            customTitle = cubeLUT.title ?? fileName.replacingOccurrences(of: ".cube", with: "")
            showingTitleEditor = true

        } catch let error as CubeLUTError {
            showImportError("CUBE文件解析失败:\n\(error.localizedDescription)")
        } catch {
            showImportError("处理CUBE文件时出错: \(error.localizedDescription)")
        }
    }



    // 测试导入的LUT文件
    private func testImportedLUT(lutFileURL: URL, filter: FilmFilter) {
        guard let cameraVC = CameraView.currentCameraViewController,
              let metalRenderer = cameraVC.getMetalRenderer() else {
            print("无法获取相机控制器进行LUT测试")
            return
        }

        // 尝试加载导入的LUT
        if let lutImage = UIImage(contentsOfFile: lutFileURL.path),
           let cgImage = lutImage.cgImage {

            // 使用MetalRenderer的loadLUT方法测试
            // 注意：这里需要修改MetalRenderer以支持外部文件路径
            print("测试导入的LUT: \(filter.displayName)")
            print("文件路径: \(lutFileURL.path)")
            print("图片尺寸: \(cgImage.width)×\(cgImage.height)")
        }
    }

    // 显示导入成功消息
    private func showImportSuccess(_ message: String) {
        importAlertMessage = message
        showingImportAlert = true
    }

    // 显示导入错误消息
    private func showImportError(_ message: String) {
        importAlertMessage = message
        showingImportAlert = true
    }

    // 完成LUT导入（PNG文件）
    private func finalizeLUTImport(data: Data, fileName: String, customTitle: String, isFromCube: Bool) {
        guard let image = UIImage(data: data) else {
            showImportError("无法解析图片格式")
            return
        }

        // 验证LUT图片格式
        let width = Int(image.size.width * image.scale)
        let height = Int(image.size.height * image.scale)

        // 检查是否为支持的LUT格式
        let isValidLUT = (width == 512 && height == 512) ||  // 64³ LUT (512x512)
                        (width == 1024 && height == 32) ||   // 64³ LUT (1024x32)
                        (width == 256 && height == 256) ||   // 32³ LUT (256x256)
                        (width == 64 && height == 64)       // 16³ LUT (64x64)

        if !isValidLUT {
            showImportError("不支持的PNG LUT格式\n支持的PNG格式:\n• 512×512 (64³ LUT)\n• 1024×32 (64³ LUT)\n• 256×256 (32³ LUT)\n• 64×64 (16³ LUT)\n\n当前图片: \(width)×\(height)\n\n提示: 也支持导入 .cube 格式的LUT文件")
            return
        }

        // 保存LUT文件到Documents目录
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let lutFileName = "imported_\(Date().timeIntervalSince1970)_\(width)x\(height).png"
        let lutFileURL = documentsPath.appendingPathComponent("ImportedLUTs").appendingPathComponent(lutFileName)

        // 创建ImportedLUTs目录
        try? FileManager.default.createDirectory(at: lutFileURL.deletingLastPathComponent(), withIntermediateDirectories: true)

        // 保存PNG文件
        if let pngData = image.pngData() {
            do {
                try pngData.write(to: lutFileURL)

                // 创建新的滤镜条目
                let displayName = customTitle.isEmpty ? "导入的LUT \(width)×\(height)" : customTitle
                let newFilter = FilmFilter(
                    name: "imported_\(lutFileName)",
                    displayName: displayName,
                    lutFileName: lutFileName,
                    previewImage: "film_preview_imported",
                    description: "用户导入的LUT滤镜 (\(width)×\(height))",
                    isBuiltIn: false,
                    sortOrder: 1000 + filmFilters.count
                )

                // 添加到滤镜列表
                filmFilters.append(newFilter)

                // 测试应用新导入的LUT
                testImportedLUT(lutFileURL: lutFileURL, filter: newFilter)

                showImportSuccess("LUT导入成功！\n标题: \(displayName)\n格式: \(width)×\(height)\n色深: 8位/通道 (32位RGBA)")

            } catch {
                showImportError("保存LUT文件失败: \(error.localizedDescription)")
            }
        } else {
            showImportError("无法生成PNG数据")
        }

        // 清理临时数据
        pendingImportData = nil
        self.customTitle = ""
    }

    // 完成CUBE LUT导入
    private func finalizeCubeLUTImport(cubeLUT: CubeLUTParser.CubeLUT, customTitle: String) {
        // 将 .cube LUT 转换为 PNG 图像
        guard let lutImage = CubeLUTParser.convertToImage(cubeLUT: cubeLUT) else {
            showImportError("无法将 .cube 文件转换为图像格式")
            return
        }

        // 保存转换后的 PNG 文件
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let pngFileName = "cube_\(Date().timeIntervalSince1970)_\(cubeLUT.dimension)d.png"
        let lutFileURL = documentsPath.appendingPathComponent("ImportedLUTs").appendingPathComponent(pngFileName)

        // 创建ImportedLUTs目录
        try? FileManager.default.createDirectory(at: lutFileURL.deletingLastPathComponent(), withIntermediateDirectories: true)

        // 保存PNG文件
        if let pngData = lutImage.pngData() {
            do {
                try pngData.write(to: lutFileURL)

                // 创建新的滤镜条目
                let displayName = customTitle.isEmpty ? "导入的CUBE LUT (\(cubeLUT.dimension)³)" : customTitle
                let newFilter = FilmFilter(
                    name: "imported_cube_\(pngFileName)",
                    displayName: displayName,
                    lutFileName: pngFileName,
                    previewImage: "film_preview_cube",
                    description: "从 .cube 文件导入的LUT滤镜 (\(cubeLUT.dimension)³)",
                    isBuiltIn: false,
                    sortOrder: 1000 + filmFilters.count
                )

                // 添加到滤镜列表
                filmFilters.append(newFilter)

                // 测试应用新导入的LUT
                testImportedLUT(lutFileURL: lutFileURL, filter: newFilter)

                let imageSize = lutImage.size
                showImportSuccess("CUBE LUT导入成功！\n标题: \(displayName)\n原始维度: \(cubeLUT.dimension)³\n转换尺寸: \(Int(imageSize.width))×\(Int(imageSize.height))\n色深: 8位/通道 (32位RGBA)")

            } catch {
                showImportError("保存LUT文件失败: \(error.localizedDescription)")
            }
        } else {
            showImportError("无法生成PNG数据")
        }

        // 清理临时数据
        pendingCubeLUT = nil
        self.customTitle = ""
    }

    // 处理滤镜编辑操作
    private func handleFilterEditAction(_ action: FilterEditAction) {
        guard let filter = editingFilter else { return }

        switch action {
        case .rename(let newName):
            // 重命名滤镜
            if let index = filmFilters.firstIndex(where: { $0.id == filter.id }) {
                filmFilters[index].displayName = newName
                saveFilterSettings()
                print("滤镜重命名: \(filter.displayName) → \(newName)")
            }

        case .delete:
            // 删除滤镜（仅限导入的滤镜）
            if !filter.isBuiltIn {
                // 删除文件
                if filter.name.hasPrefix("imported_") {
                    let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
                    let lutFileURL = documentsPath.appendingPathComponent("ImportedLUTs").appendingPathComponent(filter.lutFileName)
                    try? FileManager.default.removeItem(at: lutFileURL)
                    print("删除LUT文件: \(lutFileURL.path)")
                }

                // 从列表中移除
                filmFilters.removeAll { $0.id == filter.id }

                // 如果删除的是当前选中的滤镜，切换到"无"滤镜
                if selectedFilter?.id == filter.id {
                    selectedFilter = nil
                    if let noneFilter = filmFilters.first(where: { $0.name == "none" }) {
                        selectedFilter = noneFilter
                        applyFilmFilter(noneFilter)
                    }
                }

                saveFilterSettings()
                print("滤镜删除成功: \(filter.displayName)")
            } else {
                print("内置滤镜无法删除: \(filter.displayName)")
            }
        }
    }

    // 保存滤镜设置
    private func saveFilterSettings() {
        // TODO: 实现滤镜设置的持久化存储
        print("保存滤镜设置")
    }

    // 应用胶片滤镜的方法
    private func applyFilmFilter(_ filter: FilmFilter) {
        // 获取当前的相机视图控制器和Metal渲染器
        guard let cameraVC = CameraView.currentCameraViewController,
              let metalRenderer = cameraVC.getMetalRenderer() else {
            print("无法获取相机控制器或Metal渲染器")
            return
        }

        if filter.name == "none" {
            print("取消胶片滤镜效果")
            metalRenderer.disableLUT()
        } else {
            print("应用胶片滤镜: \(filter.displayName) (文件: \(filter.lutFileName))")

            // 检查是否为导入的LUT
            if filter.name.hasPrefix("imported_") {
                // 处理导入的LUT文件（包括PNG和从CUBE转换的PNG）
                let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
                let lutFileURL = documentsPath.appendingPathComponent("ImportedLUTs").appendingPathComponent(filter.lutFileName)

                if FileManager.default.fileExists(atPath: lutFileURL.path) {
                    let lutType = filter.name.hasPrefix("imported_cube_") ? "CUBE转换的LUT" : "导入的PNG LUT"
                    print("应用\(lutType)文件: \(lutFileURL.path)")
                    metalRenderer.loadLUTFromPath(lutFileURL.path, fileName: filter.displayName)
                } else {
                    print("导入的LUT文件不存在: \(lutFileURL.path)")
                }
            } else {
                // 处理bundle中的LUT文件
                metalRenderer.loadLUT(from: filter.lutFileName)
            }

            metalRenderer.setLUTIntensity(filterIntensity)  // 应用当前强度，与ControlButtonsView保持一致
        }
    }
}

// MARK: - 胶片滤镜卡片组件
struct FilmFilterCard: View {
    let filter: FilmSimulationPanel.FilmFilter
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 预览图像
                RoundedRectangle(cornerRadius: 8)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.gray.opacity(0.3),
                                Color.gray.opacity(0.1)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 50, height: 50)
                    .overlay(
                        // 根据滤镜类型显示不同图标
                        Image(systemName: getFilterIcon(for: filter))
                            .font(.system(size: 20))
                            .foregroundColor(.white.opacity(0.7))
                    )

                // 滤镜信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(filter.displayName)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.white)
                        .lineLimit(1)

                    Text(filter.description)
                        .font(.system(size: 11))
                        .foregroundColor(.white.opacity(0.7))
                        .lineLimit(2)
                }

                Spacer()

                // 选中状态指示器
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18))
                        .foregroundColor(.blue)
                } else {
                    Image(systemName: "circle")
                        .font(.system(size: 18))
                        .foregroundColor(.white.opacity(0.3))
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 10)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(
                        isSelected ?
                        Color.blue.opacity(0.2) :
                        Color.white.opacity(0.05)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(
                                isSelected ?
                                Color.blue.opacity(0.5) :
                                Color.white.opacity(0.1),
                                lineWidth: 1
                            )
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // 根据滤镜类型返回对应图标
    private func getFilterIcon(for filter: FilmSimulationPanel.FilmFilter) -> String {
        if filter.name == "none" {
            return "xmark"
        } else if filter.name.hasPrefix("imported_cube_") {
            return "cube"  // CUBE文件图标
        } else if filter.name.hasPrefix("imported_") {
            return "square.and.arrow.down"  // PNG导入图标
        } else {
            return "camera.filters"  // 默认滤镜图标
        }
    }
}

// MARK: - 滤镜编辑操作枚举
enum FilterEditAction {
    case rename(String)
    case delete
}

// MARK: - 简化的滤镜行组件
struct FilmFilterRow: View {
    let filter: FilmSimulationPanel.FilmFilter
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack {
                Text(filter.displayName)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.leading)

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue.opacity(0.2) : Color.white.opacity(0.1))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 标题编辑器
struct TitleEditorView: View {
    @Binding var title: String
    let onSave: () -> Void
    let onCancel: () -> Void

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text(LocalizedString("SET_LUT_TITLE"))
                    .font(.title2)
                    .fontWeight(.semibold)
                    .padding(.top, 20)

                TextField(LocalizedString("ENTER_FILTER_NAME"), text: $title)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding(.horizontal, 20)

                Spacer()
            }
            .navigationTitle(LocalizedString("IMPORT_LUT_TITLE"))
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button(LocalizedString("CANCEL"), action: onCancel),
                trailing: Button(LocalizedString("SAVE"), action: onSave)
                    .disabled(title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            )
        }
    }
}



// MARK: - DocumentPicker for iOS compatibility
struct DocumentPicker: UIViewControllerRepresentable {
    let allowedContentTypes: [UTType]
    let onDocumentPicked: (URL) -> Void

    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: allowedContentTypes)
        picker.delegate = context.coordinator
        picker.allowsMultipleSelection = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {
        // No updates needed
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentPicker

        init(_ parent: DocumentPicker) {
            self.parent = parent
        }

        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            guard let url = urls.first else { return }
            parent.onDocumentPicked(url)
        }

        func documentPickerWasCancelled(_ controller: UIDocumentPickerViewController) {
            // User cancelled, no action needed
        }
    }
}

// MARK: - 预览
struct FilmSimulationPanel_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)

            FilmSimulationPanel(isPresented: .constant(true))
        }
    }
}
