import Foundation
import AVFoundation
import UIKit

class FilterManager {
    private(set) var isBlackMistEnabled: Bool = false

    var blackMistThreshold: Float = 0.03 {
        didSet { metalRenderer?.setThreshold(blackMistThreshold); requestUpdateAndLog("Threshold: \(blackMistThreshold)") }
    }
    var blackMistIntensity: Float = 0.3 {
        didSet { metalRenderer?.setBloomIntensity(blackMistIntensity); requestUpdateAndLog("Intensity: \(blackMistIntensity)") }
    }
    var brightness: Float = 0 {
        didSet { metalRenderer?.setBrightness(brightness); requestUpdateAndLog("Brightness: \(brightness)") }
    }
    var redTint: Float = 0.0 {
        didSet { metalRenderer?.setRedTint(redTint); requestUpdateAndLog("Red Tint: \(redTint)") }
    }
    var greenTint: Float = 0.0 {
        didSet { metalRenderer?.setGreenTint(greenTint); requestUpdateAndLog("Green Tint: \(greenTint)") }
    }
    var blueTint: Float = 0.0 {
        didSet { metalRenderer?.setBlueTint(blueTint); requestUpdateAndLog("Blue Tint: \(blueTint)") }
    }

    private weak var metalRenderer: MetalRenderer?
    private weak var videoOutput: AVCaptureVideoDataOutput?

    var onFilterStateChanged: ((Bool) -> Void)?
    var requestUIUpdateForFilter: (() -> Void)?

    init(metalRenderer: MetalRenderer?, videoOutput: AVCaptureVideoDataOutput?, initialBlackMistEnabled: Bool = true) {
        self.metalRenderer = metalRenderer
        self.videoOutput = videoOutput
        self.isBlackMistEnabled = initialBlackMistEnabled // Set our desired state

        applyCurrentNumericParametersToRenderer() // Apply numeric parameters to renderer

        // If the filter should be initially enabled, and assuming MetalRenderer starts with the filter OFF,
        // one call to toggleBlackMist() will turn it ON in the renderer.
        // This matches the original CameraViewController's viewDidLoad logic.
        if initialBlackMistEnabled {
            self.metalRenderer?.toggleBlackMist()
        }
        // If initialBlackMistEnabled is false, we assume MetalRenderer also starts with filter OFF,
        // so no toggle is needed to keep them in sync (both off).

        print("FilterManager initialized. Black Mist: \(self.isBlackMistEnabled)")
    }

    // Renamed from applyInitialParameters
    private func applyCurrentNumericParametersToRenderer() {
        metalRenderer?.setThreshold(blackMistThreshold)
        metalRenderer?.setBloomIntensity(blackMistIntensity)
        metalRenderer?.setBrightness(brightness)
        metalRenderer?.setRedTint(redTint)
        metalRenderer?.setGreenTint(greenTint)
        metalRenderer?.setBlueTint(blueTint)
        // Removed enableBlackMist/disableBlackMist calls from here
        print("FilterManager: All current numeric parameters applied to MetalRenderer.")
    }

    private func ensureVideoOrientation() {
        guard let connection = videoOutput?.connection(with: .video), connection.isVideoOrientationSupported else { return }

        // Only change orientation if needed
        if connection.videoOrientation != .landscapeRight {
            // Ensure we're not in the middle of processing a frame when changing orientation
            DispatchQueue.main.async { [weak self] in
                // Set video output connection orientation
                connection.videoOrientation = .landscapeRight

                // Notify that orientation has changed so CameraViewController can update preview layer
                NotificationCenter.default.post(name: NSNotification.Name("VideoOrientationChanged"), object: nil)

                print("FilterManager: Video orientation set to landscapeRight due to filter change.")
            }
        }
    }

    private func requestUpdateAndLog(_ message: String) {
        ensureVideoOrientation()
        requestUIUpdateForFilter?()
        print("FilterManager: \(message)")
    }

    @discardableResult
    func toggleBlackMist() -> Bool {
        isBlackMistEnabled.toggle() // 1. Flip FilterManager's internal state
        metalRenderer?.toggleBlackMist() // 2. Flip MetalRenderer's internal state

        print("FilterManager: Black Mist toggled to \(isBlackMistEnabled)")
        if isBlackMistEnabled {
            // If the filter was just turned ON, ensure all current numeric parameters are applied.
            applyCurrentNumericParametersToRenderer()
        }
        // No 'else' needed to remove parameters, as disabling the effect is handled by MetalRenderer internally.

        ensureVideoOrientation()
        onFilterStateChanged?(isBlackMistEnabled)
        requestUIUpdateForFilter?()
        return isBlackMistEnabled
    }

    func resetToDefaults() {
        blackMistThreshold = 0.03
        blackMistIntensity = 0.3
        brightness = 0
        redTint = 0.0
        greenTint = 0.0
        blueTint = 0.0
        applyCurrentNumericParametersToRenderer() // Ensure defaults are propagated to the renderer
        print("FilterManager: Parameters reset to defaults.")
    }

    // Clean up resources
    func cleanup() {
        metalRenderer?.cleanupTextures()
        metalRenderer = nil
        videoOutput = nil
        onFilterStateChanged = nil
        requestUIUpdateForFilter = nil
        print("FilterManager: Resources cleaned up")
    }

    deinit {
        cleanup()
        print("FilterManager deallocated")
    }
}
