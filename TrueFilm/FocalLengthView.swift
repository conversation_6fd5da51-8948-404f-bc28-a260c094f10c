//
//  FocalLengthView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/20.
//

import SwiftUI
import AVFoundation
import TrueFilm

struct FocalLengthView: View {
    // 当前镜头类型
    @Binding var currentLensType: CameraLensType

    // 是否是多镜头相机
    var isMultiLensCamera: Bool

    // 可用的镜头类型
    var availableLensTypes: [CameraLensType]

    // 切换镜头的回调
    var onLensSwitch: (CameraLensType) -> Void

    // UI模式
    @State private var isWhiteMode: Bool = UserDefaults.standard.bool(forKey: "whiteUIMode")

    // 拖动手势状态
    @State private var dragOffset: CGFloat = 0
    @State private var isDragging: Bool = false

    // 动画状态
    @State private var isAnimating: Bool = false
    
    // TipKit 已移除

    // Add language refresh ID
    @State private var languageRefreshID = UUID()
    
    // Add a computed property to identify if current lens is digital zoom
    private var isDigitalZoom: Bool {
        return currentLensType.isDigitalZoom
    }

    var body: some View {
        // 焦距显示 - 带有左右指示点
        ZStack {
            // 添加调试信息
            Text("滑动切换: \(isDragging ? "拖动中" : "未拖动") 偏移: \(Int(dragOffset))")
                .font(.system(size: 8))
                .foregroundColor(.clear) // 设为透明，仅用于调试
            // 背景透明区域，增大滑动区域
            Color.clear
                .frame(width: 180, height: 60)
                .contentShape(Rectangle())

            HStack(spacing: 4) {
                // 左侧点 - 只在有更广角镜头可用时显示
                if hasWiderLens() {
                    Text("·")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: .black, radius: 2, x: 1, y: 1)
                        .opacity(0.8)
                }

                // 焦距显示（使用镜头倍数）
                Text(currentLensType.displayName)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(isDigitalZoom ? .yellow : .white) // Highlight digital zoom in yellow
                    .shadow(color: .black, radius: 2, x: 1, y: 1)

                // 右侧点 - 只在有更长焦镜头可用时显示
                if hasLongerLens() {
                    Text("·")
                        .font(.system(size: 16, weight: .bold))
                        .foregroundColor(.white)
                        .shadow(color: .black, radius: 2, x: 1, y: 1)
                        .opacity(0.8)
                }
            }
            .offset(x: isDragging ? dragOffset * 0.2 : 0) // 减少偏移量，降低渲染负担
            .scaleEffect(isDragging ? 1.05 : 1.0) // 减少缩放量，降低渲染负担
            // 使用更简单的动画替代复杂的弹性动画
            .animation(isDragging ? .easeOut(duration: 0.1) : nil, value: isDragging)
        }
        // Add ID to force refresh on language change
        .id("focal_length_\(languageRefreshID)")
        // Add tap gesture to cycle through available lens options
        .onTapGesture {
            guard isMultiLensCamera, !availableLensTypes.isEmpty else { return }
            
            // Play switch sound
            AudioManager.shared.playSwitchSound()
            
            // Get the next lens option in the cycle
            cycleThroughLenses()
        }
        .gesture(
            DragGesture()
                .onChanged { gesture in
                    // 只有多镜头相机才响应拖动手势
                    guard isMultiLensCamera else { return }

                    isDragging = true
                    dragOffset = gesture.translation.width
                }
                .onEnded { gesture in
                    // 只有多镜头相机才响应拖动手势
                    guard isMultiLensCamera else {
                        isDragging = false
                        dragOffset = 0
                        return
                    }

                    // 根据拖动方向切换镜头
                    let threshold: CGFloat = 30

                    if dragOffset > threshold {
                        // 向右滑动，焦距变窄（增加焦距，切换到更长焦的镜头）
                        switchToNextLens()
                    } else if dragOffset < -threshold {
                        // 向左滑动，焦距变广（减小焦距，切换到更广角的镜头）
                        switchToPreviousLens()
                    }

                    // 重置拖动状态
                    isDragging = false
                    dragOffset = 0
                }
        )
        .onAppear {
            // Debug: Print available lens types
            print("🎯 FocalLengthView - 可用镜头: \(availableLensTypes.map { "\($0.displayName) (\($0.isDigitalZoom ? "数码" : "光学"))" }.joined(separator: ", "))")
            print("📷 FocalLengthView - 当前镜头: \(currentLensType.displayName) (\(currentLensType.isDigitalZoom ? "数码" : "光学"))")
            print("🔢 FocalLengthView - 多镜头相机: \(isMultiLensCamera)")
            
            // 初始化镜头缓存
            updateLensCache()
            
            // Subscribe to UI mode changes
            NotificationCenter.default.addObserver(forName: NSNotification.Name("UIModeChanged"), object: nil, queue: .main) { _ in
                // Update UI mode from UserDefaults
                self.isWhiteMode = UserDefaults.standard.bool(forKey: "whiteUIMode")
            }
            
            // Subscribe to language changes
            NotificationCenter.default.addObserver(forName: .languageDidChange, object: nil, queue: .main) { _ in
                print("FocalLengthView: 检测到语言变更，刷新UI")
                // Force UI refresh by updating ID
                self.languageRefreshID = UUID()
            }
        }
        .onChange(of: availableLensTypes) { _ in
            // 当镜头列表变化时更新缓存
            updateLensCache()
        }
        .onDisappear {
            NotificationCenter.default.removeObserver(self)
        }
    }

    // 切换到下一个镜头（按倍数排序）- 向右滑动，变窄（增加焦距）
    private func switchToNextLens() {
        print("🔄 switchToNextLens 被调用")
        print("📝 可用镜头数量: \(availableLensTypes.count)")
        guard !availableLensTypes.isEmpty else { 
            print("❌ 没有可用镜头")
            return 
        }

        // 获取按倍数排序的镜头
        let sortedLenses = getSortedLensesByMultiplier()
        
        // 找到当前镜头在排序列表中的位置
        if let currentIndex = sortedLenses.firstIndex(where: { $0.id == currentLensType.rawValue }) {
            print("📍 当前镜头索引: \(currentIndex), 总镜头数: \(sortedLenses.count)")
            
            // 检查是否已经是最长焦的镜头
            if currentIndex >= sortedLenses.count - 1 {
                // 已经是最长焦的镜头，不做任何操作
                print("⚠️ 已经是最长焦的镜头，无法继续")
                return
            }
            
            // 计算下一个镜头的索引
            let nextIndex = currentIndex + 1
            print("➡️ 下一个镜头索引: \(nextIndex)")
            
            // 获取对应的下一个镜头类型
            if let nextLensType = CameraLensType(rawValue: sortedLenses[nextIndex].id) {
                // 播放镜头切换音效
                AudioManager.shared.playSwitchSound()
                
                print("切换到下一个镜头: \(nextLensType.displayName) (\(nextLensType.isDigitalZoom ? "数码变焦" : "光学镜头"))")
                
                // 切换到下一个镜头
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    onLensSwitch(nextLensType)
                }
            }
        } else if let firstLens = CameraLensType(rawValue: sortedLenses.first?.id ?? 1) {
            // 如果找不到当前镜头，切换到第一个镜头
            print("⚠️ 找不到当前镜头，切换到第一个镜头")
            // 播放镜头切换音效
            AudioManager.shared.playSwitchSound()
            
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                onLensSwitch(firstLens)
            }
        }
    }

    // 切换到上一个镜头（按倍数排序）- 向左滑动，变广（减小焦距）
    private func switchToPreviousLens() {
        print("🔄 switchToPreviousLens 被调用")
        print("📝 可用镜头数量: \(availableLensTypes.count)")
        guard !availableLensTypes.isEmpty else { 
            print("❌ 没有可用镜头")
            return 
        }

        // 获取按倍数排序的镜头
        let sortedLenses = getSortedLensesByMultiplier()
        
        // 找到当前镜头在排序列表中的位置
        if let currentIndex = sortedLenses.firstIndex(where: { $0.id == currentLensType.rawValue }) {
            print("📍 当前镜头索引: \(currentIndex), 总镜头数: \(sortedLenses.count)")
            
            // 检查是否已经是最广角的镜头
            if currentIndex <= 0 {
                // 已经是最广角的镜头，不做任何操作
                print("⚠️ 已经是最广角的镜头，无法继续")
                return
            }
            
            // 计算上一个镜头的索引
            let previousIndex = currentIndex - 1
            print("⬅️ 上一个镜头索引: \(previousIndex)")
            
            // 获取对应的上一个镜头类型
            if let previousLensType = CameraLensType(rawValue: sortedLenses[previousIndex].id) {
                // 播放镜头切换音效
                AudioManager.shared.playSwitchSound()
                
                print("切换到上一个镜头: \(previousLensType.displayName) (\(previousLensType.isDigitalZoom ? "数码变焦" : "光学镜头"))")
                
                // 切换到上一个镜头
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    onLensSwitch(previousLensType)
                }
            }
        } else if let firstLens = CameraLensType(rawValue: sortedLenses.first?.id ?? 1) {
            // 如果找不到当前镜头，切换到第一个镜头
            print("⚠️ 找不到当前镜头，切换到第一个镜头")
            // 播放镜头切换音效
            AudioManager.shared.playSwitchSound()
            
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                onLensSwitch(firstLens)
            }
        }
    }

    // 检查是否有更广角的镜头可用
    private func hasWiderLens() -> Bool {
        guard isMultiLensCamera, !availableLensTypes.isEmpty else { return false }

        // 按照倍数（displayName）排序镜头
        let sortedLenses = getSortedLensesByMultiplier()
        if let currentIndex = sortedLenses.firstIndex(where: { $0.id == currentLensType.rawValue }) {
            // 检查是否有更广角的镜头（索引更小的镜头）
            return currentIndex > 0
        }
        return false
    }

    // 检查是否有更长焦的镜头可用
    private func hasLongerLens() -> Bool {
        guard isMultiLensCamera, !availableLensTypes.isEmpty else { return false }

        // 按照倍数（displayName）排序镜头
        let sortedLenses = getSortedLensesByMultiplier()
        if let currentIndex = sortedLenses.firstIndex(where: { $0.id == currentLensType.rawValue }) {
            // 检查是否有更长焦的镜头（索引更大的镜头）
            return currentIndex < sortedLenses.count - 1
        }
        return false
    }
    
    // 缓存排序结果，避免重复计算
    @State private var cachedSortedLenses: [(id: Int, multiplier: Double)] = []
    @State private var lastAvailableLensTypes: [CameraLensType] = []
    
    // 获取按倍数排序的镜头列表 - 只读版本，不修改状态
    private func getSortedLensesByMultiplier() -> [(id: Int, multiplier: Double)] {
        // 如果缓存有效，直接返回缓存结果
        if !cachedSortedLenses.isEmpty && lastAvailableLensTypes == availableLensTypes {
            return cachedSortedLenses
        }
        
        // 如果缓存无效，重新计算但不修改状态（避免在视图更新时修改状态）
        let sortedResult = availableLensTypes.map { lens -> (id: Int, multiplier: Double) in
            // 使用镜头的变焦倍数
            let multiplier = lens.zoomFactor
            
            // 前置摄像头使用负值，确保排在最左侧
            let adjustedMultiplier = lens == .front ? -1.0 : multiplier
            
            return (id: lens.rawValue, multiplier: adjustedMultiplier)
        }.sorted(by: { $0.multiplier < $1.multiplier })
        
        return sortedResult
    }
    
    // 更新缓存的方法 - 只在适当的时机调用
    private func updateLensCache() {
        let newSortedLenses = availableLensTypes.map { lens -> (id: Int, multiplier: Double) in
            let multiplier = lens.zoomFactor
            let adjustedMultiplier = lens == .front ? -1.0 : multiplier
            return (id: lens.rawValue, multiplier: adjustedMultiplier)
        }.sorted(by: { $0.multiplier < $1.multiplier })
        
        cachedSortedLenses = newSortedLenses
        lastAvailableLensTypes = availableLensTypes
        
        // 减少日志输出频率 - 只在镜头列表变化时打印一次
        if !availableLensTypes.isEmpty {
            print("🔄 排序后的镜头列表:")
            for (index, lens) in newSortedLenses.enumerated() {
                if let lensType = CameraLensType(rawValue: lens.id) {
                    print("  \(index): \(lensType.displayName) (multiplier: \(lens.multiplier), digital: \(lensType.isDigitalZoom))")
                }
            }
        }
    }

    // Add a method to cycle through lens options when tapping the focal length indicator
    private func cycleThroughLenses() {
        guard !availableLensTypes.isEmpty else { return }
        
        // Get the sorted lenses
        let sortedLenses = getSortedLensesByMultiplier()
        
        // Find the current lens index
        if let currentIndex = sortedLenses.firstIndex(where: { $0.id == currentLensType.rawValue }) {
            // Calculate the next lens index (cycle back to first if needed)
            let nextIndex = (currentIndex + 1) % sortedLenses.count
            
            // Get the next lens type
            if let nextLensType = CameraLensType(rawValue: sortedLenses[nextIndex].id) {
                print("循环切换镜头: \(nextLensType.displayName) (\(nextLensType.isDigitalZoom ? "数码变焦" : "光学镜头"))")
                
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    onLensSwitch(nextLensType)
                }
            }
        } else {
            // If current lens not found, switch to the first lens
            if let firstLens = CameraLensType(rawValue: sortedLenses.first?.id ?? 1) {
                print("循环切换镜头（回到第一个）: \(firstLens.displayName) (\(firstLens.isDigitalZoom ? "数码变焦" : "光学镜头"))")
                
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    onLensSwitch(firstLens)
                }
            }
        }
    }
}

#Preview {
    // 预览用的示例数据
    let availableLenses = CameraLensType.allCases

    return FocalLengthView(
        currentLensType: .constant(.wide),
        isMultiLensCamera: true,
        availableLensTypes: availableLenses,
        onLensSwitch: { _ in }
    )
    // Using traits in the Preview macro is preferred over previewLayout
    .padding()
    .background(Color.gray)
}
