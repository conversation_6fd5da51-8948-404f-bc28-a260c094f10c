//
//  GalleryViews.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/22.
//

import SwiftUI
import Foundation
import UIKit
import AVKit
import Combine

// 媒体类型
enum MediaType: Equatable {
    case photo(UIImage)
    case video(URL)
    
    // 实现Equatable协议的相等判断
    static func == (lhs: MediaType, rhs: MediaType) -> Bool {
        switch (lhs, rhs) {
        case (.photo(let lhsImage), .photo(let rhsImage)):
            // 图片比较：使用内存地址比较，因为图片内容比较是昂贵的
            return lhsImage === rhsImage
        case (.video(let lhsURL), .video(let rhsURL)):
            // 视频比较：使用URL路径比较
            return lhsURL == rhsURL
        default:
            // 不同类型不相等
            return false
        }
    }
    
    var thumbnail: UIImage? {
        switch self {
        case .photo(let image):
            return image
        case .video(let url):
            // 使用优化的缓存技术获取缩略图
            return AppMediaStorage.shared.generateVideoThumbnail(for: url)
        }
    }
    
    var duration: TimeInterval? {
        switch self {
        case .photo(_):
            return nil
        case .video(let url):
            // 首先检查文件是否存在
            guard FileManager.default.fileExists(atPath: url.path) else {
                print("试图读取时长的视频文件不存在: \(url.path)")
                return nil
            }
            
            // 使用 AVAssetTrack 直接读取时长，更可靠
            let asset = AVAsset(url: url)
            
            // 使用同步方法加载时长属性，避免异步问题
            if asset.duration.seconds.isNaN || asset.duration.seconds == 0 {
                // 如果直接读取无效，尝试从视频附属轨道读取时长
                let tracks = asset.tracks(withMediaType: .video)
                if let videoTrack = tracks.first {
                    // 从轨道中获取时长
                    let trackDuration = videoTrack.timeRange.duration.seconds
                    if trackDuration > 0 {
                        return trackDuration
                    }
                }
                
                // 如果在音频轨道中查找
                let audioTracks = asset.tracks(withMediaType: .audio)
                if let audioTrack = audioTracks.first {
                    let audioLength = audioTrack.timeRange.duration.seconds
                    if audioLength > 0 {
                        return audioLength
                    }
                }
                
                // 还是读不到，测量文件大小来估计时长
                do {
                    let fileAttributes = try FileManager.default.attributesOfItem(atPath: url.path)
                    if let fileSize = fileAttributes[.size] as? Int64, fileSize > 1000 {
                        // 粗略估计: 假设每秒1MB文件大小
                        let estimatedDuration = max(Double(fileSize) / 1_000_000.0, 1.0)
                        print("基于文件大小估计视频时长: \(estimatedDuration) 秒")
                        return estimatedDuration 
                    }
                } catch {
                    print("无法读取文件属性: \(error.localizedDescription)")
                }
                
                print("无法获取视频时长，使用默认值")
                return 10.0  // 默认返回10秒
            }
            
            return asset.duration.seconds
        }
    }
}

// 视频播放器包装器 - 处理视频预加载和白屏问题
class VideoPlayerWrapper: NSObject, ObservableObject {
    @Published var player: AVPlayer?
    private var playerItem: AVPlayerItem?
    private var asset: AVAsset?
    private var isObserving = false
    private var isPlaying = false
    @Published var isLoading = true
    
    // 播放器就绪回调
    var onReadyToPlay: (() -> Void)?
    var onLoadingChange: ((Bool) -> Void)?
    
    deinit {
        cleanup()
        print("VideoPlayerWrapper 被释放")
    }
    
    // 初始化并开始加载视频
    func loadVideo(url: URL) -> AVPlayer {
        print("[视频加载] 开始加载视频: \(url.lastPathComponent)")
        
        // 清理旧的资源
        cleanup()
        
        // 设置加载状态
        isLoading = true
        if let onLoadingChange = onLoadingChange {
            onLoadingChange(true)
        }
        
        // 使用高质量设置创建资源
        let options: [String: Any] = [
            AVURLAssetPreferPreciseDurationAndTimingKey: true
        ]
        
        asset = AVURLAsset(url: url, options: options)
        
        // 检查文件是否存在
        if !FileManager.default.fileExists(atPath: url.path) {
            print("[视频加载] 错误: 视频文件不存在 \(url.path)")
            isLoading = false
            onLoadingChange?(false)
            // 返回空播放器
            player = AVPlayer()
            return player!
        }
        
        // 预加载资源的关键属性
        let keys = ["playable", "tracks", "duration"]
        playerItem = AVPlayerItem(asset: asset!, automaticallyLoadedAssetKeys: keys)
        
        // 创建播放器
        player = AVPlayer(playerItem: playerItem)
        
        // 关键设置来避免悬崖和白屏
        player?.actionAtItemEnd = .none // 防止播放结束时黑屏
        player?.allowsExternalPlayback = false // 防止外部播放导致的白屏
        player?.automaticallyWaitsToMinimizeStalling = false // 不等待缓冲过多
        
        // 强制预加载
        playerItem?.preferredForwardBufferDuration = 5.0 // 预加载5秒视频
        
        // 添加观察者 - 使用更完善的选项
        playerItem?.addObserver(self, forKeyPath: "loadedTimeRanges", options: [.new, .initial], context: nil)
        playerItem?.addObserver(self, forKeyPath: "status", options: [.new, .initial], context: nil)
        isObserving = true
        print("[视频加载] 添加观察者成功")
        
        // 添加循环播放通知
        NotificationCenter.default.addObserver(self,
                                             selector: #selector(playerItemDidReachEnd),
                                             name: .AVPlayerItemDidPlayToEndTime,
                                             object: playerItem)
        
        // 早期触发预加载
        if let playerItem = playerItem {
            // 手动触发状态测试
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                if playerItem.status == .readyToPlay {
                    print("[视频加载] 状态已就绪，提前触发播放")
                    self.isLoading = false
                    self.onLoadingChange?(false)
                    self.play()
                }
            }
        }
        
        return player!
    }
    
    // 播放控制
    func play() {
        isPlaying = true
        player?.play()
    }
    
    func pause() {
        isPlaying = false
        player?.pause()
    }
    
    // 循环播放
    @objc private func playerItemDidReachEnd() {
        player?.seek(to: .zero)
        if isPlaying {
            player?.play()
        }
    }
    
    // 观察加载状态
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        guard let playerItem = object as? AVPlayerItem else { return }
        
        if keyPath == "loadedTimeRanges" {
            // 检查预加载进度
            let loadedRanges = playerItem.loadedTimeRanges
            if let timeRange = loadedRanges.first?.timeRangeValue {
                let startTime = CMTimeGetSeconds(timeRange.start)
                let duration = CMTimeGetSeconds(timeRange.duration)
                let totalLoaded = startTime + duration
                
                print("[视频加载] 已缓冲: \(totalLoaded) 秒, 状态: \(playerItem.status.rawValue)")
                
                // 只需要加载1秒就可以开始播放，提高响应速度
                if totalLoaded >= 1.0 && playerItem.status == .readyToPlay && isLoading {
                    DispatchQueue.main.async { [weak self] in
                        guard let self = self else { return }
                        self.isLoading = false
                        self.onLoadingChange?(false)
                        self.play() // 自动开始播放
                        print("[视频加载] 成功: 视频已预加载 \(totalLoaded) 秒，开始播放")
                    }
                }
            } else {
                print("[视频加载] 警告: 无法获取缓冲区信息")
            }
        }
        
        if keyPath == "status" {
            print("[视频加载] 状态改变: \(playerItem.status.rawValue)")
            
            if playerItem.status == .readyToPlay {
                // 视频已就绪
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    self.onReadyToPlay?()
                    print("[视频状态] 已就绪")
                    
                    // 如果已经缓冲了足够内容，且仍在加载中，则开始播放
                    // 降低门槛到仅需要缓冲几帧(0.5秒)
                    let loadedRanges = playerItem.loadedTimeRanges
                    if let timeRange = loadedRanges.first?.timeRangeValue,
                       CMTimeGetSeconds(timeRange.duration) >= 0.5 && self.isLoading {
                        self.isLoading = false
                        self.onLoadingChange?(false)
                        self.play()
                        print("[视频加载] 成功: 状态已就绪，开始播放")
                    }
                }
            } else if playerItem.status == .failed {
                let errorDesc = playerItem.error?.localizedDescription ?? "未知错误"
                print("[视频加载] 错误: 状态为失败，原因: \(errorDesc)")
                
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    self.isLoading = false
                    self.onLoadingChange?(false)
                }
            } else if playerItem.status == .unknown {
                print("[视频状态] 未知状态")
            }
        }
    }
    
    // 清理资源
    func cleanup() {
        print("[视频播放器] 清理资源")
        
        // 停止播放
        player?.pause()
        
        // 安全地移除观察者
        if isObserving {
            // 使用try-catch来避免移除观察者失败导致崩溃
            do {
                if let item = playerItem {
                    try? item.removeObserver(self, forKeyPath: "loadedTimeRanges")
                    try? item.removeObserver(self, forKeyPath: "status")
                }
            } catch {
                print("[视频播放器] 安全移除观察者失败: \(error)")
            }
            isObserving = false
        }
        
        // 移除通知
        NotificationCenter.default.removeObserver(self)
        
        // 释放资源
        player = nil
        playerItem = nil
        asset = nil
    }
}

// 应用内相册视图
struct AppGalleryView: View {
    @Binding var isPresented: Bool
    @State private var mediaItems: [MediaType] = []
    @State private var selectedItem: MediaType? = nil
    @State private var showFullScreen = false
    @State private var isVideoLoading = false
    @State private var showDeleteAlert = false
    @State private var itemToDelete: MediaType? = nil
    // 使用新的播放器包装器替代旧的播放器和观察器
    @StateObject private var videoWrapper = VideoPlayerWrapper()
    
    // 在视图出现时加载应用内存储的媒体项
    private func loadAppMedia() {
        mediaItems = AppMediaStorage.shared.loadAllAppMedia()
    }
    
    // 重写观察值方法，优化以处理视频预加载和状态更新
    
    // AppGalleryView不再需要重写观察值方法，因为我们使用VideoPlayerWrapper来处理这些逻辑
    
    var body: some View {
        // 自定义导航界面，不使用NavigationView
        ZStack {
            // 主内容
            VStack(spacing: 0) {
                // 自定义导航栏
                HStack {
                    Button(action: {
                        isPresented = false
                    }) {
                        Text("关闭")
                            .foregroundColor(.primary)
                            .font(.system(size: 17, weight: .regular))
                    }
                    .padding(.leading)
                    
                    Spacer()
                    
                    Text("我的相册")
                        .font(.headline)
                    
                    Spacer()
                    
                    Button(action: { 
                        loadAppMedia() 
                    }) {
                        Image(systemName: "arrow.clockwise")
                            .font(.headline)
                    }
                    .padding(.trailing)
                }
                .padding(.vertical, 12)
                .background(Color(.systemBackground))
                
                // 分隔线
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color(.systemGray5))
                
                // 内容区域
                if mediaItems.isEmpty {
                    VStack {
                        Image(systemName: "photo.on.rectangle")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                            .padding()
                        
                        Text("目前没有媒体文件")
                            .font(.headline)
                            .foregroundColor(.gray)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    ScrollView {
                        LazyVGrid(columns: [GridItem(.adaptive(minimum: 100))], spacing: 10) {
                            ForEach(Array(mediaItems.enumerated()), id: \.offset) { index, item in
                                if let thumbnail = item.thumbnail {
                                    ZStack(alignment: .bottomTrailing) {
                                        // 图片缩略图
                                        Image(uiImage: thumbnail)
                                            .resizable()
                                            .scaledToFit() // 保持原始比例
                                            .frame(height: 120) // 只限制高度，宽度自适应
                                            .cornerRadius(5)
                                        
                                        // 如果是视频，在缩略图内部显示时长
                                        if case .video = item, let duration = item.duration {
                                            // 时长文字，不要背景，并且提高位置使它完全在缩略图内
                                            Text(duration < 1.0 ? "短视频" : String(format: "%.0f秒", duration))
                                                .font(.system(size: 10))
                                                .fontWeight(.semibold)
                                                .foregroundColor(.white)
                                                .shadow(color: .black, radius: 1, x: 0, y: 0) // 添加文字阴影提高可见度
                                                .padding(.bottom, 30) // 增加底部距离使文字移动到更高位置
                                                .padding(.trailing, 10) // 与右边保持距离
                                        }
                                    }
                                    .onTapGesture {
                                        selectedItem = item
                                        showFullScreen = true
                                    }
                                    .contextMenu {
                                        Button(role: .destructive) {
                                            itemToDelete = item
                                            showDeleteAlert = true
                                        } label: {
                                            Label("删除", systemImage: "trash")
                                        }
                                        
                                        Button {
                                            // 分享功能可以在这里扩展
                                            if case .photo(let image) = item {
                                                // TODO: 实现照片分享
                                            } else if case .video(let url) = item {
                                                // TODO: 实现视频分享
                                            }
                                        } label: {
                                            Label("分享", systemImage: "square.and.arrow.up")
                                        }
                                    }
                                    .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                                        Button(role: .destructive) {
                                            itemToDelete = item
                                            showDeleteAlert = true
                                        } label: {
                                            Label("删除", systemImage: "trash")
                                        }
                                    }
                                }
                                }
                            }
                        }
                        .padding()
                    }
                }
                }
                
                // 悬浮关闭按钮 - 只在小或中屏幕上显示，助力关闭
                VStack {
                    HStack {
                        Button(action: {
                            isPresented = false
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 30))
                                .foregroundColor(.primary)
                                .padding(8)
                                .background(Color.white.opacity(0.7))
                                .clipShape(Circle())
                        }
                        .padding()
                        .padding(.top, 40) // 留出状态栏空间
                        Spacer()
                    }
                    Spacer()
                }
            }
        }
        .onAppear {
            loadAppMedia()
        }
        .sheet(isPresented: $showFullScreen, onDismiss: {
            // 手势关闭时回调
            videoWrapper.cleanup()
        }) {
            if let item = selectedItem {
                ZStack {
                    Color.black.edgesIgnoringSafeArea(.all)
                    
                    switch item {
                    case .photo(let image):
                        // 照片显示
                        ZStack {
                            // 黑色背景
                            Color.black.ignoresSafeArea()
                                
                            // 照片内容
                            Image(uiImage: image)
                                .resizable()
                                .scaledToFit()
                                .frame(maxWidth: .infinity, maxHeight: .infinity)
                                
                            // 控制层 - 确保始终可见
                            VStack {
                                // 顶部控制栏
                                HStack {
                                    // 左上角添加删除按钮
                                    Button(action: {
                                        itemToDelete = item
                                        showDeleteAlert = true
                                    }) {
                                        ZStack {
                                            Circle()
                                                .fill(Color.black.opacity(0.5))
                                                .frame(width: 44, height: 44)
                                            
                                            Image(systemName: "trash")
                                                .font(.system(size: 20))
                                                .foregroundColor(.white)
                                        }
                                    }
                                    .padding(.leading, 16)
                                    
                                    Spacer()
                                    
                                    // 右上角添加关闭按钮
                                    Button(action: {
                                        showFullScreen = false
                                    }) {
                                        ZStack {
                                            Circle()
                                                .fill(Color.black.opacity(0.5))
                                                .frame(width: 44, height: 44)
                                            
                                            Image(systemName: "xmark")
                                                .font(.system(size: 20))
                                                .foregroundColor(.white)
                                        }
                                    }
                                    .padding(.trailing, 16)
                                }
                                .padding(.top, 16)
                                .padding(.bottom, 8)
                            ZStack {
                                // 黑色背景层
                                Color.black.ignoresSafeArea()
                                    
                                // 加载状态切换
                                if videoWrapper.isLoading {
                                    // 加载指示器
                                    VStack {
                                            ProgressView()
                                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                                .scaleEffect(2.0)
                                            Text("视频加载中...")
                                                .foregroundColor(.white)
                                                .padding(.top, 20)
                                        }
                                    } else if let player = videoWrapper.player {
                                        // 视频播放器
                                        VideoPlayer(player: player)
                                            .transition(.opacity)
                                    }
                                    
                                // 控制层 - 确保始终可见
                                VStack {
                                    // 顶部控制栏
                                    HStack {
                                            // 左上角添加删除按钮
                                            Button(action: {
                                                itemToDelete = item
                                                showDeleteAlert = true
                                            }) {
                                                ZStack {
                                                    Circle()
                                                        .fill(Color.black.opacity(0.5))
                                                        .frame(width: 44, height: 44)
                                                    
                                                    Image(systemName: "trash")
                                                        .font(.system(size: 20))
                                                        .foregroundColor(.white)
                                                }
                                            }
                                            .padding(.leading, 16)
                                            
                                            Spacer()
                                            
                                            // 右上角添加关闭按钮
                                            Button(action: {
                                                showFullScreen = false
                                            }) {
                                                ZStack {
                                                    Circle()
                                                        .fill(Color.black.opacity(0.5))
                                                        .frame(width: 44, height: 44)
                                                    
                                                    Image(systemName: "xmark")
                                                        .font(.system(size: 20))
                                                        .foregroundColor(.white)
                                                }
                                            }
                                            .padding(.trailing, 16)
                                        }
                                        .padding(.top, 16)
                                        .padding(.bottom, 8)
                                        
                                        Spacer()
                                    }
                                    .edgesIgnoringSafeArea(.top)
                                }
                                .onAppear {
                                    // 在视图出现时加载视频
                                    print("[相册] 显示视频: \(url.lastPathComponent)")
                                    // 首先设置回调
                                    videoWrapper.onLoadingChange = { isLoading in
                                        self.isVideoLoading = isLoading
                                    }
                                    videoWrapper.onReadyToPlay = {
                                        print("[相册] 视频就绪回调触发")
                                    }
                                    
                                    // 然后加载视频
                                    _ = videoWrapper.loadVideo(url: url)
                                }
                                .onDisappear {
                                    // 清理资源
                                    videoWrapper.cleanup()
                                }
                            } else {
                                Text("视频文件不存在")
                                    .foregroundColor(.white)
                            }
                        }
                        
                        // 控制按钮
                        VStack {
                            HStack {
                                // 删除按钮
                                Button(action: {
                                    itemToDelete = item
                                    showDeleteAlert = true
                                }) {
                                    Image(systemName: "trash.circle.fill")
                                        .font(.system(size: 30))
                                        .foregroundColor(.white)
                                        .padding()
                                }
                                
                                Spacer()
                                
                                // 关闭按钮
                                Button(action: {
                                    showFullScreen = false
                                }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.system(size: 30))
                                        .foregroundColor(.white)
                                        .padding()
                                }
                            }
                            Spacer()
                        }
                    }
                }
            }
            .alert("确认删除", isPresented: $showDeleteAlert) {
                Button("取消", role: .cancel) {}
                Button("删除", role: .destructive) {
                    if let itemToDelete = itemToDelete {
                        // 在全屏模式下如果正在查看要删除的项目，退出全屏
                        if showFullScreen, selectedItem == itemToDelete {
                            showFullScreen = false
                        }
                        
                        // 使用应用内存储删除媒体
                        AppMediaStorage.shared.deleteMedia(itemToDelete) { success in
                            if success {
                                // 刷新媒体列表
                                loadAppMedia()
                                
                                // 更新选中状态
                                if selectedItem == itemToDelete {
                                    selectedItem = nil
                                }
                            }
                        }
                    }
                }
            } message: {
                VStack(spacing: 10) {
                    Text("确定要删除这个文件吗？")
                        .font(.headline)
                    Text("此操作无法撤销，文件将被永久删除")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}
