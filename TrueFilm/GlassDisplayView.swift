//
//  GlassDisplayView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/14.
//

import SwiftUI

struct GlassDisplayView: View {
    var displayText: String
    
    // Determine if text is shutter speed format (like 1/60)
    private var isShutterSpeed: Bool {
        return displayText.contains("/") && !displayText.contains(" ")
    }
    
    var body: some View {
        ZStack {
            // Glass display background 
            RoundedRectangle(cornerRadius: 25)
                .fill(Color.black)
                .frame(width: 160, height: 50)
                .overlay(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .background(
                    // 内部背景，添加金属质感的渐变
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)))  // #F0F0F0
                        .frame(width: 160, height: 50)
                )
                .shadow(color: Color.black.opacity(0.2), radius: 3, x: 0, y: 1)

            // Display text with white color
            Text(displayText)
                .font(.system(size: isShutterSpeed ? 24 : 20, weight: .medium, design: .monospaced))
                .foregroundColor(Color.white)
                .tracking(isShutterSpeed ? 2.0 : 1.5)
                .frame(width: 150)
                .multilineTextAlignment(.center)
        }
    }
}

#Preview {
    ZStack {
        // 使用渐变背景更好地展示凹陷玻璃效果
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.12, green: 0.12, blue: 0.18),
                Color(red: 0.08, green: 0.08, blue: 0.12)
            ]),
            startPoint: .top,
            endPoint: .bottom
        )
        .edgesIgnoringSafeArea(.all)

        // 添加一些模糊的光点来增强深度感
        ZStack {
            Circle()
                .fill(Color.white.opacity(0.15))
                .frame(width: 120, height: 120)
                .blur(radius: 35)
                .offset(x: -80, y: -120)

            Circle()
                .fill(Color.blue.opacity(0.08))
                .frame(width: 150, height: 150)
                .blur(radius: 45)
                .offset(x: 90, y: 120)

            // 添加一个微妙的光源模拟环境光
            Circle()
                .fill(Color.white.opacity(0.1))
                .frame(width: 80, height: 80)
                .blur(radius: 25)
                .offset(x: 60, y: -80)
        }

        // 添加一个模拟的设备面板来增强凹陷效果的对比
        VStack {
            // 模拟设备面板
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(red: 0.15, green: 0.15, blue: 0.2))
                .frame(width: 280, height: 180)
                .shadow(color: Color.black.opacity(0.5), radius: 10, x: 0, y: 5)
                .overlay(
                    // 展示两个不同的凹陷玻璃显示效果
                    VStack(spacing: 30) {
                        GlassDisplayView(displayText: "Mist: +1")
                        GlassDisplayView(displayText: "12 photos")
                    }
                    .padding(.vertical, 20)
                )
        }
    }
}