//
//  GlassDisplayViewModel.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

import Foundation
import SwiftUI
import Combine

class GlassDisplayViewModel: ObservableObject {
    // 显示内容
    @Published var displayText: String = ""

    // 照片计数
    @Published var photoCount: Int = 0
    @Published var showPhotoCount: Bool = true

    // 控制类型
    @Published var selectedControl: ControlType = .mist

    // 滤镜参数
    @Published var blackMistIntensity: Float = 0.5
    @Published var blackMistThreshold: Float = 0.2
    @Published var brightness: Float = -0.17
    @Published var filmIntensity: Float = 0.75

    // 曝光补偿值
    @Published var exposureCompValue: Float = -0.17

    // 快门速度
    @Published var shutterSpeed: String = "1/60"
    @Published var showShutterSpeed: Bool = true

    // 录像相关
    @Published var isRecording: Bool = false
    @Published var recordingTime: TimeInterval = 0
    private var recordingTimer: Timer?
    @Published var showRecordingTime: Bool = false

    // 用于强制更新显示
    @Published var displayUpdateTrigger: UUID = UUID()

    // 初始化
    init() {
        loadPhotoCount()
        // 启动时监听语言变化通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleLanguageChange),
            name: .languageDidChange,
            object: nil
        )

        // 添加重置照片计数的通知监听
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handlePhotoCountReset),
            name: .photoCountReset,
            object: nil
        )

        // 添加胶片滤镜强度更新的通知监听
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleFilmIntensityUpdate),
            name: NSNotification.Name("UpdateFilmIntensity"),
            object: nil
        )
    }

    // 析构函数
    deinit {
        recordingTimer?.invalidate()
        NotificationCenter.default.removeObserver(self)
    }

    @objc private func handleLanguageChange() {
        print("GlassDisplayViewModel: 检测到语言变更，更新显示文本")
        // 重新生成显示文本
        updateDisplayText()
    }

    // 处理照片计数重置
    @objc private func handlePhotoCountReset() {
        print("GlassDisplayViewModel: 重置照片计数")
        photoCount = 0
        UserDefaults.standard.set(0, forKey: "PhotoCount")
        UserDefaults.standard.set(0, forKey: "totalPhotosProcessed")
        UserDefaults.standard.synchronize()

        // 强制显示照片计数
        showPhotoCount = true
        updateDisplayText()
    }

    // 处理胶片滤镜强度更新
    @objc private func handleFilmIntensityUpdate(_ notification: Notification) {
        if let userInfo = notification.userInfo,
           let intensity = userInfo["intensity"] as? Float {
            print("GlassDisplayViewModel: 更新胶片滤镜强度为 \(intensity)")
            filmIntensity = intensity
            // 如果当前选择的是胶片控制，更新显示
            if selectedControl == .film {
                updateDisplayText()
            }
        }
    }

    // 加载照片计数
    func loadPhotoCount() {
        photoCount = UserDefaults.standard.integer(forKey: "PhotoCount")
    }

    // 更新照片计数
    func incrementPhotoCount() {
        photoCount += 1

        // 保存照片计数到 UserDefaults
        UserDefaults.standard.set(photoCount, forKey: "PhotoCount")
        UserDefaults.standard.set(photoCount, forKey: "totalPhotosProcessed")
        UserDefaults.standard.synchronize()

        showPhotoCount = true
        updateDisplayText()

        // 3秒后自动隐藏照片计数
        autoHidePhotoCount()
    }

    // 开始录像计时
    func startRecording() {
        isRecording = true
        recordingTime = 0
        showRecordingTime = true
        showPhotoCount = false
        showShutterSpeed = false

        recordingTimer?.invalidate()
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            self.recordingTime += 0.1
            self.updateDisplayText()
        }
    }

    // 停止录像计时
    func stopRecording() {
        isRecording = false
        recordingTimer?.invalidate()
        recordingTimer = nil

        // 延迟隐藏录像时间，改为显示照片计数
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.showRecordingTime = false
            self.showPhotoCount = true
            self.updateDisplayText()
        }
    }

    // 更新快门速度
    func updateShutterSpeed(speed: String) {
        shutterSpeed = speed
        if !showRecordingTime && !showPhotoCount && showShutterSpeed {
            updateDisplayText()
        }
    }

    // 获取当前控制的显示值
    func getDisplayValueForCurrentControl() -> String {
        // 录像状态下优先显示录像时长
        if showRecordingTime {
            let minutes = Int(recordingTime) / 60
            let seconds = Int(recordingTime) % 60
            let tenths = Int(recordingTime * 10) % 10
            return String(format: "%02d:%02d.%d", minutes, seconds, tenths)
        }

        // 默认显示快门速度
        if showShutterSpeed && selectedControl == .none {
            return shutterSpeed
        }

        switch selectedControl {
        case .mist:
            // 只显示黑柔强度值，保留两位小数
            return String(format: "%.2f", blackMistIntensity)

        case .threshold:
            // 只显示高光阈值，保留两位小数
            return String(format: "%.2f", blackMistThreshold)

        case .brightness:
            // 只显示亮度值，保留两位小数
            if brightness > 0 {
                return "+\(String(format: "%.2f", brightness))"
            } else {
                return String(format: "%.2f", brightness)
            }

        case .film:
            // 显示胶片滤镜强度，转换为百分比
            return "\(Int(filmIntensity * 100))%"

        case .none:
            // 显示快门速度
            return shutterSpeed
        }
    }

    // 更新显示内容
    func updateDisplayText() {
        if showRecordingTime {
            let minutes = Int(recordingTime) / 60
            let seconds = Int(recordingTime) % 60
            let tenths = Int(recordingTime * 10) % 10
            displayText = String(format: "%02d:%02d.%d", minutes, seconds, tenths)
        } else if showPhotoCount {
            displayText = "\(photoCount) \(LocalizedString("PHOTOS"))"
        } else {
            displayText = getDisplayValueForCurrentControl()
        }
        // 强制更新显示
        displayUpdateTrigger = UUID()
    }

    // 自动隐藏照片计数，切换到显示对应的参数
    func autoHidePhotoCount(after seconds: Double = 3.0) {
        DispatchQueue.main.asyncAfter(deadline: .now() + seconds) {
            self.showPhotoCount = false
            // 注意：不再强制切换到 .none 模式，保持当前选择的控制类型
            self.updateDisplayText()
        }
    }

    // 更新参数值
    func updateParameters(mistIntensity: Float? = nil, threshold: Float? = nil, brightnessValue: Float? = nil, exposureComp: Float? = nil, filmIntensity: Float? = nil) {
        if let mistIntensity = mistIntensity {
            blackMistIntensity = mistIntensity
        }

        if let threshold = threshold {
            blackMistThreshold = threshold
        }

        if let brightnessValue = brightnessValue {
            brightness = brightnessValue
        }

        if let exposureComp = exposureComp {
            exposureCompValue = exposureComp
        }

        if let filmIntensity = filmIntensity {
            self.filmIntensity = filmIntensity
        }

        // 参数改变后，确保显示参数而不是照片计数
        showPhotoCount = false
        updateDisplayText()
    }
}
