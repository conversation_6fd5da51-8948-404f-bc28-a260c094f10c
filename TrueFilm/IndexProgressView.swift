import SwiftUI

// 索引构建进度展示视图
struct IndexProgressView: View {
    let progress: Double
    let message: String
    @State private var pulseScale: CGFloat = 1.0
    @State private var rotation: Double = 0
    
    var body: some View {
        VStack(spacing: 24) {
            // 动画图标区域
            ZStack {
                // 背景圆环
                Circle()
                    .stroke(Color.gray.opacity(0.2), lineWidth: 6)
                    .frame(width: 100, height: 100)
                
                // 进度圆环
                Circle()
                    .trim(from: 0, to: progress)
                    .stroke(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        style: StrokeStyle(lineWidth: 6, lineCap: .round)
                    )
                    .frame(width: 100, height: 100)
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut(duration: 0.5), value: progress)
                
                // 中心图标
                ZStack {
                    // 背景圆
                    Circle()
                        .fill(Color(.systemBackground))
                        .frame(width: 60, height: 60)
                        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                    
                    // 主图标
                    Image(systemName: "photo.stack.fill")
                        .font(.system(size: 24, weight: .medium))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [.blue, .purple],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(pulseScale)
                        .rotationEffect(.degrees(rotation))
                }
            }
            .onAppear {
                // 脉冲动画
                withAnimation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true)) {
                    pulseScale = 1.15
                }
                
                // 缓慢旋转动画
                withAnimation(.linear(duration: 8.0).repeatForever(autoreverses: false)) {
                    rotation = 360
                }
            }
            .onDisappear {
                pulseScale = 1.0
                rotation = 0
            }
            
            // 文字信息区域
            VStack(spacing: 12) {
                Text(NSLocalizedString("index_building_title", comment: ""))
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text(message)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                    .fixedSize(horizontal: false, vertical: true)
                
                // 线性进度条
                VStack(spacing: 8) {
                    ProgressView(value: progress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                        .frame(maxWidth: 240)
                        .scaleEffect(y: 1.5) // 让进度条稍微粗一些
                    
                    HStack {
                        Text("\(Int(progress * 100))%")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.blue)
                        
                        Spacer()
                        
                        if progress < 1.0 {
                            HStack(spacing: 2) {
                                ForEach(0..<3) { index in
                                    Circle()
                                        .fill(Color.blue)
                                        .frame(width: 4, height: 4)
                                        .scaleEffect(pulseScale)
                                        .animation(
                                            .easeInOut(duration: 0.6)
                                                .repeatForever(autoreverses: true)
                                                .delay(Double(index) * 0.2),
                                            value: pulseScale
                                        )
                                }
                            }
                        }
                    }
                }
            }
            
            // 底部提示
            VStack(spacing: 8) {
                Text(NSLocalizedString("index_first_time_notice", comment: ""))
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                
                Text(NSLocalizedString("index_completion_notice", comment: ""))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
            .multilineTextAlignment(.center)
        }
        .padding(.horizontal, 32)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

// 预览
#Preview {
    IndexProgressView(
        progress: 0.65,
        message: "正在处理图片 45/68"
    )
} 