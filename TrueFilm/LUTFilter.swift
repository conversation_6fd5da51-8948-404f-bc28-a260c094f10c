//
//  LUTFilter.swift
//  TrueFilm
//
//  Created on 2025/5/11.
//

import Foundation
import Metal
import MetalKit
import CoreImage
import UIKit

class LUTFilter {
    // 支持的LUT尺寸
    enum LUTSize {
        case small  // 16x16x16 (64x64)
        case medium // 32x32x32 (512x512)
        case large  // 64x64x64 (512x512x8)
        
        var dimension: Int {
            switch self {
            case .small: return 16
            case .medium: return 32
            case .large: return 64
            }
        }
        
        var textureSize: (width: Int, height: Int) {
            switch self {
            case .small: return (64, 64)
            case .medium: return (512, 512)
            case .large: return (512, 512) // 需要多个纹理或特殊处理
            }
        }
    }
    
    // LUT纹理
    private(set) var lutTexture: MTLTexture?
    private let device: MTLDevice
    private let lutSize: LUTSize
    private var lutName: String = ""
    
    // 初始化
    init(device: MTLDevice, lutSize: LUTSize = .medium) {
        self.device = device
        self.lutSize = lutSize
    }
    
    // 加载LUT图像
    func loadLUT(named name: String) -> Bool {
        guard let lutImage = UIImage(named: name) else {
            print("Failed to load LUT image: \(name)")
            return false
        }
        
        lutName = name
        return createLUTTexture(from: lutImage)
    }
    
    // 从UIImage创建Metal纹理
    private func createLUTTexture(from image: UIImage) -> Bool {
        guard let cgImage = image.cgImage else {
            print("Failed to get CGImage from UIImage")
            return false
        }
        
        let textureLoader = MTKTextureLoader(device: device)
        let options: [MTKTextureLoader.Option: Any] = [
            .textureUsage: MTLTextureUsage.shaderRead.rawValue,
            .generateMipmaps: false
        ]
        
        do {
            lutTexture = try textureLoader.newTexture(cgImage: cgImage, options: options)
            return true
        } catch {
            print("Failed to create LUT texture: \(error)")
            return false
        }
    }
    
    // 获取当前LUT名称
    func getCurrentLUTName() -> String {
        return lutName
    }
    
    // 检查LUT是否已加载
    var isLUTLoaded: Bool {
        return lutTexture != nil
    }
    
    // 获取LUT尺寸
    func getLUTSize() -> LUTSize {
        return lutSize
    }
}
