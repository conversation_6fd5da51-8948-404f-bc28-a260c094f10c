import Foundation
import UIKit
import Accelerate

extension UIColor {
    var floatComponents: [Float] {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        self.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        return [Float(red), Float(green), Float(blue), Float(alpha)]
    }
}


class LUTProcessor {
    var activeLUT: LUT?
    var activeLUTName: String?
    var blendStrength: Float = 0.75
    
    
    func createLUTFilter() -> CIFilter? {
            guard let lut = activeLUT else { return nil }
            
            guard let lutFilter = CIFilter(name: "CIColorCubeWithColorSpace") else {
                print("Failed to create CIFilter")
                return nil
            }
            
            lutFilter.setValue(lut.dimension, forKey: "inputCubeDimension")
            lutFilter.setValue(lut.data, forKey: "inputCubeData")
            
            if #available(iOS 10.0, *) {
                if let sRGBColorSpace = CGColorSpace(name: CGColorSpace.sRGB) {
                    lutFilter.setValue(sRGBColorSpace, forKey: "inputColorSpace")
                }
            }
            
            return lutFilter
        }
    
    func loadPNG(at url: URL, name: String) {
        if let lutImage = UIImage(contentsOfFile: url.path) {
            let generator = CICubeColorGenerator(image: lutImage)
            if let filter = generator?.filter() {
                let dimension = generator!.dimension
                let data = filter.value(forKey: "inputCubeData") as! Data
                activeLUT = LUT(url: url, name: name, dimension: dimension, data: data)
                activeLUTName = name
                print("Successfully loaded LUT: \(name)")
            } else {
                print("Error: Unable to create CIFilter.")
            }
        } else {
            print("Error: Unable to load LUT image.")
        }
    }
    
    func process(image: UIImage) -> UIImage {
        print("Processing input image with LUT: \(String(describing: activeLUT?.name))")
            
        guard let lut = activeLUT, var ciImage = CIImage(image: image) else {
            return image
        }
        
        // 压缩亮度到[0,0.95]范围
        if let compressFilter = CIFilter(name: "CIColorMatrix") {
            compressFilter.setValue(ciImage, forKey: kCIInputImageKey)
            compressFilter.setValue(CIVector(x: 0.95, y: 0, z: 0, w: 0), forKey: "inputRVector")
            compressFilter.setValue(CIVector(x: 0, y: 0.95, z: 0, w: 0), forKey: "inputGVector")
            compressFilter.setValue(CIVector(x: 0, y: 0, z: 0.95, w: 0), forKey: "inputBVector")
            compressFilter.setValue(CIVector(x: 0, y: 0, z: 0, w: 1), forKey: "inputAVector")
            ciImage = compressFilter.outputImage!
        }
        
        guard let lutFilter = CIFilter(name: "CIColorCubeWithColorSpace") else {
            return image
        }
        
        lutFilter.setValue(lut.dimension, forKey: "inputCubeDimension")
        lutFilter.setValue(lut.data, forKey: "inputCubeData")
        lutFilter.setValue(ciImage, forKey: kCIInputImageKey)
        
        // 设置颜色空间为 sRGB
        if #available(iOS 10.0, *) {
            if let sRGBColorSpace = CGColorSpace(name: CGColorSpace.sRGB) {
                lutFilter.setValue(sRGBColorSpace, forKey: "inputColorSpace")
            } else {
                print("Error: Unable to get sRGB color space.")
                return image
            }
        } else {
            print("Error: sRGB color space is only available on iOS 10.0 and later.")
            return image
        }
        
        // 添加调试输出
        print("Filter input parameters:")
        print("  inputCubeDimension: \(String(describing: lutFilter.value(forKey: "inputCubeDimension")))")
        print("  inputCubeData.length: \(String(describing: (lutFilter.value(forKey: "inputCubeData") as? Data)?.count))")
        print("  inputImage.extent: \(ciImage.extent)")
        print("  inputColorSpace: \(String(describing: lutFilter.value(forKey: "inputColorSpace")))")
        
        guard var lutOutputCIImage = lutFilter.outputImage else {
            return image
        }
        
        // 扩展亮度到[0,1]范围
        if let expandFilter = CIFilter(name: "CIColorMatrix") {
            expandFilter.setValue(lutOutputCIImage, forKey: kCIInputImageKey)
            expandFilter.setValue(CIVector(x: 1.0/0.95, y: 0, z: 0, w: 0), forKey: "inputRVector")
            expandFilter.setValue(CIVector(x: 0, y: 1.0/0.95, z: 0, w: 0), forKey: "inputGVector")
            expandFilter.setValue(CIVector(x: 0, y: 0, z: 1.0/0.95, w: 0), forKey: "inputBVector")
            expandFilter.setValue(CIVector(x: 0, y: 0, z: 0, w: 1), forKey: "inputAVector")
            lutOutputCIImage = expandFilter.outputImage!
        }
        
        if let compositingFilter = CIFilter(name: "CIDissolveTransition") {
            compositingFilter.setValue(ciImage, forKey: kCIInputImageKey)
            compositingFilter.setValue(lutOutputCIImage, forKey: kCIInputTargetImageKey)
            compositingFilter.setValue(blendStrength, forKey: kCIInputTimeKey)
            if let compositedImage = compositingFilter.outputImage {
                if let finalCgImage = CIContext().createCGImage(compositedImage, from: ciImage.extent) {
                    let outputImage = UIImage(cgImage: finalCgImage)
                    print("Successfully processed output image.")
                    return outputImage
                }
            }
        }
        
        return image
    }

    
    
    class LUT {
        var url: URL
        var name: String
        var dimension: Int
        var data: Data
        
        init(url: URL, name: String, dimension: Int, data: Data) {
            self.url = url
            self.name = name
            self.dimension = dimension
            self.data = data
        }
    }
}
