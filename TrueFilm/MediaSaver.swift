//
//  MediaSaver.swift
//  TrueFilm
//
//  Created on 2025/5/7.
//

import UIKit
import AVFoundation
import Photos
import CoreLocation

/// 用于保存照片和视频的工具类
class MediaSaver: NSObject {
    
    static let shared = MediaSaver()
    
    private override init() {
        super.init()
    }
    
    // MARK: - 视频保存
    
    /// 保存视频到相册
    /// - Parameter fileURL: 视频文件URL
    /// - Parameter completion: 完成回调，返回是否成功、错误和资源标识符
    func saveVideoToPhotoLibrary(fileURL: URL, completion: ((Bool, Error?, String?) -> Void)? = nil) {
        var placeholder: PHObjectPlaceholder?
        
        PHPhotoLibrary.shared().performChanges {
            // 创建视频资源请求
            let request = PHAssetChangeRequest.creationRequestForAssetFromVideo(atFileURL: fileURL)
            // 保存占位符引用
            placeholder = request?.placeholderForCreatedAsset
            
        } completionHandler: { success, error in
            // 获取标识符
            let assetId = placeholder?.localIdentifier
            
            if success {
                print("视频已保存到相册，标识符: \(assetId ?? "未知")")
            } else if let error = error {
                print("保存视频到相册失败: \(error.localizedDescription)")
            }
            
            // 删除临时文件
            try? FileManager.default.removeItem(at: fileURL)
            
            // 调用完成回调
            completion?(success, error, assetId)
        }
    }
    
    // MARK: - 照片保存
    
    /// 保存高质量照片到相册，包含EXIF数据
    /// - Parameters:
    ///   - image: 要保存的图像
    ///   - location: 位置信息（可选）
    ///   - lensType: 镜头类型
    ///   - quality: 照片质量
    ///   - iso: ISO值
    ///   - aperture: 光圈值
    ///   - shutterSpeed: 快门速度
    ///   - exposureCompensation: 曝光补偿
    ///   - onAssetSaved: 照片保存到系统相册后的回调，返回标识符
    ///   - completion: 完成回调，返回是否成功和文件URL
    func saveHighQualityPhoto(image: UIImage,
                          location: CLLocation? = nil,
                          lensType: CameraLensType,
                          quality: PhotoQuality = .maximum,
                          iso: Double = 100,
                          aperture: Double = 2.8,
                          shutterSpeed: Double = 1/60,
                          exposureCompensation: Double = -0.17,
                          onAssetSaved: ((String?) -> Void)? = nil,
                          completion: @escaping (Bool, URL?) -> Void) {
    
        // 使用PhotoUtility保存照片，它处理所有的EXIF数据和高质量保存
        PhotoUtility.shared.saveHighQualityPhoto(
            image: image,
            location: location,
            lensType: lensType,
            quality: quality,
            iso: iso,
            aperture: aperture,
            shutterSpeed: shutterSpeed,
            exposureCompensation: exposureCompensation,
            onAssetSaved: onAssetSaved,
            completion: completion
        )
    }
}
