//
//  MetalView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

import SwiftUI
import MetalKit
import AVFoundation

// UIViewRepresentable wrapper for MTKView
struct MetalView: UIViewRepresentable {
    var texture: MTLTexture?
    var device: MTLDevice

    func makeUIView(context: Context) -> MTKView {
        let mtkView = MTKView()
        mtkView.device = device
        mtkView.delegate = context.coordinator
        mtkView.framebufferOnly = false
        mtkView.colorPixelFormat = .bgra8Unorm
        mtkView.clearColor = MTLClearColor(red: 0, green: 0, blue: 0, alpha: 0) // 使用透明背景，便于叠加
        mtkView.isPaused = false
        mtkView.enableSetNeedsDisplay = true
        mtkView.autoResizeDrawable = true // 自动调整绘制大小
        mtkView.contentScaleFactor = UIScreen.main.scale // 使用屏幕的缩放因子

        // 设置视图的内容模式
        mtkView.layer.contentsGravity = .resizeAspect // 保持纵横比例

        return mtkView
    }

    func updateUIView(_ uiView: MTKView, context: Context) {
        // Update the texture and force redraw
        context.coordinator.texture = texture

        // Force redraw
        uiView.setNeedsDisplay()

        // Print debug info
        if let texture = texture {
            print("MetalView updated with texture: \(texture.width)x\(texture.height)")
        } else {
            print("MetalView updated with nil texture")
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MTKViewDelegate {
        var parent: MetalView
        var texture: MTLTexture?
        var commandQueue: MTLCommandQueue?
        var pipelineState: MTLRenderPipelineState?
        var vertexBuffer: MTLBuffer?
        var frameCount: Int = 0 // 用于调试信息

        init(_ parent: MetalView) {
            self.parent = parent
            super.init()

            commandQueue = parent.device.makeCommandQueue()
            createRenderPipeline()
            createVertexBuffer()
        }

        func createRenderPipeline() {
            guard let library = parent.device.makeDefaultLibrary() else { return }

            // Get vertex and fragment functions
            guard let vertexFunction = library.makeFunction(name: "vertexShader"),
                  let fragmentFunction = library.makeFunction(name: "fragmentShader") else {
                print("Could not find Metal shader functions")
                return
            }

            // Create render pipeline descriptor
            let pipelineDescriptor = MTLRenderPipelineDescriptor()
            pipelineDescriptor.vertexFunction = vertexFunction
            pipelineDescriptor.fragmentFunction = fragmentFunction
            pipelineDescriptor.colorAttachments[0].pixelFormat = .bgra8Unorm

            // Create pipeline state
            do {
                pipelineState = try parent.device.makeRenderPipelineState(descriptor: pipelineDescriptor)
            } catch {
                print("Failed to create render pipeline state: \(error)")
            }
        }

        func createVertexBuffer() {
            // Define quad vertices (two triangles) with normalized coordinates
            // We'll adjust these in the draw method to maintain aspect ratio
            let quadVertices: [Float] = [
                -1.0, -1.0, 0.0, 1.0,  // bottom left
                 1.0, -1.0, 1.0, 1.0,  // bottom right
                -1.0,  1.0, 0.0, 0.0,  // top left
                 1.0,  1.0, 1.0, 0.0,  // top right
            ]

            // Create vertex buffer
            vertexBuffer = parent.device.makeBuffer(bytes: quadVertices,
                                                  length: quadVertices.count * MemoryLayout<Float>.size,
                                                  options: .storageModeShared)
        }

        // Calculate vertices that maintain the texture's aspect ratio
        func aspectFitVertices(for texture: MTLTexture, in viewSize: CGSize) -> [Float] {
            let textureAspect = Float(texture.width) / Float(texture.height)
            let viewAspect = Float(viewSize.width) / Float(viewSize.height)

            var scaleX: Float = 1.0
            var scaleY: Float = 1.0

            if textureAspect > viewAspect {
                // Texture is wider than view
                scaleY = viewAspect / textureAspect
            } else {
                // Texture is taller than view
                scaleX = textureAspect / viewAspect
            }

            // Create vertices that maintain aspect ratio
            return [
                -scaleX, -scaleY, 0.0, 1.0,  // bottom left
                 scaleX, -scaleY, 1.0, 1.0,  // bottom right
                -scaleX,  scaleY, 0.0, 0.0,  // top left
                 scaleX,  scaleY, 1.0, 0.0,  // top right
            ]
        }

        func mtkView(_ view: MTKView, drawableSizeWillChange size: CGSize) {
            // Handle view size changes if needed
        }

        func draw(in view: MTKView) {
            // Check if we have a texture to draw
            guard let texture = texture else {
                // No texture available, skip drawing
                return
            }

            // Print debug info occasionally
            self.frameCount += 1
            if self.frameCount % 60 == 0 {
                print("Drawing texture: \(texture.width)x\(texture.height) in view: \(view.drawableSize.width)x\(view.drawableSize.height)")
            }

            // Prepare for drawing
            guard let commandQueue = commandQueue,
                  let pipelineState = pipelineState,
                  let drawable = view.currentDrawable,
                  let commandBuffer = commandQueue.makeCommandBuffer(),
                  let renderPassDescriptor = view.currentRenderPassDescriptor,
                  let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else {
                print("Failed to create Metal resources for drawing")
                return
            }

            // Calculate vertices that maintain aspect ratio
            let aspectCorrectVertices = aspectFitVertices(for: texture, in: view.drawableSize)

            // Create a new vertex buffer with the aspect-corrected vertices
            let aspectVertexBuffer = parent.device.makeBuffer(bytes: aspectCorrectVertices,
                                                            length: aspectCorrectVertices.count * MemoryLayout<Float>.size,
                                                            options: .storageModeShared)

            // Set render pipeline state
            renderEncoder.setRenderPipelineState(pipelineState)

            // Set vertex buffer
            if let aspectVertexBuffer = aspectVertexBuffer {
                renderEncoder.setVertexBuffer(aspectVertexBuffer, offset: 0, index: 0)
            } else {
                // Fallback to original vertex buffer if aspect correction fails
                guard let vertexBuffer = vertexBuffer else { return }
                renderEncoder.setVertexBuffer(vertexBuffer, offset: 0, index: 0)
            }

            // Set fragment texture
            renderEncoder.setFragmentTexture(texture, index: 0)

            // Draw quad
            renderEncoder.drawPrimitives(type: .triangleStrip, vertexStart: 0, vertexCount: 4)

            // End encoding
            renderEncoder.endEncoding()

            // Present drawable
            commandBuffer.present(drawable)

            // Commit command buffer
            commandBuffer.commit()
        }
    }
}
