//
//  Models.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/15.
//

import Foundation
import AVFoundation
import SwiftUI
import Darwin  // For utsname struct

// 闪光灯模式枚举
public enum FlashMode {
    case off
    case on
    case auto

    // 获取下一个模式
    var nextMode: FlashMode {
        switch self {
        case .off: return .on
        case .on: return .auto
        case .auto: return .off
        }
    }

    // 获取图标名称
    var iconName: String {
        switch self {
        case .off: return "bolt.slash"
        case .on: return "bolt"
        case .auto: return "bolt.badge.a"
        }
    }

    // 转换为 AVCaptureDevice.FlashMode
    var avFlashMode: AVCaptureDevice.FlashMode {
        switch self {
        case .off: return .off
        case .on: return .on
        case .auto: return .auto
        }
    }

    // UI 相关属性
    var systemName: String {
        switch self {
        case .off: return "bolt.slash.fill"
        case .on: return "bolt.fill"  // 使用填充的闪电图标表示强制开启
        case .auto: return "bolt.badge.a.fill"
        }
    }

    var color: Color {
        switch self {
        case .off: return .gray
        case .on: return .orange.opacity(1.0)  // Full opacity for better visibility
        case .auto: return .orange
        }
    }
}

// 视频录制模式枚举
public enum RecordingMode {
    case off
    case recording

    // 切换状态
    var toggle: RecordingMode {
        switch self {
        case .off: return .recording
        case .recording: return .off
        }
    }

    // UI 相关属性
    var systemName: String {
        switch self {
        case .off: return "record.circle"
        case .recording: return "stop.fill"
        }
    }

    var color: Color {
        switch self {
        case .off: return Color(hex: "#A12A38")
        case .recording: return Color(hex: "#A12A38").opacity(1.0)
        }
    }
}

// 控制类型枚举
public enum ControlType {
    case mist      // 黑柔强度
    case threshold // 高光阈值
    case brightness // 亮度调整
    case film      // 胶片滤镜强度
    case none      // 没有按钮被选中的状态
}

// 相机镜头类型
public enum CameraLensType: Int, CaseIterable {
    case front = -1     // 前置摄像头 (-1x)
    case ultraWide = 0  // 超广角 (0.5x)
    case wide = 1       // 广角 (1x)
    case telephoto = 2  // 长焦 (2x)
    case telephoto3 = 3 // 长焦 (3x)
    case telephoto5 = 4 // 超长焦 (5x)

    // 获取对应的AVCaptureDevice.DeviceType
    var deviceType: AVCaptureDevice.DeviceType {
        switch self {
        case .front:
            return .builtInWideAngleCamera
        case .ultraWide:
            return .builtInUltraWideCamera
        case .wide:
            return .builtInWideAngleCamera
        case .telephoto:
            // 对于2x镜头，需要根据是否为数码变焦来决定使用哪个物理镜头
            if self.isDigitalZoom {
                // 数码变焦应该使用广角镜头
                return .builtInWideAngleCamera
            } else {
                // 光学镜头使用长焦镜头
                return .builtInTelephotoCamera
            }
        case .telephoto3, .telephoto5:
            return .builtInTelephotoCamera
        }
    }

    // 获取镜头的显示名称（使用倍数表示）
    var displayName: String {
        switch self {
        case .front: return "前置".localized
        case .ultraWide: return "0.5×".localized
        case .wide: return "1×".localized
        case .telephoto: return "2×".localized
        case .telephoto3: return "3×".localized
        case .telephoto5: return "5×".localized
        }
    }

    // 获取镜头的实际焦距 (mm)
    var focalLength: Double {
        switch self {
        case .front: return -26.0    // 前置摄像头焦距（负值表示前置）
        case .ultraWide: return 13.0  // iPhone 超广角镜头的实际焦距
        case .wide: return 26.0      // iPhone 广角镜头的实际焦距
        case .telephoto:
            // 根据设备型号返回不同的焦距
            var systemInfo = utsname()
            uname(&systemInfo)
            let deviceName = Mirror(reflecting: systemInfo.machine).children.reduce("") { identifier, element in
                guard let value = element.value as? Int8, value != 0 else { return identifier }
                return identifier + String(UnicodeScalar(UInt8(value)))
            }.lowercased()

            if deviceName.contains("iphone13,3") { // iPhone 12 Pro Max
                return 65.0  // iPhone 12 Pro Max: 2.5x光学镜头的实际焦距
            } else if self.isDigitalZoom {
                // 数码变焦使用广角镜头的焦距
                return 26.0  // 广角镜头焦距，但会有2x数码裁切
            } else {
                return 52.0  // 其他机型的2x光学镜头焦距
            }
        case .telephoto3: return 77.0 // iPhone 长焦镜头的实际焦距 (3x)
        case .telephoto5: return 130.0 // iPhone 超长焦镜头的实际焦距 (5x)
        }
    }

    // 获取镜头的实际描述
    var description: String {
        switch self {
        case .front: return "前置摄像头"
        case .ultraWide: return "超广角"
        case .wide: return "广角"
        case .telephoto:
            // 根据设备型号返回不同的描述
            var systemInfo = utsname()
            uname(&systemInfo)
            let deviceName = Mirror(reflecting: systemInfo.machine).children.reduce("") { identifier, element in
                guard let value = element.value as? Int8, value != 0 else { return identifier }
                return identifier + String(UnicodeScalar(UInt8(value)))
            }.lowercased()

            if deviceName.contains("iphone13,3") { // iPhone 12 Pro Max
                return "2.5倍长焦"
            } else if deviceName.contains("iphone17") || // iPhone 16 series
                     deviceName.contains("iphone16") || // iPhone 15 series
                     deviceName.contains("iphone15") {  // iPhone 14 series
                return "2倍数码变焦"
            } else {
                return "2倍长焦"
            }
        case .telephoto3: return "3倍长焦"
        case .telephoto5: return "5倍长焦"
        }
    }

    // 获取镜头的变焦倍数
    var zoomFactor: Double {
        switch self {
        case .front: return 1.0      // 前置摄像头使用1倍变焦
        case .ultraWide: return 0.5   // 超广角使用0.5倍变焦
        case .wide: return 1.0       // 广角使用1倍变焦
        case .telephoto:
            // 根据设备型号返回不同的变焦倍数
            var systemInfo = utsname()
            uname(&systemInfo)
            let deviceName = Mirror(reflecting: systemInfo.machine).children.reduce("") { identifier, element in
                guard let value = element.value as? Int8, value != 0 else { return identifier }
                return identifier + String(UnicodeScalar(UInt8(value)))
            }.lowercased()

            if deviceName.contains("iphone13,3") { // iPhone 12 Pro Max
                return 2.5  // iPhone 12 Pro Max: 2.5x光学镜头
            } else {
                return 2.0  // 其他机型: 2x变焦
            }
        case .telephoto3: return 3.0  // 3倍长焦
        case .telephoto5: return 5.0  // 5倍长焦
        }
    }

    // 判断是否为数码变焦
    var isDigitalZoom: Bool {
        // 对于2x镜头，需要根据设备型号判断是否为数码变焦
        switch self {
        case .telephoto:
            // 获取设备型号信息
            var systemInfo = utsname()
            uname(&systemInfo)
            let deviceName = Mirror(reflecting: systemInfo.machine).children.reduce("") { identifier, element in
                guard let value = element.value as? Int8, value != 0 else { return identifier }
                return identifier + String(UnicodeScalar(UInt8(value)))
            }.lowercased()

            // iPhone 14 Pro及更新机型的2x是数码变焦
            // 使用正确的设备标识符
            if deviceName.contains("iphone17") || // iPhone 16 series
               deviceName.contains("iphone16") || // iPhone 15 series
               deviceName.contains("iphone15") {  // iPhone 14 series
                return true  // 这些机型的2x是数码变焦
            } else if deviceName.contains("iphone13") { // iPhone 12 series
                // iPhone 12 Pro/Pro Max: 2x/2.5x都是光学镜头
                return false
            }
            // 其他情况默认为光学镜头
            return false
        default:
            // 其他镜头都是光学镜头
            return false
        }
    }

    // 获取下一个镜头类型
    var next: CameraLensType {
        let allCases = CameraLensType.allCases
        let currentIndex = allCases.firstIndex(of: self) ?? 0
        let nextIndex = (currentIndex + 1) % allCases.count
        return allCases[nextIndex]
    }

    // 获取上一个镜头类型
    var previous: CameraLensType {
        let allCases = CameraLensType.allCases
        let currentIndex = allCases.firstIndex(of: self) ?? 0
        let previousIndex = (currentIndex - 1 + allCases.count) % allCases.count
        return allCases[previousIndex]
    }

    // 获取摄像头位置（前置或后置）
    var position: AVCaptureDevice.Position {
        return self == .front ? .front : .back
    }
}
