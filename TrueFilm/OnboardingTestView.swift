//
//  OnboardingTestView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/15.
//

import SwiftUI

struct OnboardingTestView: View {
    @State private var showOnboarding = true
    
    var body: some View {
        if showOnboarding {
            OnboardingView(isFirstLaunch: $showOnboarding)
        } else {
            VStack {
                Text("Onboarding 完成!")
                    .font(.title)
                    .padding()
                
                <PERSON><PERSON>("重新显示 Onboarding") {
                    showOnboarding = true
                }
                .padding()
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
    }
}

#Preview {
    OnboardingTestView()
} 