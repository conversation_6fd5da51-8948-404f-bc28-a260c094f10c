//
//  OnboardingView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/15.
//

import SwiftUI
import Foundation
import StoreKit

struct OnboardingView: View {
    @Binding var isFirstLaunch: Bool
    var isRealFirstLaunch: Bool = true // 新增参数，区分是否是真正的首次启动
    @State private var currentPage = 0
    @State private var animateContent = false
    @State private var isWhiteMode: Bool = UserDefaults.standard.bool(forKey: "whiteUIMode")
    @State private var currentLanguage: String = {
        let savedLanguage = UserDefaults.standard.string(forKey: "selectedLanguage") ?? "zh-Hans"
        return savedLanguage.contains("zh") ? "zh" : "en"
    }()
    @State private var forceRefresh: Bool = false
    
    // 引导页面数据 - 横向教程设计（包含首页）
    private let onboardingPages: [OnboardingPage] = [
        // 首页：APP简介（占位，实际内容在AppIntroPageView中）
        OnboardingPage(
            titleKey: "ONBOARDING_WELCOME_TITLE",
            descriptionKey: "ONBOARDING_WELCOME_DESC",
            iconName: "camera.fill",
            accentColor: Color.orange,
            demoType: .welcome
        ),
        OnboardingPage(
            titleKey: "ONBOARDING_DIAL_BUTTON_TITLE",
            descriptionKey: "ONBOARDING_DIAL_BUTTON_DESC",
            iconName: "dial.max",
            accentColor: Color.orange,
            demoType: .dialAndButton
        ),
        OnboardingPage(
            titleKey: "ONBOARDING_FOCAL_LENGTH_TITLE",
            descriptionKey: "ONBOARDING_FOCAL_LENGTH_DESC",
            iconName: "move.3d",
            accentColor: Color.blue,
            demoType: .focalLength
        ),
        OnboardingPage(
            titleKey: "ONBOARDING_FLASH_RECORD_TITLE",
            descriptionKey: "ONBOARDING_FLASH_RECORD_DESC",
            iconName: "bolt.circle",
            accentColor: Color.yellow,
            demoType: .flashAndRecord
        ),
        OnboardingPage(
            titleKey: "ONBOARDING_SHOULDER_SCREEN_TITLE",
            descriptionKey: "ONBOARDING_SHOULDER_SCREEN_DESC",
            iconName: "display",
            accentColor: Color.purple,
            demoType: .shoulderScreen
        )
    ]
    
    private let hapticFeedback = UIImpactFeedbackGenerator(style: .medium)
    
    var body: some View {
        ZStack {
            // 背景 - 深色拟物风格
            LinearGradient(
                gradient: Gradient(colors: [
                    Color(red: 0.08, green: 0.08, blue: 0.12),
                    Color(red: 0.12, green: 0.12, blue: 0.18),
                    Color(red: 0.08, green: 0.08, blue: 0.12)
                ]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)
            
            // 添加环境光效果
            ZStack {
                Circle()
                    .fill(Color.white.opacity(0.1))
                    .frame(width: 200, height: 200)
                    .blur(radius: 60)
                    .offset(x: -200, y: -50)
                
                Circle()
                    .fill(Color.blue.opacity(0.06))
                    .frame(width: 250, height: 250)
                    .blur(radius: 80)
                    .offset(x: 200, y: 50)
                
                Circle()
                    .fill(Color.orange.opacity(0.04))
                    .frame(width: 180, height: 180)
                    .blur(radius: 50)
                    .offset(x: 0, y: -80)
            }
            
            // 横屏布局 - 分页设计
            HStack(spacing: 0) {
                // 左侧区域：根据页面显示不同内容
                VStack {
                    Spacer(minLength: 60)
                    
                    if currentPage == 0 {
                        // 首页：显示Logo和APP名称
                        VStack(spacing: 20) {
                            // App Logo - 简化版本
                            Image("logo")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 100, height: 100)
                                .clipShape(RoundedRectangle(cornerRadius: 20))
                                .shadow(color: Color.black.opacity(0.6), radius: 8, x: 4, y: 4)
                                .shadow(color: Color.white.opacity(0.1), radius: 4, x: -2, y: -2)
                                .scaleEffect(animateContent ? 1.0 : 0.8)
                                .animation(.easeOut(duration: 0.8).delay(0.2), value: animateContent)
                            
                            // App 名称
                            Text("MistLens")
                                .font(.system(size: 36, weight: .bold, design: .rounded))
                                .foregroundColor(.white)
                                .shadow(color: .black.opacity(0.5), radius: 2, x: 1, y: 1)
                                .opacity(animateContent ? 1.0 : 0)
                                .animation(.easeOut(duration: 0.6).delay(0.4), value: animateContent)
                                .id("app-name-\(forceRefresh)")
                        }
                    } else {
                        // 功能页面：显示属性数值
                        TutorialParameterDisplay(
                            pageIndex: currentPage,
                            accentColor: onboardingPages[currentPage].accentColor,
                            animateContent: animateContent
                        )
                    }
                    
                    Spacer(minLength: 40)
                    
                    // 水平进度指示器
                    HStack(spacing: 12) {
                        ForEach(0..<onboardingPages.count, id: \.self) { index in
                            SkeuomorphicDot(
                                isActive: currentPage == index,
                                accentColor: index == 0 ? Color.orange : onboardingPages[index].accentColor
                            )
                            .onTapGesture {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    currentPage = index
                                }
                                hapticFeedback.impactOccurred()
                            }
                            .scaleEffect(currentPage == index ? 1.1 : 1.0)
                            .animation(.easeOut(duration: 0.2), value: currentPage)
                        }
                    }
                    .padding(.bottom, 25)
                    
                    // 导航按钮区域
                    VStack(spacing: 0) {
                        if currentPage < onboardingPages.count - 1 {
                            // 下一步按钮
                            SkeuomorphicButton(
                                title: "NEXT".localized,
                                style: .primary,
                                action: {
                                    withAnimation(.easeInOut(duration: 0.3)) {
                                        currentPage += 1
                                    }
                                    hapticFeedback.impactOccurred()
                                }
                            )
                            .frame(maxWidth: 160)
                            .id("next-button-\(forceRefresh)")
                        } else {
                            // 开始使用按钮
                            SkeuomorphicButton(
                                title: "GET_STARTED".localized,
                                style: .primary,
                                action: {
                                    hapticFeedback.impactOccurred()
                                    withAnimation(.easeOut(duration: 0.5)) {
                                        isFirstLaunch = false
                                    }
                                    
                                    // 只有在真正的首次启动时才请求评分
                                    if isRealFirstLaunch {
                                        // 延迟请求评分，让用户先看到主界面
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                                            requestAppReview()
                                        }
                                    }
                                }
                            )
                            .frame(maxWidth: 160)
                            .id("get-started-button-\(forceRefresh)")
                        }
                    }
                    .padding(.bottom, 40)
                }
                .frame(width: UIScreen.main.bounds.width * 0.35)
                .padding(.horizontal, 25)
                
                // 中间：教程内容区域
                TabView(selection: $currentPage) {
                    // 首页：APP简介
                    AppIntroPageView(
                        isActive: currentPage == 0,
                        animateContent: $animateContent
                    )
                    .tag(0)
                    
                    // 功能教程页面
                    ForEach(1..<onboardingPages.count, id: \.self) { index in
                        FunctionTutorialPageView(
                            page: onboardingPages[index],
                            isActive: currentPage == index,
                            animateContent: $animateContent
                        )
                        .tag(index)
                    }
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
                .frame(width: UIScreen.main.bounds.width * 0.65)
            }
        }
        
        // 语言切换按钮 - 固定位置overlay
        .overlay(
            Button(action: {
                let haptic = UIImpactFeedbackGenerator(style: .light)
                haptic.impactOccurred()
                toggleLanguage()
                         }) {
                 Text(currentLanguage == "zh" ? "English" : "中文")
                     .font(.system(size: 14, weight: .medium))
                     .foregroundColor(.white.opacity(0.9))
                     .shadow(color: .black.opacity(0.5), radius: 2, x: 1, y: 1)
                     .id("language-text-\(currentLanguage)-\(forceRefresh)")
             }
            .buttonStyle(PlainButtonStyle())
            .position(x: UIScreen.main.bounds.width - 50, y: 60)
        )
        
        // 强制横屏显示
        .ignoresSafeArea(.all)
        .modifier(PersistentSystemOverlaysModifier())
        .onAppear {
            withAnimation {
                animateContent = true
            }
        }
        .onChange(of: currentPage) { _ in
            // 页面切换时重新触发动画
            animateContent = false
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation {
                    animateContent = true
                }
            }
        }
    }
    
    // 语言切换功能
    private func toggleLanguage() {
        // 立即更新状态，确保按钮状态同步
        if currentLanguage == "zh" {
            currentLanguage = "en"
            setAppLanguage(to: "en")
        } else {
            currentLanguage = "zh"
            setAppLanguage(to: "zh-Hans")
        }
    }
    
    private func setAppLanguage(to language: String) {
        // 保存语言偏好到两个地方，确保与应用的语言系统兼容
        UserDefaults.standard.set(language, forKey: "selectedLanguage")
        UserDefaults.standard.set([language], forKey: "AppleLanguages")
        UserDefaults.standard.synchronize()
        
        // 使用应用现有的语言切换系统
        Bundle.setLanguage(language)
        
        // 仅在OnboardingView内部刷新，不发送应用级别的通知
        // 这样可以避免整个应用刷新导致退出OnboardingView
        
        // 强制刷新界面
        forceRefresh.toggle()
        
        // 重新创建动画 - 更快的切换效果
        withAnimation(.easeInOut(duration: 0.3)) {
            animateContent = false
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            withAnimation(.easeInOut(duration: 0.4)) {
                animateContent = true
            }
        }
    }
    
    // MARK: - App Review Request
    private func requestAppReview() {
        // 检查是否已经请求过评分，避免重复弹出
        let hasRequestedReview = UserDefaults.standard.bool(forKey: "hasRequestedReviewAfterOnboarding")
        
        // 如果还没有请求过，则请求评分
        if !hasRequestedReview {
            // 使用StoreKit请求应用内评分
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                SKStoreReviewController.requestReview(in: windowScene)
                
                // 标记已经请求过评分
                UserDefaults.standard.set(true, forKey: "hasRequestedReviewAfterOnboarding")
                UserDefaults.standard.synchronize()
                
                print("已请求App评分")
            }
        }
    }
}

// 引导页面数据模型
struct OnboardingPage {
    let titleKey: String
    let descriptionKey: String
    let iconName: String
    let accentColor: Color
    let demoType: DemoType
}

// 演示类型枚举
enum DemoType {
    case welcome
    case dialAndButton
    case focalLength
    case flashAndRecord
    case shoulderScreen
}

// APP简介页面视图
struct AppIntroPageView: View {
    let isActive: Bool
    @Binding var animateContent: Bool
    
    var body: some View {
        VStack(spacing: 25) {
            Spacer(minLength: 50)
            
            // APP详细描述
            ScrollView(.vertical, showsIndicators: false) {
                VStack {
                    Text("APP_DETAILED_DESCRIPTION".localized)
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.white.opacity(0.85))
                        .lineSpacing(8)
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil)
                        .fixedSize(horizontal: false, vertical: true)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 15)
                        .background(Color.clear)
                        .id("app-desc-\(isActive)-\(animateContent)")
                }
            }
            .frame(height: 280)
            .opacity(isActive && animateContent ? 1.0 : 0)
            .animation(.easeOut(duration: 0.8).delay(0.4), value: isActive && animateContent)
            
            Spacer(minLength: 30)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, 20)
    }
}

// 功能教程页面视图
struct FunctionTutorialPageView: View {
    let page: OnboardingPage
    let isActive: Bool
    @Binding var animateContent: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Spacer(minLength: 20)
            
            // 标题
            Text(page.titleKey.localized)
                .font(.system(size: 28, weight: .bold, design: .rounded))
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
                .shadow(color: .black.opacity(0.5), radius: 2, x: 1, y: 1)
                .opacity(isActive && animateContent ? 1.0 : 0)
                .animation(.easeOut(duration: 0.6).delay(0.1), value: isActive && animateContent)
                .id("title-\(page.titleKey)-\(animateContent)")
            
            // 功能演示区域
            FunctionDemoView(
                demoType: page.demoType,
                accentColor: page.accentColor,
                isActive: isActive,
                animateContent: $animateContent
            )
            
            // 描述文字
            Text(page.descriptionKey.localized)
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .lineLimit(nil)
                .fixedSize(horizontal: false, vertical: true)
                .padding(.horizontal, 30)
                .opacity(isActive && animateContent ? 1.0 : 0.7)
                .animation(.easeOut(duration: 0.6).delay(0.3), value: isActive && animateContent)
                .id("desc-\(page.descriptionKey)-\(animateContent)")
            
            Spacer(minLength: 20)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.horizontal, 40)
        .padding(.vertical, 20)
    }
}

// 功能演示视图
struct FunctionDemoView: View {
    let demoType: DemoType
    let accentColor: Color
    let isActive: Bool
    @Binding var animateContent: Bool
    
    @State private var dialRotation: Double = 0
    @State private var focalIndex: Int = 0
    @State private var flashOn: Bool = false
    @State private var recording: Bool = false
    
    private var focalLengths: [String] {
        return ["前置".localized, "0.5×", "1×", "2×", "3×", "5×"]
    }
    
    var body: some View {
        ZStack {
            // 演示背景
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.black.opacity(0.4))
                .frame(width: 220, height: 160)
                .shadow(color: Color.black.opacity(0.8), radius: 6, x: 3, y: 3)
            
            RoundedRectangle(cornerRadius: 18)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 0.05, green: 0.05, blue: 0.08),
                            Color(red: 0.08, green: 0.08, blue: 0.12),
                            Color(red: 0.12, green: 0.12, blue: 0.16)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 210, height: 150)
            
            // 根据类型显示不同的演示内容
            switch demoType {
            case .welcome:
                // 首页不显示演示内容
                EmptyView()
            case .dialAndButton:
                dialAndButtonDemo
            case .focalLength:
                focalLengthDemo
            case .flashAndRecord:
                flashAndRecordDemo
            case .shoulderScreen:
                shoulderScreenDemo
            }
        }
        .scaleEffect(isActive && animateContent ? 1.0 : 0.9)
        .animation(.easeOut(duration: 0.8), value: isActive && animateContent)
        .onAppear {
            if isActive {
                startAnimations()
            }
        }
        .onChange(of: isActive) { active in
            if active {
                startAnimations()
            } else {
                stopAnimations()
            }
        }
        .onDisappear {
            stopAnimations()
        }
    }
    
    // 转盘+按钮演示
    private var dialAndButtonDemo: some View {
        VStack(spacing: 20) {
            // 转盘和Lock按钮
            HStack(spacing: 15) {
                // 使用真实的ControlDialView样式
                TutorialControlDialView(
                    dialRotation: dialRotation,
                    accentColor: accentColor
                )
                .frame(width: 100, height: 100)
                
                // Lock按钮 - 位于转盘右边
                VStack {
                    Spacer().frame(height: 20)
                    TutorialLockButton(accentColor: accentColor, isActive: isActive)
                    Spacer()
                }
                .frame(height: 100)
            }
            
            // 底部按钮组 - 使用真实的ControlButtonsView样式
            TutorialControlButtons(accentColor: accentColor, isActive: isActive)
        }
    }
    
    // 焦距切换演示
    private var focalLengthDemo: some View {
        VStack(spacing: 30) {
            // 焦距显示
            Text(focalLengths[focalIndex])
                .font(.system(size: 32, weight: .bold, design: .monospaced))
                .foregroundColor(accentColor)
                .shadow(color: accentColor.opacity(0.5), radius: 4, x: 0, y: 0)
            
            // 滑动指示器
            HStack(spacing: 8) {
                ForEach(0..<focalLengths.count, id: \.self) { index in
                    Circle()
                        .fill(index == focalIndex ? accentColor : Color.gray.opacity(0.3))
                        .frame(width: 8, height: 8)
                }
            }
            
            // 左右箭头
            HStack(spacing: 40) {
                Image(systemName: "chevron.left")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(accentColor.opacity(0.7))
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 20, weight: .bold))
                    .foregroundColor(accentColor.opacity(0.7))
            }
        }
    }
    
    // 闪光灯和录像演示
    private var flashAndRecordDemo: some View {
        VStack(spacing: 25) {
            // 闪光灯按钮
            ZStack {
                Circle()
                    .fill(flashOn ? Color.yellow.opacity(0.8) : Color.gray.opacity(0.3))
                    .frame(width: 50, height: 50)
                    .shadow(color: flashOn ? Color.yellow.opacity(0.6) : Color.clear, radius: 8, x: 0, y: 0)
                
                Image(systemName: "bolt.fill")
                    .font(.system(size: 24))
                    .foregroundColor(flashOn ? .black : .white)
            }
            
            Text("Flash")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.gray)
            
            // 录像按钮
            ZStack {
                Circle()
                    .fill(recording ? Color.red.opacity(0.8) : Color.gray.opacity(0.3))
                    .frame(width: 50, height: 50)
                
                RoundedRectangle(cornerRadius: recording ? 6 : 15)
                    .fill(.white)
                    .frame(width: recording ? 12 : 30, height: recording ? 12 : 30)
                    .animation(.easeInOut(duration: 0.3), value: recording)
            }
            
            Text("Record")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(.gray)
        }
    }
    
    // 肩屏演示
    private var shoulderScreenDemo: some View {
        VStack(spacing: 20) {
            // 肩屏显示 - 使用GlassDisplayView样式
            TutorialGlassDisplayView(
                displayText: "F2.8  1/60",
                accentColor: accentColor
            )
            
            // 点击指示
            VStack(spacing: 8) {
                Image(systemName: "hand.tap")
                    .font(.system(size: 24))
                    .foregroundColor(accentColor)
                
                Text("Tap to enter\nSettings")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    // 存储定时器引用，以便在需要时清理
    @State private var animationTimers: [Timer] = []
    
    private func startAnimations() {
        // 清理之前的定时器
        stopAnimations()
        
        switch demoType {
        case .welcome:
            // 首页无需动画
            break
        case .dialAndButton:
            // 转盘只转一下，然后停留
            withAnimation(.easeInOut(duration: 1.5)) {
                dialRotation = 360
            }
        case .focalLength:
            let timer = Timer.scheduledTimer(withTimeInterval: 2.5, repeats: true) { timer in
                guard self.isActive else {
                    timer.invalidate()
                    return
                }
                withAnimation(.easeInOut(duration: 0.5)) {
                    self.focalIndex = (self.focalIndex + 1) % self.focalLengths.count
                }
            }
            animationTimers.append(timer)
        case .flashAndRecord:
            let flashTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { timer in
                guard self.isActive else {
                    timer.invalidate()
                    return
                }
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.flashOn.toggle()
                }
            }
            let recordTimer = Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { timer in
                guard self.isActive else {
                    timer.invalidate()
                    return
                }
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.recording.toggle()
                }
            }
            animationTimers.append(flashTimer)
            animationTimers.append(recordTimer)
        case .shoulderScreen:
            // 肩屏无需特殊动画
            break
        }
    }
    
    private func stopAnimations() {
        animationTimers.forEach { $0.invalidate() }
        animationTimers.removeAll()
    }
}

// 拟物风格的指示点
struct SkeuomorphicDot: View {
    let isActive: Bool
    let accentColor: Color
    
    var body: some View {
        ZStack {
            // 凹陷底座
            Circle()
                .fill(Color.black.opacity(0.6))
                .frame(width: 16, height: 16)
                .shadow(color: Color.black.opacity(0.8), radius: 2, x: 1, y: 1)
                .shadow(color: Color.white.opacity(0.05), radius: 1, x: -0.5, y: -0.5)
            
            // 指示点
            Circle()
                .fill(
                    isActive 
                    ? LinearGradient(
                        gradient: Gradient(colors: [
                            accentColor.opacity(0.8),
                            accentColor,
                            accentColor.opacity(0.6)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                      )
                    : LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 0.08, green: 0.08, blue: 0.12),
                            Color(red: 0.05, green: 0.05, blue: 0.08)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                      )
                )
                .frame(width: isActive ? 12 : 8, height: isActive ? 12 : 8)
                .overlay(
                    Circle()
                        .stroke(Color.black.opacity(0.4), lineWidth: 0.5)
                )
            
            // 活跃状态的光晕
            if isActive {
                Circle()
                    .fill(accentColor.opacity(0.3))
                    .frame(width: 20, height: 20)
                    .blur(radius: 6)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: isActive)
    }
}

// 拟物风格按钮
struct SkeuomorphicButton: View {
    let title: String
    let style: ButtonStyle
    let action: () -> Void
    
    @State private var isPressed = false
    
    enum ButtonStyle {
        case primary
        case secondary
    }
    
    var body: some View {
        Button(action: action) {
            ZStack {
                // 按钮阴影和底座
                RoundedRectangle(cornerRadius: 25)
                    .fill(Color.black.opacity(0.6))
                                    .frame(height: 52)
                .shadow(color: Color.black.opacity(0.8), radius: 8, x: 4, y: 4)
                .shadow(color: Color.white.opacity(0.05), radius: 4, x: -2, y: -2)
                
                // 按钮主体
                RoundedRectangle(cornerRadius: 22)
                    .fill(
                        style == .primary
                        ? LinearGradient(
                            gradient: Gradient(colors: [
                                Color.orange.opacity(0.9),
                                Color.orange,
                                Color.orange.opacity(0.7)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                          )
                        : LinearGradient(
                            gradient: Gradient(colors: [
                                Color(red: 0.15, green: 0.15, blue: 0.2),
                                Color(red: 0.12, green: 0.12, blue: 0.18),
                                Color(red: 0.08, green: 0.08, blue: 0.12)
                            ]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                          )
                    )
                    .frame(height: 46)
                    .overlay(
                        RoundedRectangle(cornerRadius: 22)
                            .stroke(
                                style == .primary 
                                ? Color.orange.opacity(0.6)
                                : Color.white.opacity(0.1),
                                lineWidth: 1
                            )
                    )
                
                // 按钮文字
                Text(title)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(
                        style == .primary ? .white : .gray
                    )
                    .shadow(
                        color: style == .primary ? .black.opacity(0.3) : .clear,
                        radius: 1, x: 0, y: 1
                    )
                
                // 按下效果
                if isPressed {
                    RoundedRectangle(cornerRadius: 22)
                        .fill(Color.black.opacity(0.2))
                        .frame(height: 46)
                }
                
                // 主要按钮的光晕效果
                if style == .primary {
                    RoundedRectangle(cornerRadius: 22)
                        .fill(Color.clear)
                        .frame(height: 46)
                        .overlay(
                            RoundedRectangle(cornerRadius: 22)
                                .stroke(Color.orange.opacity(0.4), lineWidth: 2)
                                .blur(radius: 2)
                        )
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isPressed ? 0.96 : 1.0)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

#Preview {
    OnboardingView(isFirstLaunch: .constant(true), isRealFirstLaunch: true)
}

#Preview("Dial Tutorial") {
    ZStack {
        // 深色背景
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.12, green: 0.12, blue: 0.18)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .edgesIgnoringSafeArea(.all)
        
        FunctionTutorialPageView(
            page: OnboardingPage(
                titleKey: "ONBOARDING_DIAL_BUTTON_TITLE",
                descriptionKey: "ONBOARDING_DIAL_BUTTON_DESC",
                iconName: "dial.max",
                accentColor: Color.orange,
                demoType: .dialAndButton
            ),
            isActive: true,
            animateContent: .constant(true)
        )
    }
}

#Preview("Focal Length Tutorial") {
    ZStack {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.12, green: 0.12, blue: 0.18)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .edgesIgnoringSafeArea(.all)
        
        FunctionTutorialPageView(
            page: OnboardingPage(
                titleKey: "ONBOARDING_FOCAL_LENGTH_TITLE",
                descriptionKey: "ONBOARDING_FOCAL_LENGTH_DESC",
                iconName: "move.3d",
                accentColor: Color.blue,
                demoType: .focalLength
            ),
            isActive: true,
            animateContent: .constant(true)
        )
    }
}

#Preview("Flash & Record Tutorial") {
    ZStack {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.12, green: 0.12, blue: 0.18)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .edgesIgnoringSafeArea(.all)
        
        FunctionTutorialPageView(
            page: OnboardingPage(
                titleKey: "ONBOARDING_FLASH_RECORD_TITLE",
                descriptionKey: "ONBOARDING_FLASH_RECORD_DESC",
                iconName: "bolt.circle",
                accentColor: Color.yellow,
                demoType: .flashAndRecord
            ),
            isActive: true,
            animateContent: .constant(true)
        )
    }
}

// 教程专用的转盘视图，基于ControlDialView样式但用于演示
struct TutorialControlDialView: View {
    let dialRotation: Double
    let accentColor: Color
    var isWhiteMode: Bool = false // 在教程中固定使用深色模式风格
    @State private var isPressed: Bool = false
    
    var body: some View {
        GeometryReader { geometry in
            let center = CGPoint(x: geometry.size.width/2, y: geometry.size.height/2)
            let dialRadius: CGFloat = min(geometry.size.width, geometry.size.height)/2 - 8

            ZStack {
                // Main dial body - 使用与ControlDialView相同的渐变
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.gray.opacity(0.9), Color.white.opacity(0.95), Color.gray.opacity(0.85)]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: dialRadius * 2, height: dialRadius * 2)
                    .position(center)
                    .shadow(color: Color.black.opacity(0.3), radius: 4, x: 0, y: 2)

                // 同心圆 - 使用ControlDialView的ConcentricCircles样式
                TutorialConcentricCircles(center: center, radius: dialRadius, count: 3)
                    .stroke(Color.gray.opacity(0.1), lineWidth: 0.5)

                // Center highlight
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.6),
                                Color.clear
                            ]),
                            center: .center,
                            startRadius: 0,
                            endRadius: dialRadius * 0.5
                        )
                    )
                    .frame(width: dialRadius * 1.2, height: dialRadius * 1.2)
                    .position(center)
                    .blendMode(.screen)
                


                // 齿轮边缘 - 使用ControlDialView的样式
                TutorialSerratedDialEdge(center: center, innerRadius: dialRadius - 2, outerRadius: dialRadius + 6, teethCount: 18)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [Color.gray.opacity(0.9), Color.white.opacity(0.95), Color.gray.opacity(0.85)]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .shadow(color: Color.black.opacity(0.15), radius: 0.5, x: 0, y: 0.5)
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
            .rotationEffect(.degrees(dialRotation), anchor: .center)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
        }
        .aspectRatio(1, contentMode: .fit)
        .onTapGesture {
            // 模拟点击效果
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
        }
        .onAppear {
            // 使用异步方式避免在视图更新时创建Timer
            DispatchQueue.main.async {
                Timer.scheduledTimer(withTimeInterval: 4.0, repeats: true) { timer in
                    // 检查视图是否仍然存在
                    guard !self.isPressed else {
                        // 如果组件正在被按下，说明可能正在动画中，先不操作
                        return
                    }
                    
                    withAnimation(.easeInOut(duration: 0.1)) {
                        self.isPressed = true
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.15) {
                        withAnimation(.easeInOut(duration: 0.1)) {
                            self.isPressed = false
                        }
                    }
                }
            }
        }
    }
}

// 教程专用的同心圆形状
struct TutorialConcentricCircles: Shape {
    let center: CGPoint
    let radius: CGFloat
    let count: Int

    func path(in rect: CGRect) -> Path {
        var path = Path()
        for i in 1...count {
            let circleRadius = radius * CGFloat(i * 3) / CGFloat(count * 4)
            path.addEllipse(in: CGRect(
                x: center.x - circleRadius,
                y: center.y - circleRadius,
                width: circleRadius * 2,
                height: circleRadius * 2
            ))
        }
        return path
    }
}

// 教程专用的齿轮边缘形状
struct TutorialSerratedDialEdge: Shape {
    let center: CGPoint
    let innerRadius: CGFloat
    let outerRadius: CGFloat
    let teethCount: Int

    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        for i in 0..<teethCount {
            let startAngle = Double(i) * (2 * .pi / Double(teethCount))
            let endAngle = Double(i + 1) * (2 * .pi / Double(teethCount))
            let midAngle = (startAngle + endAngle) / 2
            
            let startX = center.x + innerRadius * CGFloat(cos(startAngle))
            let startY = center.y + innerRadius * CGFloat(sin(startAngle))
            
            if i == 0 {
                path.move(to: CGPoint(x: startX, y: startY))
            }
            
            let toothHeight = (outerRadius - innerRadius) * 0.65
            let toothWidthRatio = 0.80
            
            let toothStartAngle = startAngle + (1.0 - toothWidthRatio) * (endAngle - startAngle) * 0.5
            let toothEndAngle = endAngle - (1.0 - toothWidthRatio) * (endAngle - startAngle) * 0.5
            
            let leftX = center.x + (innerRadius + toothHeight * 0.3) * CGFloat(cos(toothStartAngle))
            let leftY = center.y + (innerRadius + toothHeight * 0.3) * CGFloat(sin(toothStartAngle))
            
            let topX = center.x + (innerRadius + toothHeight * 1.05) * CGFloat(cos(midAngle))
            let topY = center.y + (innerRadius + toothHeight * 1.05) * CGFloat(sin(midAngle))
            
            let rightX = center.x + (innerRadius + toothHeight * 0.3) * CGFloat(cos(toothEndAngle))
            let rightY = center.y + (innerRadius + toothHeight * 0.3) * CGFloat(sin(toothEndAngle))
            
            let endX = center.x + innerRadius * CGFloat(cos(endAngle))
            let endY = center.y + innerRadius * CGFloat(sin(endAngle))
            
            // 简化的路径绘制
            path.addLine(to: CGPoint(x: leftX, y: leftY))
            path.addLine(to: CGPoint(x: topX, y: topY))
            path.addLine(to: CGPoint(x: rightX, y: rightY))
            path.addLine(to: CGPoint(x: endX, y: endY))
        }

        path.addArc(center: center, radius: innerRadius, startAngle: .radians(2 * .pi), endAngle: .radians(0), clockwise: true)
        return path
    }
}

// 教程专用的控制按钮组件
struct TutorialControlButtons: View {
    let accentColor: Color
    let isActive: Bool
    @State private var selectedButton: String = "MIST"
    
    var body: some View {
        HStack(spacing: 18) {
            // MIST 按钮
            TutorialModeButton(
                title: "MIST",
                isSelected: selectedButton == "MIST",
                accentColor: accentColor
            )
            
            // Highlight 按钮
            TutorialModeButton(
                title: "Highlight",
                isSelected: selectedButton == "Highlight",
                accentColor: accentColor
            )
            
            // EV 按钮
            TutorialModeButton(
                title: "EV",
                isSelected: selectedButton == "EV",
                accentColor: accentColor
            )
        }
        .onAppear {
            // 只在当前激活状态下启动定时器
            if isActive {
                DispatchQueue.main.async {
                    Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { timer in
                        // 检查组件是否仍然激活
                        guard self.isActive else {
                            timer.invalidate()
                            return
                        }
                        withAnimation(.easeInOut(duration: 0.5)) {
                            if self.selectedButton == "MIST" {
                                self.selectedButton = "" // 取消选择，这时Lock会亮起
                            } else {
                                self.selectedButton = "MIST" // 重新选择MIST
                            }
                        }
                    }
                }
            }
        }
    }
}

// 教程专用的模式按钮
struct TutorialModeButton: View {
    let title: String
    let isSelected: Bool
    let accentColor: Color
    
    // 定义橙色强调色
    private var accentOrange: Color {
        Color(red: 1.0, green: 0.58, blue: 0.0) // 橙色 #FF9500
    }
    
    var body: some View {
        VStack(spacing: 4) {
            // 标题文字
            Text(title)
                .font(.system(size: 10, weight: isSelected ? .medium : .regular))
                .foregroundColor(isSelected ? accentOrange : .gray.opacity(0.7))
            
            // 圆形按钮
            ZStack {
                // 外层阴影 - 创建凹陷效果
                Circle()
                    .fill(Color.black.opacity(0.8))
                    .overlay(
                        Circle()
                            .stroke(LinearGradient(
                                gradient: Gradient(colors: [
                                    Color.black.opacity(0.9),
                                    Color.gray.opacity(0.15)
                                ]),
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ), lineWidth: 1.5)
                    )
                    .shadow(color: Color.black.opacity(0.8), radius: 2, x: 1, y: 1)
                    .shadow(color: Color.gray.opacity(0.2), radius: 1, x: -1, y: -1)

                // 内层圆形 - 凹陷表面
                Circle()
                    .fill(Color(red: 0.12, green: 0.12, blue: 0.12))
                    .frame(width: 36, height: 36)
                    .overlay(
                        Circle()
                            .stroke(Color.black.opacity(0.8), lineWidth: 1)
                    )
                    .shadow(color: Color.black.opacity(0.6), radius: 2, x: 0, y: 0)

                // 选中时的橙色高亮效果
                if isSelected {
                    // 内部橙色光晕
                    Circle()
                        .fill(accentOrange.opacity(0.15))
                        .frame(width: 34, height: 34)
                        .blur(radius: 1)
                    
                    // 橙色边框高亮
                    Circle()
                        .fill(Color.clear)
                        .frame(width: 36, height: 36)
                        .overlay(
                            Circle()
                                .stroke(accentOrange.opacity(0.8), lineWidth: 1.0)
                        )
                    
                    // 外层橙色光晕
                    Circle()
                        .fill(Color.clear)
                        .frame(width: 38, height: 38)
                        .overlay(
                            Circle()
                                .stroke(accentOrange.opacity(0.4), lineWidth: 0.5)
                        )
                }
            }
            .frame(width: 40, height: 40)
        }
    }
}

// 教程专用的参数显示组件
struct TutorialParameterDisplay: View {
    let pageIndex: Int
    let accentColor: Color
    let animateContent: Bool
    
    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 25)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            Color(red: 0.15, green: 0.15, blue: 0.2),
                            Color(red: 0.08, green: 0.08, blue: 0.12)
                        ]),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .frame(width: 120, height: 120)
                .shadow(color: Color.black.opacity(0.6), radius: 8, x: 4, y: 4)
            
            // 根据页面索引显示不同的参数值
            VStack(spacing: 8) {
                // 参数值
                Text(getParameterValue())
                    .font(.system(size: 24, weight: .bold, design: .monospaced))
                    .foregroundColor(accentColor)
                    .shadow(color: accentColor.opacity(0.5), radius: 4, x: 0, y: 0)
                
                // 参数名称
                Text(getParameterName())
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.gray.opacity(0.8))
            }
        }
        .scaleEffect(animateContent ? 1.0 : 0.8)
        .animation(.easeOut(duration: 0.6), value: animateContent)
    }
    
    private func getParameterValue() -> String {
        switch pageIndex {
        case 1: return "0.3" // 转盘与按钮控制 - 黑柔强度
        case 2: return "1.0×" // 焦距控制
        case 3: return "0.48" // 闪光灯与录像 - 高光阈值
        case 4: return "-0.17" // 肩屏设置 - 亮度
        default: return "0.0"
        }
    }
    
    private func getParameterName() -> String {
        switch pageIndex {
        case 1: return "MIST"
        case 2: return "ZOOM"
        case 3: return "THLD"
        case 4: return "BRIT"
        default: return ""
        }
    }
}

// 教程专用的玻璃显示组件，基于GlassDisplayView
struct TutorialGlassDisplayView: View {
    let displayText: String
    let accentColor: Color
    
    // 判断文本是否为快门速度格式 (如 1/60)
    private var isShutterSpeed: Bool {
        return displayText.contains("/") && !displayText.contains(" ")
    }
    
    var body: some View {
        ZStack {
            // 玻璃显示背景 - 基于GlassDisplayView的设计
            RoundedRectangle(cornerRadius: 25)
                .fill(Color.black)
                .frame(width: 160, height: 50)
                .overlay(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .background(
                    // 内部背景，添加金属质感的渐变
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color(red: 0.94, green: 0.94, blue: 0.94))
                        .frame(width: 160, height: 50)
                )
                .shadow(color: Color.black.opacity(0.2), radius: 3, x: 0, y: 1)

            // 显示文字
            Text(displayText)
                .font(.system(size: isShutterSpeed ? 24 : 20, weight: .medium, design: .monospaced))
                .foregroundColor(Color.white)
                .tracking(isShutterSpeed ? 2.0 : 1.5)
                .frame(width: 150)
                .multilineTextAlignment(.center)
        }
    }
}

// 教程专用的Lock按钮组件
struct TutorialLockButton: View {
    let accentColor: Color
    let isActive: Bool
    @State private var isLocked: Bool = false
    @State private var isButtonPressed: Bool = false // 用于跟踪按钮是否被按下
    
    var body: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.3)) {
                isLocked.toggle()
            }
        }) {
            Text("Lock")
                .font(.system(size: 10, weight: .medium))
                .foregroundColor(isLocked ? Color.orange : Color.gray)
                .padding(.horizontal, 6)
                .padding(.vertical, 3)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.black.opacity(0.3))
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isLocked ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isLocked)
        .onAppear {
            // 只在当前激活状态下启动定时器
            if isActive {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    Timer.scheduledTimer(withTimeInterval: 3.0, repeats: true) { timer in
                        // 检查组件是否仍然激活
                        guard self.isActive else {
                            timer.invalidate()
                            return
                        }
                        withAnimation(.easeInOut(duration: 0.3)) {
                            self.isLocked.toggle()
                        }
                    }
                }
            }
        }
    }
}

#Preview("App Intro") {
    ZStack {
        LinearGradient(
            gradient: Gradient(colors: [
                Color(red: 0.08, green: 0.08, blue: 0.12),
                Color(red: 0.12, green: 0.12, blue: 0.18)
            ]),
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
        .edgesIgnoringSafeArea(.all)
        
        AppIntroPageView(
            isActive: true,
            animateContent: .constant(true)
        )
    }
}


