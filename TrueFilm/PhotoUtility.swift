//
//  PhotoUtility.swift
//  TrueFilm
//
//  Created by Augment on 2025/5/15.
//

import UIKit
import CoreLocation
import ImageIO
import MobileCoreServices
import AVFoundation
import Photos

enum PhotoQuality: Int {
    case standard = 0
    case high = 1
    case maximum = 2

    var compressionQuality: CGFloat {
        switch self {
        case .standard:
            return 0.7
        case .high:
            return 0.9
        case .maximum:
            return 1.0
        }
    }
}

class PhotoUtility: NSObject {

    static let shared = PhotoUtility()

    private override init() {
        super.init()
    }

    // MARK: - Save high quality photo with EXIF data

    func saveHighQualityPhoto(image: UIImage,
                              location: CLLocation? = nil,
                              lensType: CameraLensType,
                              quality: PhotoQuality = .maximum,
                              iso: Double = 100,
                              aperture: Double = 2.8,
                              shutterSpeed: Double = 1/60,
                              exposureCompensation: Double = 0.0,
                              onAssetSaved: ((String?) -> Void)? = nil,
                              completion: @escaping (Bool, URL?) -> Void) {

        // Create a temporary file URL
        let temporaryDirectory = FileManager.default.temporaryDirectory
        let fileName = "MistLens_\(Date().timeIntervalSince1970).jpg"
        let fileURL = temporaryDirectory.appendingPathComponent(fileName)

        // Get CGImage with correct orientation
        let cgImage: CGImage
        let imageOrientation = image.imageOrientation
        
        if image.imageOrientation == .up {
            // If image is already in the correct orientation, use it directly
            guard let img = image.cgImage else {
                print("Failed to get CGImage from UIImage")
                completion(false, nil)
                return
            }
            cgImage = img
        } else {
            // If image has orientation metadata, correct it
            UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
            image.draw(in: CGRect(origin: .zero, size: image.size))
            if let normalizedImage = UIGraphicsGetImageFromCurrentImageContext()?.cgImage {
                cgImage = normalizedImage
            } else {
                guard let img = image.cgImage else {
                    print("Failed to get CGImage from UIImage")
                    completion(false, nil)
                    return
                }
                cgImage = img
            }
            UIGraphicsEndImageContext()
        }

        // Create destination with EXIF metadata
        guard let destination = CGImageDestinationCreateWithURL(fileURL as CFURL, kUTTypeJPEG, 1, nil) else {
            print("Failed to create CGImageDestination")
            completion(false, nil)
            return
        }

        // Create metadata dictionary
        let metadata = createMetadata(location: location, lensType: lensType, iso: iso, aperture: aperture, shutterSpeed: shutterSpeed, exposureCompensation: exposureCompensation)

        // Add the image with metadata to the destination
        CGImageDestinationAddImage(destination, cgImage, metadata as CFDictionary)

        // Finalize the destination
        if CGImageDestinationFinalize(destination) {
            // Save to photo library with high quality
            saveToPhotoLibrary(fileURL: fileURL, quality: quality, onAssetSaved: onAssetSaved, completion: completion)
        } else {
            print("Failed to finalize CGImageDestination")
            completion(false, nil)
        }
    }

    // MARK: - Create metadata dictionary

    private func createMetadata(location: CLLocation?, lensType: CameraLensType, iso: Double, aperture: Double, shutterSpeed: Double, exposureCompensation: Double) -> [String: Any] {
        var metadata: [String: Any] = [:]

        // 首先计算所有需要的值
        // 确保 ISO 值在有效范围内
        let isoValue = max(50, min(3200, iso)) // 限制 ISO 在 50-3200 范围内
        let apertureString = String(format: "f/%.1f", aperture)
        let shutterSpeedString = String(format: "1/%.0fs", 1.0/shutterSpeed)

        // TIFF dictionary
        var tiffDict: [String: Any] = [:]
        tiffDict[kCGImagePropertyTIFFMake as String] = "Apple"
        tiffDict[kCGImagePropertyTIFFModel as String] = UIDevice.current.model
        tiffDict[kCGImagePropertyTIFFSoftware as String] = "MistLens Camera"
        tiffDict[kCGImagePropertyTIFFDateTime as String] = dateFormatter.string(from: Date())

        // 在图像描述中添加完整的相机参数
        let exposureCompString = exposureCompensation == 0 ? "" : String(format: " | EV %.1f", exposureCompensation)
        tiffDict[kCGImagePropertyTIFFImageDescription as String] = "Shot by MistLens | \(lensType.description) | ISO \(Int(isoValue)) | \(apertureString) | \(shutterSpeedString)\(exposureCompString)"

        tiffDict[kCGImagePropertyTIFFCopyright as String] = "MistLens \(Calendar.current.component(.year, from: Date()))"
        metadata[kCGImagePropertyTIFFDictionary as String] = tiffDict

        // EXIF dictionary
        var exifDict: [String: Any] = [:]
        exifDict[kCGImagePropertyExifDateTimeOriginal as String] = dateFormatter.string(from: Date())
        exifDict[kCGImagePropertyExifDateTimeDigitized as String] = dateFormatter.string(from: Date())

        // Add focal length - 使用实际的 iPhone 镜头焦距
        let focalLengthValue = lensType.focalLength
        exifDict[kCGImagePropertyExifFocalLength as String] = focalLengthValue

        // Add lens information - 使用实际的镜头描述
        exifDict[kCGImagePropertyExifLensModel as String] = lensType.description
        exifDict[kCGImagePropertyExifLensMake as String] = "Apple"

        // 添加更多镜头信息到 TIFF 字典
        tiffDict[kCGImagePropertyTIFFModel as String] = "\(UIDevice.current.model) \(lensType.description)"

        // Add ISO, aperture, and shutter speed
        exifDict[kCGImagePropertyExifISOSpeedRatings as String] = [Int(isoValue)]

        // 添加光圈值 - 使用多种方式确保它能被识别
        exifDict[kCGImagePropertyExifFNumber as String] = aperture
        exifDict[kCGImagePropertyExifApertureValue as String] = log2(aperture * aperture) // 使用 APEX 值
        exifDict["Aperture"] = String(format: "f/%.1f", aperture) // 添加自定义光圈标签

        // 添加快门速度
        exifDict[kCGImagePropertyExifExposureTime as String] = shutterSpeed

        // 添加曝光补偿值 - 使用多种方式确保它能被识别
        exifDict[kCGImagePropertyExifExposureBiasValue as String] = exposureCompensation
        exifDict["ExposureCompensation"] = String(format: "%.1f EV", exposureCompensation) // 添加自定义曝光补偿标签

        // 添加更多相机参数
        exifDict[kCGImagePropertyExifSensitivityType as String] = 2 // 2 表示 REI (推荐曝光指数)
        exifDict[kCGImagePropertyExifMeteringMode as String] = 5 // 5 表示模式测光

        // Add user comment with MistLens branding and camera parameters
        let commentString = "Shot by MistLens Camera | \(lensType.description) | ISO \(Int(isoValue)) | \(apertureString) | \(shutterSpeedString)\(exposureCompString)"
        exifDict[kCGImagePropertyExifUserComment as String] = commentString

        metadata[kCGImagePropertyExifDictionary as String] = exifDict

        // Add GPS data if available
        if let location = location {
            var gpsDict: [String: Any] = [:]

            // Latitude
            let latitude = location.coordinate.latitude
            gpsDict[kCGImagePropertyGPSLatitude as String] = abs(latitude)
            gpsDict[kCGImagePropertyGPSLatitudeRef as String] = latitude >= 0 ? "N" : "S"

            // Longitude
            let longitude = location.coordinate.longitude
            gpsDict[kCGImagePropertyGPSLongitude as String] = abs(longitude)
            gpsDict[kCGImagePropertyGPSLongitudeRef as String] = longitude >= 0 ? "E" : "W"

            // Altitude
            gpsDict[kCGImagePropertyGPSAltitude as String] = location.altitude
            gpsDict[kCGImagePropertyGPSAltitudeRef as String] = location.altitude >= 0 ? 0 : 1

            // Timestamp
            gpsDict[kCGImagePropertyGPSTimeStamp as String] = dateFormatter.string(from: Date())
            gpsDict[kCGImagePropertyGPSDateStamp as String] = dateFormatter.string(from: Date())

            metadata[kCGImagePropertyGPSDictionary as String] = gpsDict
        }

        return metadata
    }

    // MARK: - Save to photo library

    private func saveToPhotoLibrary(fileURL: URL, quality: PhotoQuality, onAssetSaved: ((String?) -> Void)? = nil, completion: @escaping (Bool, URL?) -> Void) {
        // Read the image data from the file
        guard let imageData = try? Data(contentsOf: fileURL) else {
            print("Failed to read image data from file")
            completion(false, nil)
            return
        }
        
        // 提取原始文件名，用于后续建立映射关系
        let fileName = fileURL.lastPathComponent
        var placeholderAsset: PHObjectPlaceholder? = nil

        // 使用 PHPhotoLibrary 保存照片
        PHPhotoLibrary.shared().performChanges({
            // 创建照片请求
            let creationRequest = PHAssetCreationRequest.forAsset()

            // 添加照片数据
            creationRequest.addResource(with: .photo, data: imageData, options: nil)
            
            // 保存占位符引用，用于后续获取 localIdentifier
            placeholderAsset = creationRequest.placeholderForCreatedAsset

        }) { success, error in
            DispatchQueue.main.async {
                if success, let placeholder = placeholderAsset {
                    // 获取新创建照片的本地标识符
                    let localIdentifier = placeholder.localIdentifier
                    print("Photo saved successfully with EXIF data, localIdentifier: \(localIdentifier)")
                    
                    // 调用回调，传递系统标识符
                    onAssetSaved?(localIdentifier)
                    
                    completion(true, fileURL)
                } else if let error = error {
                    print("Error saving photo: \(error.localizedDescription)")
                    onAssetSaved?(nil) // 调用回调但传递 nil
                    completion(false, nil)
                } else {
                    print("Unknown error saving photo")
                    onAssetSaved?(nil) // 调用回调但传递 nil
                    completion(false, nil)
                }
            }
        }
    }

    // MARK: - Helpers

    private lazy var dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy:MM:dd HH:mm:ss"
        return formatter
    }()
}
