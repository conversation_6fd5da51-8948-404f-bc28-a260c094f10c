# 索引进度本地化实现

## 概述

为了提供更好的国际化用户体验，我们为索引构建进度界面添加了完整的本地化支持，目前支持中文（简体）和英文两种语言。

## 本地化字符串列表

### 界面文字
| 本地化键 | 中文 | 英文 | 说明 |
|---------|------|------|------|
| `index_building_title` | 正在构建相册索引 | Building Gallery Index | 进度界面标题 |
| `index_first_time_notice` | 首次使用需要建立索引 | First time use requires building index | 首次使用提示 |
| `index_completion_notice` | 索引完成后，相册打开将非常快速 | Gallery will open very quickly after index is complete | 完成后效果说明 |

### 进度消息
| 本地化键 | 中文 | 英文 | 说明 |
|---------|------|------|------|
| `index_scanning_files` | 🔍 正在扫描媒体文件... | 🔍 Scanning media files... | 初始扫描阶段 |
| `index_found_files` | 📁 找到 %d 个媒体文件 | 📁 Found %d media files | 文件发现阶段 |
| `index_processing_images` | 📷 正在处理图片 %d/%d | 📷 Processing images %d/%d | 图片处理阶段 |
| `index_processing_videos` | 🎥 正在处理视频 %d/%d | 🎥 Processing videos %d/%d | 视频处理阶段 |
| `index_sorting_files` | 📋 正在排序文件... | 📋 Sorting files... | 文件排序阶段 |
| `index_saving_index` | 💾 正在保存索引... | 💾 Saving index... | 索引保存阶段 |
| `index_build_complete` | ✅ 索引构建完成！ | ✅ Index build complete! | 构建完成 |

## 实现细节

### 1. 本地化文件位置
```
TrueFilm/
├── zh-Hans.lproj/
│   └── Localizable.strings (中文简体)
└── en.lproj/
    └── Localizable.strings (英文)
```

### 2. 使用方法

#### 简单文本本地化
```swift
Text(NSLocalizedString("index_building_title", comment: ""))
```

#### 带参数的文本本地化
```swift
let message = String.localizedStringWithFormat(
    NSLocalizedString("index_found_files", comment: ""), 
    totalFiles
)
```

#### 带多个参数的文本本地化
```swift
let message = String.localizedStringWithFormat(
    NSLocalizedString("index_processing_images", comment: ""), 
    processedFiles, 
    totalFiles
)
```

### 3. 修改的文件

#### IndexProgressView.swift
- 所有硬编码的中文文字替换为本地化字符串
- 使用 `NSLocalizedString` 函数获取本地化文本

#### AppMediaStorage.swift
- 所有进度消息使用本地化字符串
- 支持带参数的格式化消息

#### zh-Hans.lproj/Localizable.strings
- 添加了9个索引相关的中文本地化字符串

#### en.lproj/Localizable.strings
- 添加了9个索引相关的英文本地化字符串

## 设计原则

### 1. 一致性
- 所有索引相关的文字都使用统一的本地化键命名规范
- 键名使用 `index_` 前缀，便于识别和维护

### 2. 用户友好
- 保留了有意义的 emoji 图标，增强视觉效果
- 消息简洁明了，便于用户理解当前进度

### 3. 可维护性
- 所有本地化字符串集中管理
- 键名语义清晰，便于后续维护和扩展

### 4. 扩展性
- 预留了添加其他语言的空间
- 字符串格式支持参数化，便于适配不同语言的语法结构

## 测试建议

### 1. 语言切换测试
- 在系统设置中切换语言
- 验证所有索引界面文字正确显示

### 2. 参数格式测试
- 验证数字参数在不同语言中正确显示
- 确保格式化字符串不会导致崩溃

### 3. 长文本测试
- 验证不同语言的文字长度不会影响界面布局
- 确保文字在各种屏幕尺寸下正常显示

## 未来扩展

### 添加新语言支持
1. 创建新的语言目录（如 `ja.lproj/` 用于日语）
2. 复制 `Localizable.strings` 文件到新目录
3. 翻译所有索引相关的字符串
4. 在项目设置中添加新语言支持

### 添加新的索引消息
1. 在两个 `Localizable.strings` 文件中添加新的键值对
2. 在代码中使用 `NSLocalizedString` 获取本地化文本
3. 确保所有支持的语言都有对应的翻译

## 注意事项

- 本地化字符串的 comment 参数虽然为空，但建议在实际项目中添加有意义的注释
- 参数化字符串需要确保所有语言版本的参数顺序和类型一致
- 添加新的本地化字符串时，确保所有语言文件都同时更新 