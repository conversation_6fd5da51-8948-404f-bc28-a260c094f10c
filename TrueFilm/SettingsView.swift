//
//  SettingsView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

import SwiftUI
import Foundation
import CoreLocation
import UIKit

// 全局语言环境对象
class LanguageManager {
    static let shared = LanguageManager()
    
    // 当前语言
    private(set) var currentLanguage: String
    
    // 本地化Bundle
    private var localizationBundle: Bundle?
    
    // 是否需要重启应用
    var needsRestart: Bool = false
    
    // 获取UserDefaults里保存的语言或系统语言
    private func getInitialLanguage() -> String {
        let savedLang = UserDefaults.standard.string(forKey: "selectedLanguage")
        if let lang = savedLang, !lang.isEmpty {
            return lang
        }
        
        // 获取系统语言
        let preferredLanguage = Locale.preferredLanguages.first ?? "en"
        
        // 判断是否是中文（简体或繁体）
        if preferredLanguage.starts(with: "zh-Hans") || 
           preferredLanguage.starts(with: "zh-Hant") || 
           preferredLanguage.starts(with: "zh") {
            // 设置为简体中文
            return "zh-Hans"
        } else {
            // 其他语言默认使用英文
            return "en"
        }
    }
    
    private init() {
        // 先初始化所有存储属性
        currentLanguage = ""
        
        // 然后获取初始语言设置
        currentLanguage = getInitialLanguage()
        
        // 确保首次运行时也保存语言设置
        if UserDefaults.standard.string(forKey: "selectedLanguage") == nil {
            UserDefaults.standard.set(currentLanguage, forKey: "selectedLanguage")
            UserDefaults.standard.synchronize()
        }
        
        // 初始化Bundle
        updateLocalizationBundle()
        
        print("[LanguageManager] 初始化完成，当前语言: \(currentLanguage)")
    }
    
    // 更新语言设置
    func setLanguage(_ languageCode: String) -> Bool {
        print("[LanguageManager] 尝试切换语言: \(languageCode)，当前语言: \(currentLanguage)")
        
        // 检查是否是不同的语言
        let isNewLanguage = currentLanguage != languageCode
        
        if isNewLanguage {
            print("[LanguageManager] 检测到语言变更，保存新语言: \(languageCode)")
            
            // 保存设置
            UserDefaults.standard.set(languageCode, forKey: "selectedLanguage")
            UserDefaults.standard.set([languageCode], forKey: "AppleLanguages")
            UserDefaults.standard.synchronize()
            
            // 更新当前语言
            currentLanguage = languageCode
            
            // 更新Bundle
            updateLocalizationBundle()
            
            // 标记需要重启应用来完全应用语言
            needsRestart = true
            
            // 发送通知
            NotificationCenter.default.post(name: .languageDidChange, object: nil)
            
            return true
        } else {
            print("[LanguageManager] 语言未变更，保持当前语言: \(currentLanguage)")
            return false
        }
    }
    
    // 获取本地化字符串
    func localizedString(forKey key: String, comment: String = "") -> String {
        guard let bundle = localizationBundle else {
            return NSLocalizedString(key, comment: comment)
        }
        
        return bundle.localizedString(forKey: key, value: key, table: nil)
    }
    
    // 更新本地化资源包
    private func updateLocalizationBundle() {
        guard let path = Bundle.main.path(forResource: currentLanguage, ofType: "lproj") else {
            print("[LanguageManager] 未找到语言资源: \(currentLanguage).lproj")
            // 如果找不到特定语言的资源，回退到基础语言或主Bundle
            localizationBundle = Bundle.main
            return
        }
        
        localizationBundle = Bundle(path: path)
        print("[LanguageManager] 已加载语言资源包: \(currentLanguage).lproj")
    }
    
    // 打印当前保存的语言设置（用于调试）
    func printLanguageSettings() {
        let userDefaultsLang = UserDefaults.standard.string(forKey: "selectedLanguage") ?? "nil"
        let appleLang = UserDefaults.standard.array(forKey: "AppleLanguages") as? [String] ?? []
        print("[LanguageManager] 当前语言设置:")
        print("  - currentLanguage: \(currentLanguage)")
        print("  - UserDefaults[selectedLanguage]: \(userDefaultsLang)")
        print("  - UserDefaults[AppleLanguages]: \(appleLang)")
    }
}

// 定义通知名称
extension NSNotification.Name {
    static let languageDidChange = NSNotification.Name("languageDidChange")
    static let buttonSoundsChanged = NSNotification.Name("ButtonSoundsChanged")
    static let photoCountReset = NSNotification.Name("PhotoCountReset")
}

// 封装获取本地化字符串的函数
func LocalizedString(_ key: String, comment: String = "") -> String {
    return LanguageManager.shared.localizedString(forKey: key, comment: comment)
}

// 设置页面
struct SettingsView: View {
    @Binding var isPresented: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    // 强制视图刷新的状态
    @State private var refreshID = UUID()
    
    // 主题设置
    @AppStorage("appearanceMode") private var appearanceMode: AppearanceMode = .system
    
    // 按钮音效设置
    @AppStorage("buttonSoundsEnabled") private var buttonSoundsEnabled: Bool = true
    
    // 语言设置
    @State private var selectedLanguage: String
    @State private var previousLanguage: String
    
    // 语言选项
    let languages = [
        "en": "English",
        "zh-Hans": "简体中文"
    ]
    
    // 重置照片计数的确认状态
    @State private var showResetConfirmation = false
    
    // 教程显示状态
    @State private var showTutorial = false
    
    // 初始化
    init(isPresented: Binding<Bool>) {
        self._isPresented = isPresented
        
        // 从UserDefaults获取当前语言设置
        let currentLang = UserDefaults.standard.string(forKey: "selectedLanguage") ?? "zh-Hans"
        self._selectedLanguage = State(initialValue: currentLang)
        self._previousLanguage = State(initialValue: currentLang)
        
        print("[SettingsView] 初始化，当前语言: \(currentLang)")
    }

    var body: some View {
        GeometryReader { geometry in
            HStack(spacing: 0) {
                // 左侧区域 - Logo
                VStack(alignment: .center) {
                    Spacer()
                    
                    Image("logo")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 100, height: 100)
                        .cornerRadius(22)
                        .padding(.bottom, 30)
                    
                    Text("MistLens")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding(.bottom, 10)
                    
                    Text("VERSION".localized + " 1.0.0")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .padding(.top, 10)
                    
                    Spacer()
                }
                .frame(width: geometry.size.width * 0.4)
                
                // 右侧区域 - 设置项目
                ZStack {
                    ScrollView {
                            LazyVStack(spacing: 24) {
                                // 简介卡片
                                VStack(alignment: .leading, spacing: 12) {
                                    Text("APP_DESCRIPTION".localized)
                                        .font(.body)
                                        .lineSpacing(4)
                                        .id("description_\(selectedLanguage)_\(refreshID)")
                                }
                                .padding(20)
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(16)
                                .padding(.horizontal, 20)
                                .padding(.top,60)
                                
                                // 个性化设置卡片
                                VStack(spacing: 20) {
                                    // 卡片标题
                                    HStack {
                                        Image(systemName: "paintbrush.fill")
                                            .foregroundColor(.purple)
                                            .font(.title3)
                                        Text(selectedLanguage == "zh-Hans" ? "个性化" : "Personalization")
                                            .font(.title3)
                                            .fontWeight(.semibold)
                                        Spacer()
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.top, 20)
                                    
                                    // 主题设置
                                    VStack(alignment: .leading, spacing: 12) {
                                        HStack {
                                            Image(systemName: "circle.lefthalf.filled")
                                                .foregroundColor(.orange)
                                                .font(.title3)
                                            Text("THEME_COLOR".localized)
                                                .font(.headline)
                                                .id("theme_\(selectedLanguage)_\(refreshID)")
                                            Spacer()
                                        }
                                        
                                        Picker("", selection: $appearanceMode) {
                                            Text("SYSTEM".localized).tag(AppearanceMode.system)
                                                .id("system_\(selectedLanguage)_\(refreshID)")
                                            Text("DARK".localized).tag(AppearanceMode.dark)
                                                .id("dark_\(selectedLanguage)_\(refreshID)")
                                            Text("LIGHT".localized).tag(AppearanceMode.light)
                                                .id("light_\(selectedLanguage)_\(refreshID)")
                                        }
                                        .pickerStyle(SegmentedPickerStyle())
                                        .onChange(of: appearanceMode) { _ in
                                            updateAppearance()
                                        }
                                    }
                                    .padding(.horizontal, 20)
                                    
                                    Divider()
                                        .padding(.horizontal, 20)
                                    
                                    // 语言设置
                                    VStack(alignment: .leading, spacing: 12) {
                                        HStack {
                                            Image(systemName: "globe")
                                                .foregroundColor(.green)
                                                .font(.title3)
                                            Text("LANGUAGE".localized)
                                                .font(.headline)
                                                .id("language_title_\(selectedLanguage)_\(refreshID)")
                                            Spacer()
                                        }
                                        
                                        Menu {
                                            Button("English") {
                                                changeLanguage(to: "en")
                                            }
                                            
                                            Button("简体中文") {
                                                changeLanguage(to: "zh-Hans")
                                            }
                                        } label: {
                                            HStack {
                                                Text(languages[selectedLanguage] ?? "")
                                                    .foregroundColor(.primary)
                                                
                                                Spacer()
                                                
                                                Image(systemName: "chevron.down")
                                                    .font(.system(size: 14))
                                                    .foregroundColor(.secondary)
                                            }
                                            .padding(12)
                                            .background(Color(UIColor.tertiarySystemBackground))
                                            .cornerRadius(10)
                                        }
                                    }
                                    .padding(.horizontal, 20)
                                    
                                    Divider()
                                        .padding(.horizontal, 20)
                                    
                                    // 按钮音效设置
                                    HStack {
                                        Image(systemName: "speaker.wave.2.fill")
                                            .foregroundColor(.pink)
                                            .font(.title3)
                                        Text("BUTTON_SOUNDS".localized)
                                            .font(.headline)
                                            .id("button_sounds_title_\(selectedLanguage)_\(refreshID)")
                                        
                                        Spacer()
                                        
                                        Toggle("", isOn: $buttonSoundsEnabled)
                                            .onChange(of: buttonSoundsEnabled) { _ in
                                                // 发送通知让其他组件响应更改
                                                NotificationCenter.default.post(name: NSNotification.Name("ButtonSoundsChanged"), object: nil)
                                            }
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.bottom, 20)
                                }
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(16)
                                .padding(.horizontal, 20)
                                
                                // 功能卡片
                                VStack(spacing: 20) {
                                    // 卡片标题
                                    HStack {
                                        Image(systemName: "star.fill")
                                            .foregroundColor(.yellow)
                                            .font(.title3)
                                        Text(selectedLanguage == "zh-Hans" ? "功能" : "Features")
                                            .font(.title3)
                                            .fontWeight(.semibold)
                                        Spacer()
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.top, 20)
                                    
                                    // 教程按钮
                                    VStack(alignment: .leading, spacing: 12) {
                                        HStack {
                                            Image(systemName: "graduationcap.fill")
                                                .foregroundColor(.orange)
                                                .font(.title3)
                                            VStack(alignment: .leading, spacing: 4) {
                                                Text("TUTORIAL_TITLE".localized)
                                                    .font(.headline)
                                                    .id("tutorial_title_\(selectedLanguage)_\(refreshID)")
                                                Text("TUTORIAL_DESCRIPTION".localized)
                                                    .font(.subheadline)
                                                    .foregroundColor(.secondary)
                                                    .id("tutorial_description_\(selectedLanguage)_\(refreshID)")
                                            }
                                            Spacer()
                                        }
                                        
                                        Button(action: {
                                            showTutorial = true
                                        }) {
                                            HStack {
                                                Text("VIEW_TUTORIAL".localized)
                                                    .fontWeight(.medium)
                                                Spacer()
                                                Image(systemName: "arrow.right.circle.fill")
                                            }
                                            .foregroundColor(.white)
                                            .padding(14)
                                            .background(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [Color.orange, Color.orange.opacity(0.8)]),
                                                    startPoint: .leading,
                                                    endPoint: .trailing
                                                )
                                            )
                                            .cornerRadius(12)
                                        }
                                    }
                                    .padding(.horizontal, 20)
                                    
                                    Divider()
                                        .padding(.horizontal, 20)
                                    
                                    // 重置照片计数
                                    VStack(alignment: .leading, spacing: 12) {
                                        HStack {
                                            Image(systemName: "arrow.counterclockwise.circle.fill")
                                                .foregroundColor(.red)
                                                .font(.title3)
                                            VStack(alignment: .leading, spacing: 4) {
                                                Text("RESET_PHOTO_COUNT".localized)
                                                    .font(.headline)
                                                    .id("reset_photo_count_title_\(selectedLanguage)_\(refreshID)")
                                                Text("RESET_PHOTO_COUNT_DESCRIPTION".localized)
                                                    .font(.subheadline)
                                                    .foregroundColor(.secondary)
                                                    .id("reset_photo_count_description_\(selectedLanguage)_\(refreshID)")
                                            }
                                            Spacer()
                                        }
                                        
                                        Button(action: {
                                            showResetConfirmation = true
                                        }) {
                                            HStack {
                                                Text("RESET_COUNT".localized)
                                                    .fontWeight(.medium)
                                                Spacer()
                                                Image(systemName: "arrow.right.circle.fill")
                                            }
                                            .foregroundColor(.white)
                                            .padding(14)
                                            .background(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [Color.red, Color.red.opacity(0.8)]),
                                                    startPoint: .leading,
                                                    endPoint: .trailing
                                                )
                                            )
                                            .cornerRadius(12)
                                        }
                                        .alert("RESET_CONFIRM_TITLE".localized, isPresented: $showResetConfirmation) {
                                            Button("CANCEL".localized, role: .cancel) { }
                                            Button("RESET".localized, role: .destructive) {
                                                // 重置照片计数
                                                UserDefaults.standard.set(0, forKey: "totalPhotosProcessed")
                                                UserDefaults.standard.synchronize()
                                                
                                                // 发送通知以更新UI
                                                NotificationCenter.default.post(name: .photoCountReset, object: nil)
                                            }
                                        } message: {
                                            Text("RESET_CONFIRM_MESSAGE".localized)
                                        }
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.bottom, 20)
                                }
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(16)
                                .padding(.horizontal, 20)
                                
                                // 支持与反馈卡片
                                VStack(spacing: 20) {
                                    // 卡片标题
                                    HStack {
                                        Image(systemName: "heart.fill")
                                            .foregroundColor(.red)
                                            .font(.title3)
                                        Text(selectedLanguage == "zh-Hans" ? "支持与反馈" : "Support & Feedback")
                                            .font(.title3)
                                            .fontWeight(.semibold)
                                        Spacer()
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.top, 20)
                                    
                                    // APP评分功能
                                    VStack(alignment: .leading, spacing: 12) {
                                        HStack {
                                            Image(systemName: "star.circle.fill")
                                                .foregroundColor(.blue)
                                                .font(.title3)
                                            VStack(alignment: .leading, spacing: 4) {
                                                Text("APP_RATING_TITLE".localized)
                                                    .font(.headline)
                                                    .id("app_rating_title_\(selectedLanguage)_\(refreshID)")
                                                Text("APP_RATING_MESSAGE".localized)
                                                    .font(.subheadline)
                                                    .foregroundColor(.secondary)
                                                    .id("app_rating_message_\(selectedLanguage)_\(refreshID)")
                                            }
                                            Spacer()
                                        }
                                        
                                        Button(action: {
                                            // 打开App Store评分页面
                                            if let writeReviewURL = URL(string: "https://apps.apple.com/app/id6744577017?action=write-review") {
                                                UIApplication.shared.open(writeReviewURL)
                                            }
                                        }) {
                                            HStack {
                                                HStack(spacing: 8) {
                                                    ForEach(0..<5) { _ in
                                                        Image(systemName: "star.fill")
                                                            .foregroundColor(.yellow)
                                                            .font(.caption)
                                                    }
                                                }
                                                Text(selectedLanguage == "zh-Hans" ? "在App Store中评分" : "Rate on App Store")
                                                    .fontWeight(.medium)
                                                Spacer()
                                                Image(systemName: "arrow.up.right")
                                            }
                                            .foregroundColor(.white)
                                            .padding(14)
                                            .background(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [Color.blue, Color.blue.opacity(0.8)]),
                                                    startPoint: .leading,
                                                    endPoint: .trailing
                                                )
                                            )
                                            .cornerRadius(12)
                                        }
                                    }
                                    .padding(.horizontal, 20)
                                    
                                    Divider()
                                        .padding(.horizontal, 20)
                                    
                                    // 反馈功能
                                    VStack(alignment: .leading, spacing: 12) {
                                        HStack {
                                            Image(systemName: "envelope.circle.fill")
                                                .foregroundColor(.green)
                                                .font(.title3)
                                            VStack(alignment: .leading, spacing: 4) {
                                                Text(selectedLanguage == "zh-Hans" ? "欢迎反馈" : "Feedback")
                                                    .font(.headline)
                                                    .id("feedback_title_\(selectedLanguage)_\(refreshID)")
                                                Text(selectedLanguage == "zh-Hans" ? "发送邮件到: <EMAIL>" : "Send email to: <EMAIL>")
                                                    .font(.subheadline)
                                                    .foregroundColor(.secondary)
                                            }
                                            Spacer()
                                        }
                                        
                                        Button(action: {
                                            // 创建mailto链接
                                            let email = "<EMAIL>"
                                            let subject = "MistLens Feedback"
                                            let body = "MistLens Feedback"
                                            
                                            let encodedSubject = subject.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
                                            let encodedBody = body.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
                                            
                                            let mailtoString = "mailto:\(email)?subject=\(encodedSubject)&body=\(encodedBody)"
                                            
                                            if let mailtoUrl = URL(string: mailtoString) {
                                                UIApplication.shared.open(mailtoUrl)
                                            }
                                        }) {
                                            HStack {
                                                Image(systemName: "envelope.fill")
                                                    .font(.caption)
                                                Text(selectedLanguage == "zh-Hans" ? "写邮件" : "Compose Email")
                                                    .fontWeight(.medium)
                                                Spacer()
                                                Image(systemName: "arrow.up.right")
                                            }
                                            .foregroundColor(.green)
                                            .padding(14)
                                            .background(Color.green.opacity(0.1))
                                            .cornerRadius(12)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 12)
                                                    .stroke(Color.green.opacity(0.3), lineWidth: 1)
                                            )
                                        }
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.bottom, 20)
                                }
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(16)
                                .padding(.horizontal, 20)
                                
                                // 法律与支持卡片
                                VStack(spacing: 16) {
                                    // 卡片标题
                                    HStack {
                                        Image(systemName: "doc.text.fill")
                                            .foregroundColor(.gray)
                                            .font(.title3)
                                        Text(selectedLanguage == "zh-Hans" ? "法律与支持" : "Legal & Support")
                                            .font(.title3)
                                            .fontWeight(.semibold)
                                        Spacer()
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.top, 20)
                                    
                                    VStack(spacing: 12) {
                                        // 服务条款链接
                                        Button(action: {
                                            if let url = URL(string: "https://donuts-sneeze-ph2.craft.me/ygG5ztrtY5FRex") {
                                                UIApplication.shared.open(url)
                                            }
                                        }) {
                                            modernLinkStyle(
                                                text: selectedLanguage == "zh-Hans" ? "服务条款" : "Terms of Use",
                                                icon: "doc.plaintext",
                                                color: .blue
                                            )
                                        }
                                        
                                        // 隐私政策链接
                                        Button(action: {
                                            if let url = URL(string: "https://donuts-sneeze-ph2.craft.me/o7fMGXyTpMIR5w") {
                                                UIApplication.shared.open(url)
                                            }
                                        }) {
                                            modernLinkStyle(
                                                text: selectedLanguage == "zh-Hans" ? "隐私政策" : "Privacy Policy",
                                                icon: "hand.raised.fill",
                                                color: .purple
                                            )
                                        }
                                        
                                        // 支持信息链接
                                        Button(action: {
                                            if let url = URL(string: "https://donuts-sneeze-ph2.craft.me/43osHqSqva1Y7W") {
                                                UIApplication.shared.open(url)
                                            }
                                        }) {
                                            modernLinkStyle(
                                                text: selectedLanguage == "zh-Hans" ? "支持信息" : "Support",
                                                icon: "questionmark.circle.fill",
                                                color: .orange
                                            )
                                        }
                                    }
                                    .padding(.horizontal, 20)
                                    .padding(.bottom, 20)
                                }
                                .background(Color(UIColor.secondarySystemBackground))
                                .cornerRadius(16)
                                .padding(.horizontal, 20)
                                .padding(.bottom, 30)
                            }
                            .frame(maxWidth: .infinity)
                        }
                    
                    // 关闭按钮 - 绝对定位
                    VStack {
                        HStack {
                            Spacer()
                            Button(action: {
                                isPresented = false
                            }) {
                                ZStack {
                                    Circle()
                                        .fill(Color(UIColor.tertiarySystemBackground))
                                        .frame(width: 36, height: 36)
                                    
                                    Image(systemName: "xmark")
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding(.trailing, 20)
                            .padding(.top, 12)
                        }
                        Spacer()
                    }
                }
                .frame(width: geometry.size.width * 0.6)
            }
            .edgesIgnoringSafeArea(.all)
            .modifier(ConditionalPersistentSystemOverlaysModifier())
            // 使用稳定的ID以避免整个视图重建
            .id("settings_view")
            .onAppear {
                // 打印当前语言设置（调试用）
                print("[SettingsView] 视图加载，当前语言: \(selectedLanguage)")
                printLanguageSettings()
                
                // 如果是首次运行，设置默认值
                if !UserDefaults.standard.bool(forKey: "settingsInitialized") {
                    UserDefaults.standard.set(true, forKey: "buttonSoundsEnabled") // 默认开启按钮音效
                    UserDefaults.standard.set(true, forKey: "settingsInitialized")
                    appearanceMode = .system
                }
            }
            // 监听语言变更通知
            .onReceive(NotificationCenter.default.publisher(for: .languageDidChange)) { _ in
                print("[SettingsView] 收到语言变更通知")
                // 不重建整个视图，仅强制更新文本
                // self.refreshID = UUID()
            }
            .preferredColorScheme(colorSchemeForAppearanceMode)
            .fullScreenCover(isPresented: $showTutorial) {
                OnboardingView(isFirstLaunch: $showTutorial, isRealFirstLaunch: false)
            }
        }
    }
    
    // 根据外观模式返回对应的 ColorScheme
    private var colorSchemeForAppearanceMode: ColorScheme? {
        switch appearanceMode {
        case .light:
            return .light
        case .dark:
            return .dark
        case .system:
            return nil // 使用系统设置
        }
    }
    
    // 更新应用外观
    private func updateAppearance() {
        // 根据外观模式设置whiteUIMode
        let isWhiteMode = appearanceMode == .light
        UserDefaults.standard.set(isWhiteMode, forKey: "whiteUIMode")
        UserDefaults.standard.synchronize()
        
        // 通知其他组件UI模式已更改
        NotificationCenter.default.post(name: NSNotification.Name("UIModeChanged"), object: nil)
        
        print("[SettingsView] 外观模式已更新: \(appearanceMode), whiteUIMode: \(isWhiteMode)")
    }
    
    // 改变语言的方法
    private func changeLanguage(to newLanguage: String) {
        // 打印当前和新选择的语言（用于调试）
        print("[SettingsView] 语言选择变更: \(previousLanguage) -> \(newLanguage)")
        
        if previousLanguage != newLanguage {
            print("[SettingsView] 设置新语言: \(newLanguage)")
            
            // 在语言切换期间禁用UI交互
            UIApplication.shared.beginIgnoringInteractionEvents()
            
            // 保存新的语言设置
            UserDefaults.standard.set(newLanguage, forKey: "selectedLanguage")
            UserDefaults.standard.set([newLanguage], forKey: "AppleLanguages")
            UserDefaults.standard.synchronize()
            
            // 更新前一次语言
            selectedLanguage = newLanguage
            previousLanguage = newLanguage
            
            // 设置应用语言
            Bundle.setLanguage(newLanguage)
            
            // 只更新文本部分的刷新ID，而不是整个视图层次结构
            DispatchQueue.main.async {
                // 更新刷新ID以重新加载文本
                self.refreshID = UUID()
                
                // 发送语言变更通知 - 但没有重建视图
                NotificationCenter.default.post(name: .languageDidChange, object: nil)
                
                // 给相机足够的时间重启，然后再启用UI交互
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    if UIApplication.shared.isIgnoringInteractionEvents {
                        UIApplication.shared.endIgnoringInteractionEvents()
                    }
                    print("[SettingsView] 语言切换完成，已恢复UI交互")
                }
            }
            
            print("[SettingsView] 语言已设置为: \(newLanguage)")
        } else {
            print("[SettingsView] 选择了相同的语言，忽略")
        }
    }
    
    // 打印当前语言设置（调试用）
    private func printLanguageSettings() {
        let userDefaultsLang = UserDefaults.standard.string(forKey: "selectedLanguage") ?? "nil"
        let appleLang = UserDefaults.standard.array(forKey: "AppleLanguages") as? [String] ?? []
        print("[SettingsView] 当前语言设置:")
        print("  - selectedLanguage: \(selectedLanguage)")
        print("  - previousLanguage: \(previousLanguage)")
        print("  - UserDefaults[selectedLanguage]: \(userDefaultsLang)")
        print("  - UserDefaults[AppleLanguages]: \(appleLang)")
    }
    
    // 现代链接样式
    private func modernLinkStyle(text: String, icon: String, color: Color) -> some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title3)
                .frame(width: 24)
            
            Text(text)
                .font(.headline)
                .foregroundColor(.primary)
            
            Spacer()
            
            Image(systemName: "arrow.up.right")
                .foregroundColor(.secondary)
                .font(.caption)
        }
        .padding(16)
        .background(Color(UIColor.tertiarySystemBackground))
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(color.opacity(0.2), lineWidth: 1)
        )
    }
}

// 外观模式枚举
enum AppearanceMode: String, CaseIterable {
    case system
    case light
    case dark
}

// 使LocalizedStringKey与SwiftUI.Text兼容的扩展
extension Text {
    init(_ localizedString: String) {
        self.init(verbatim: localizedString)
    }
}

// 条件性的系统覆盖层修饰符
struct ConditionalPersistentSystemOverlaysModifier: ViewModifier {
    func body(content: Content) -> some View {
        if #available(iOS 16.0, *) {
            content.persistentSystemOverlays(.hidden)
        } else {
            content
        }
    }
}
