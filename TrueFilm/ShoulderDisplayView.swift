//
//  ShoulderDisplayView.swift
//  TrueFilm
//
//  Created by Augment on 2025/4/12.
//

import SwiftUI

struct ShoulderDisplayView: View {
    @ObservedObject var viewModel: GlassDisplayViewModel
    var onTap: () -> Void
    @State private var isWhiteMode: Bool = UserDefaults.standard.bool(forKey: "whiteUIMode")
    
    // Add language refresh ID
    @State private var languageRefreshID = UUID()
    
    // TipKit 已移除

    var body: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 25)
                .fill(Color.black)
                .frame(width: 160, height: 50)
                .overlay(
                    RoundedRectangle(cornerRadius: 25)
                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                )
                .shadow(color: Color.black.opacity(0.2), radius: 3, x: 0, y: 1)
                .background(
                    // 内部背景，添加金属质感的渐变
                    RoundedRectangle(cornerRadius: 25)
                        .fill(isWhiteMode ? Color(#colorLiteral(red: 0.94, green: 0.94, blue: 0.94, alpha: 1)) : Color.black)  // #F0F0F0 or black
                        .frame(width: 160, height: 50)
                )

            // 文本内容
            Text(viewModel.displayText)
                .font(.system(size: 20, weight: .medium, design: .default))
                .foregroundColor(Color.white)
                .tracking(1.5)
                .frame(width: 150)
                .multilineTextAlignment(.center)
        }
        // Add ID to force refresh on language change
        .id("shoulder_display_\(languageRefreshID)")
        .onTapGesture {
            onTap()
        }
        .onAppear {
            // Subscribe to UI mode changes
            NotificationCenter.default.addObserver(forName: NSNotification.Name("UIModeChanged"), object: nil, queue: .main) { _ in
                // Update UI mode from UserDefaults
                self.isWhiteMode = UserDefaults.standard.bool(forKey: "whiteUIMode")
            }
            
            // Subscribe to language changes
            NotificationCenter.default.addObserver(forName: .languageDidChange, object: nil, queue: .main) { _ in
                print("ShoulderDisplayView: 检测到语言变更，刷新UI")
                // Force UI refresh by updating ID
                self.languageRefreshID = UUID()
                // Also refresh the display text in the view model
                self.viewModel.updateDisplayText()
            }
        }
        .onDisappear {
            NotificationCenter.default.removeObserver(self)
        }
    }
}

#Preview {
    ZStack {
        Color.black
        
        ShoulderDisplayView(
            viewModel: {
                let vm = GlassDisplayViewModel()
                vm.photoCount = 12
                vm.showPhotoCount = false
                vm.selectedControl = .mist
                vm.blackMistIntensity = 0.75
                return vm
            }(),
            onTap: {}
        )
    }
}
