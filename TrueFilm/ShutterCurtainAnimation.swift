import SwiftUI

/// A traditional camera shutter curtain animation
/// Simulates the mechanical shutter of a film camera with a single curtain
struct ShutterCurtainAnimation: View {
    // Animation state
    @Binding var isAnimating: Bool

    // Animation duration settings
    var closeDuration: Double = 0.25  // Duration for curtain to close (move down)
    var stayClosedDuration: Double = 0.2  // Duration curtain stays closed
    var openDuration: Double = 0.3  // Duration for curtain to open (move up)

    // Animation progress states
    @State private var curtainPosition: CGFloat = 0
    @State private var curtainOpacity: Double = 0
    @State private var curtainBlur: CGFloat = 0

    // Curtain texture and appearance
    private let curtainColor = Color.black
    private let curtainShadowRadius: CGFloat = 3
    private let curtainShadowOpacity: CGFloat = 0.8

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Only show when animation is active
                if isAnimating {
                    // Single curtain with texture and shadow
                    VStack(spacing: 0) {
                        // Top edge with subtle highlight
                        Rectangle()
                            .fill(Color.white.opacity(0.15))
                            .frame(width: geometry.size.width, height: 1)
                        
                        // Main curtain body
                        Rectangle()
                            .fill(curtainColor)
                            .overlay(
                                // Subtle texture overlay
                                Rectangle()
                                    .fill(LinearGradient(
                                        gradient: Gradient(colors: [
                                            Color.black.opacity(0.7),
                                            Color.black.opacity(0.85),
                                            Color.black.opacity(0.7)
                                        ]),
                                        startPoint: .leading,
                                        endPoint: .trailing
                                    ))
                                    .blendMode(.overlay)
                            )
                            .overlay(
                                // Horizontal lines to simulate fabric texture
                                VStack(spacing: 3) {
                                    ForEach(0..<20, id: \.self) { _ in
                                        Rectangle()
                                            .fill(Color.black.opacity(0.3))
                                            .frame(height: 1)
                                        Spacer()
                                    }
                                }
                                .blendMode(.overlay)
                            )
                            .frame(width: geometry.size.width, height: geometry.size.height * 1.5) // Extra height to ensure complete coverage

                        // Bottom edge with gradient to make it look thicker
                        Rectangle()
                            .fill(LinearGradient(
                                gradient: Gradient(colors: [curtainColor, curtainColor.opacity(0.85)]),
                                startPoint: .top,
                                endPoint: .bottom
                            ))
                            .frame(width: geometry.size.width, height: 4)
                    }
                    .shadow(color: Color.black.opacity(curtainShadowOpacity), radius: curtainShadowRadius, x: 0, y: 4)
                    .position(x: geometry.size.width / 2, y: curtainPosition)
                    .blur(radius: curtainBlur)
                    .opacity(curtainOpacity)
                }
            }
            .onChange(of: isAnimating) { newValue in
                if newValue {
                    // Start the animation sequence when isAnimating becomes true
                    startAnimation(screenSize: geometry.size)
                }
            }
            .clipped() // Important: clip the animation to the bounds of its container
        }
    }

    /// Starts the shutter curtain animation sequence 
    /// - Parameter screenSize: The size of the screen for calculating positions
    private func startAnimation(screenSize: CGSize) {
        // Reset initial position (curtain off-screen at the bottom)
        curtainPosition = screenSize.height * 1.5  // Position curtain fully below the view
        curtainOpacity = 1.0
        curtainBlur = 0.5

        // Step 1: Quickly move curtain upward into view (closing the shutter from bottom to middle)
        withAnimation(.easeOut(duration: closeDuration)) {
            // Move curtain to cover the entire screen
            curtainPosition = screenSize.height / 2  // Middle of screen
            curtainBlur = 0
        }

        // Step 2: Wait while shutter is closed
        DispatchQueue.main.asyncAfter(deadline: .now() + closeDuration + stayClosedDuration) {
            // Step 3: Open the shutter by moving the curtain back down (instead of continuing upward)
            withAnimation(.easeIn(duration: openDuration)) {
                // Move curtain back down and out of view
                curtainPosition = screenSize.height * 1.5  // Back to below the view
                curtainBlur = 0.5
                curtainOpacity = 0.9
            }

            // Step 4: Reset the animation state when complete
            DispatchQueue.main.asyncAfter(deadline: .now() + openDuration) {
                isAnimating = false
            }
        }
    }
}

// Preview provider for SwiftUI canvas
struct ShutterCurtainAnimation_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            // Background to see the animation against
            Image(systemName: "camera.fill")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 100, height: 100)
                .foregroundColor(.gray)

            // The shutter animation
            ShutterCurtainAnimation(isAnimating: .constant(true))
        }
        .frame(width: 300, height: 400)
        .background(Color.white)
        .previewLayout(.sizeThatFits)
    }
}
