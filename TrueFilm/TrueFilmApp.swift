//
//  TrueFilmApp.swift
//  TrueFilm (MistLens)
//
//  Created by late night king on 2025/4/12.
//  Updated by Augment on 2025/5/15.
//

import SwiftUI
import AVFoundation
import CoreLocation
import Photos
import UIKit

// Add a class to hide home indicator system-wide
class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // 在应用启动时初始化语言设置
        initializeLanguage()
        return true
    }
    
    // 初始化应用语言设置
    private func initializeLanguage() {
        // 从UserDefaults获取保存的语言设置
        if let languageCode = UserDefaults.standard.string(forKey: "selectedLanguage") {
            print("[AppDelegate] 从UserDefaults加载语言设置: \(languageCode)")
            
            // 设置应用语言为保存的语言
            UserDefaults.standard.set([languageCode], forKey: "AppleLanguages")
            UserDefaults.standard.synchronize()
            
            // 初始化对应语言的Bundle
            if let path = Bundle.main.path(forResource: languageCode, ofType: "lproj"),
               let bundle = Bundle(path: path) {
                print("[AppDelegate] 成功加载语言资源包: \(languageCode)")
                objc_setAssociatedObject(Bundle.main, &bundleKey, bundle, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            } else {
                print("[AppDelegate] 未找到语言资源包: \(languageCode)")
            }
        } else {
            // 如果没有保存的语言设置，根据设备语言判断
            let preferredLanguage = Locale.preferredLanguages.first ?? "en"
            let languageCode: String
            
            if preferredLanguage.starts(with: "zh-Hans") || 
               preferredLanguage.starts(with: "zh-Hant") || 
               preferredLanguage.starts(with: "zh") {
                languageCode = "zh-Hans"  // 中文
            } else {
                languageCode = "en"  // 英文
            }
            
            print("[AppDelegate] 使用默认语言设置: \(languageCode)")
            UserDefaults.standard.set(languageCode, forKey: "selectedLanguage")
            UserDefaults.standard.set([languageCode], forKey: "AppleLanguages")
            UserDefaults.standard.synchronize()
        }
    }
}

// 用于保存Bundle的关联对象Key
var bundleKey: UInt8 = 0

// 扩展Bundle以支持动态切换语言
extension Bundle {
    static let onLanguageChangeCallbacks = NSHashTable<AnyObject>.weakObjects()
    
    class func setLanguage(_ language: String) {
        guard let path = Bundle.main.path(forResource: language, ofType: "lproj"),
              let bundle = Bundle(path: path) else {
            print("[Bundle] 语言资源未找到: \(language)")
            return
        }
        
        objc_setAssociatedObject(Bundle.main, &bundleKey, bundle, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        print("[Bundle] 已设置语言为: \(language)")
        
        // 通知所有注册的对象进行刷新
        for observer in onLanguageChangeCallbacks.allObjects {
            if let callback = observer as? () -> Void {
                callback()
            }
        }
    }
    
    // 获取当前语言的Bundle
    class var languageBundle: Bundle? {
        return objc_getAssociatedObject(Bundle.main, &bundleKey) as? Bundle
    }
    
    // 添加语言变更监听
    class func addLanguageChangeCallback(_ callback: @escaping () -> Void) {
        onLanguageChangeCallbacks.add(callback as AnyObject)
    }
}

// 扩展String以支持本地化
extension String {
    var localized: String {
        if let bundle = Bundle.languageBundle {
            return bundle.localizedString(forKey: self, value: self, table: nil)
        }
        return NSLocalizedString(self, comment: "")
    }
}

// Custom UIViewController that hides the home indicator
class HiddenHomeIndicatorViewController: UIViewController {
    override var prefersHomeIndicatorAutoHidden: Bool {
        return true
    }
}

@main
struct TrueFilmApp: App {
    // Register app delegate
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    // 使用 StateObject 确保 LocationManager 在整个应用生命周期内保持活跃
    @StateObject private var locationManager = LocationManager.shared
    
    // 用于强制视图刷新的状态
    @State private var refreshTrigger = UUID()

    @AppStorage("whiteUIMode") private var whiteUIMode: Bool = false

    init() {
        // 设置默认的照片质量为最高
        if !UserDefaults.standard.bool(forKey: "settingsInitialized") {
            UserDefaults.standard.set(2, forKey: "photoQuality") // 默认最高质量
            UserDefaults.standard.set(true, forKey: "saveExifInfo") // 默认保存EXIF信息
            UserDefaults.standard.set(true, forKey: "settingsInitialized")
        }

        // Request camera permissions at app startup
        AVCaptureDevice.requestAccess(for: .video) { granted in
            if granted {
                print("Camera access granted")
            } else {
                print("Camera access denied")
            }
        }

        // 请求位置权限
        LocationManager.shared.requestPermission()

        // 请求照片库权限
        PHPhotoLibrary.requestAuthorization { status in
            if status == .authorized {
                print("Photo library access granted")
            } else {
                print("Photo library access denied")
            }
        }
        
        // 注册语言变更通知
        NotificationCenter.default.addObserver(forName: NSNotification.Name("languageDidChange"), 
                                               object: nil, 
                                               queue: .main) { _ in
            print("[TrueFilmApp] 收到语言变更通知")
            // 这里不能直接更新refreshTrigger，因为init中不能访问self的属性
        }

        // TipKit 配置已移除
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .id("app_root_\(refreshTrigger)")  // 使用id强制根视图刷新
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("languageDidChange"))) { _ in
                    // 强制刷新整个应用界面
                    refreshTrigger = UUID()
                }
        }
    }
}
