/*
  Localizable.strings
  MistLens
*/

// Camera View
"BLACK_MIST" = "Black Mist";
"NO_FILTER" = "No Filter";
"BLACK_MIST_ENABLED" = "Black Mist Enabled";
"SETTINGS" = "Settings";

// Control Buttons
"MIST" = "MIST";
"Highlight" = "HIGHLIGHT";
"EV" = "EV";
"FILM" = "FILM";
"PHOTOS" = "PHOTOS";
"THLD" = "THLD";
"BRIT" = "BRIT";

// Camera Lens
"前置" = "Front";

// Settings Panel
"BLACK_MIST_SETTINGS" = "Black Mist Settings";
"THRESHOLD" = "Threshold: %@";
"INTENSITY" = "Intensity: %@";
"BRIGHTNESS" = "Brightness: %@";
"RED_TINT" = "Red Tint: %@";

// General Settings
"VERSION" = "Version";
"THEME_COLOR" = "Theme Color";
"SYSTEM" = "System";
"DARK" = "Dark";
"LIGHT" = "Light";
"LANGUAGE" = "Language";
"BUTTON_SOUNDS" = "Button Sounds";
"LANGUAGE_CHANGED" = "Language Changed";
"RESTART_APP" = "To ensure all interface elements display correctly, it is recommended to restart the app.";
"LANGUAGE_CHANGED_SUCCESS" = "Language changed successfully";
"APP_DESCRIPTION" = "I love Black Mist filters so much that I use them on almost every lens. I especially love how they soften and bloom the highlights. But after searching for a long time without finding a real-time rendering app, I decided to make one myself.";
"RESTART_NOW" = "Restart Now";
"LATER" = "Later";
"PLEASE_CLOSE_APP" = "Please Close App";
"REOPEN_APP_INSTRUCTION" = "Language settings have been changed. Please completely close the app and reopen it to apply the new language settings.";

// Camera Messages
"PHOTO_SAVED" = "Photo saved to album";
"PHOTO_SAVE_FAILED" = "Failed to save photo";
"CAMERA_ACCESS_DENIED" = "Camera access denied";
"CANNOT_ADD_CAMERA_INPUT" = "Cannot add camera input";
"PREVIEW_LAYER_SETUP_COMPLETE" = "Preview layer and filter view setup complete";
"FILTER_VIEW_SHOWN" = "Filter view shown";
"FILTER_VIEW_HIDDEN" = "Filter view hidden";
"BLACK_MIST_PARAMS" = "Black Mist parameters - Threshold: %@, Intensity: %@";
"IMAGE_SAVE_SUCCESS" = "Image saved successfully";
"IMAGE_SAVE_FAILED" = "Failed to save image: %@";
"UPDATING_PREVIEW_LAYER" = "Updating preview layer size: %@";
"UPDATING_FILTER_BG" = "Updating filter background view size: %@";
"UPDATING_FILTER_IMG" = "Updating filter image view size: %@";
"PROCESSED_FRAME" = "Processed a frame with Black Mist effect";

// TipKit Tips (已移除)

// Gallery View
"gallery_sort_newest" = "Latest";
"gallery_sort_oldest" = "Earliest";
"gallery_empty" = "No media files yet";
"gallery_select" = "Select";
"gallery_cancel" = "Cancel";
"gallery_done" = "Done";
"gallery_close" = "Close";
"gallery_delete" = "Delete";
"gallery_delete_selected" = "Delete Selected (%d)";
"gallery_share" = "Share";
"gallery_delete_confirm_title" = "Confirm Deletion";
"gallery_delete_confirm_message" = "Are you sure you want to delete this file?";
"gallery_delete_confirm_details" = "This action cannot be undone, the file will be permanently deleted.";
"gallery_multi_delete_title" = "Batch Delete";
"gallery_multi_delete_message" = "Are you sure you want to delete %d files?";
"gallery_multi_delete_details" = "This action cannot be undone, all selected files will be permanently deleted.";
"gallery_select_all" = "Select All";
"gallery_deselect_all" = "Deselect All";
"gallery_album" = "Album";

// Index Building
"index_building_title" = "Building Gallery Index";
"index_first_time_notice" = "First time use requires building index";
"index_completion_notice" = "Gallery will open very quickly after index is complete";
"index_scanning_files" = "🔍 Scanning media files...";
"index_found_files" = "📁 Found %d media files";
"index_processing_images" = "📷 Processing images %d/%d";
"index_processing_videos" = "🎥 Processing videos %d/%d";
"index_sorting_files" = "📋 Sorting files...";
"index_saving_index" = "💾 Saving index...";
"index_build_complete" = "✅ Index build complete!";

// App Rating
"APP_RATING_TITLE" = "Rate Our App";
"APP_RATING_MESSAGE" = "If you enjoy using our app, please take a moment to rate it. Thank you for your support!";

// Tutorial
"TUTORIAL_TITLE" = "Interactive Tutorial";
"TUTORIAL_DESCRIPTION" = "Learn how to use all the key features of MistLens with our interactive tutorial.";
"VIEW_TUTORIAL" = "View Tutorial";

// Photo Count Reset
"RESET_PHOTO_COUNT" = "Reset Photo Count";
"RESET_PHOTO_COUNT_DESCRIPTION" = "Reset the count of photos processed by the app.";
"RESET_CONFIRM_TITLE" = "Confirm Reset";
"RESET_CONFIRM_MESSAGE" = "This will reset all photo count statistics. This action cannot be undone.";
"RESET_COUNT" = "Reset Count";
"CANCEL" = "Cancel";
"RESET" = "Reset";

// Onboarding - Interactive Tutorial
"ONBOARDING_WELCOME_TITLE" = "Welcome to MistLens";
"ONBOARDING_WELCOME_DESC" = "Professional film-inspired photography with real-time Black Mist effects. Create stunning cinematic imagery with ease.";
"APP_DETAILED_DESCRIPTION" = "Black Mist filters create a dreamy, cinematic look by softening highlights and adding a subtle glow to bright areas. This effect makes skin tones appear naturally luminous while preserving image detail and texture.\n\nMistLens uses advanced real-time rendering technology to simulate this optical effect digitally. Unlike traditional post-processing apps, you can see the exact result before capturing, ensuring perfect exposure and composition.\n\nIdeal for portrait photography, fashion shoots, and creating atmospheric cinematic content. Professional filmmakers and content creators use Black Mist effects to achieve that coveted film-like aesthetic in their work.";
"ONBOARDING_DIAL_BUTTON_TITLE" = "Dial & Button Controls";
"ONBOARDING_DIAL_BUTTON_DESC" = "Rotate the dial to fine-tune parameters quickly. Tap the dial center to take photos. Tap Lock button to prevent accidental dial rotation.";
"ONBOARDING_FOCAL_LENGTH_TITLE" = "Focal Length Control";
"ONBOARDING_FOCAL_LENGTH_DESC" = "Swipe left or right on the focal length text to switch between different zoom levels (0.5×, 1×, 2×, 3×, 5×).";
"ONBOARDING_FLASH_RECORD_TITLE" = "Flash & Record";
"ONBOARDING_FLASH_RECORD_DESC" = "Toggle flash with the lightning bolt button. Press and hold the record button to capture video with real-time mist effects.";
"ONBOARDING_SHOULDER_SCREEN_TITLE" = "Mini Display";
"ONBOARDING_SHOULDER_SCREEN_DESC" = "The shoulder screen displays camera parameters like aperture, shutter speed, and ISO. Tap it to quickly enter camera settings and configure your shooting preferences.";
"NEXT" = "Next";
"SKIP" = "Skip";
"GET_STARTED" = "Get Started";

// Film Simulation Panel
"FILM_SIMULATION" = "Film Simulation";
"FILM_INTENSITY" = "Intensity";
"IMPORT_LUT" = "Import LUT";
"EDIT_FILTER" = "Edit Filter";
"EDIT_NAME" = "Edit Name";
"FILTER_NAME" = "Filter Name";
"SAVE_NAME" = "Save Name";
"DELETE_FILTER" = "Delete Filter";
"CONFIRM_DELETE" = "Confirm Delete";
"DELETE_FILTER_MESSAGE" = "Are you sure you want to delete filter「%@」? This action cannot be undone.";
"SET_LUT_TITLE" = "Set LUT Filter Title";
"ENTER_FILTER_NAME" = "Enter filter name";
"IMPORT_LUT_TITLE" = "Import LUT";
"SAVE" = "Save";
"DELETE" = "Delete";
"NONE_FILTER" = "None";

// Import Messages
"LUT_IMPORT_SUCCESS" = "LUT import successful!";
"CUBE_LUT_IMPORT_SUCCESS" = "CUBE LUT import successful!";
"IMPORT_FAILED" = "Import failed";
"UNSUPPORTED_LUT_DIMENSION" = "Unsupported LUT dimension: %d";
"SUPPORTED_DIMENSIONS" = "Supported dimensions: 16, 32, 64";
"DIMENSION_65_HINT" = "Detected dimension 65, which usually indicates a CUBE file format issue.\nPlease confirm the file is a standard 3D LUT format with correct LUT_3D_SIZE declaration.";
"DIMENSION_TOO_LARGE_HINT" = "Large dimensions may cause performance issues.\nRecommend using 64 dimension or smaller LUT files.";
"UNSUPPORTED_PNG_FORMAT" = "Unsupported PNG LUT format";
"SUPPORTED_PNG_FORMATS" = "Supported PNG formats:\n• 512×512 (64³ LUT)\n• 1024×32 (64³ LUT)\n• 256×256 (32³ LUT)\n• 64×64 (16³ LUT)";
"CURRENT_IMAGE_SIZE" = "Current image: %dx%d";
"CUBE_FORMAT_HINT" = "Tip: Also supports importing .cube format LUT files";
"CANNOT_PARSE_IMAGE" = "Cannot parse image format";
"CANNOT_ACCESS_FILE" = "Cannot access selected file";
"READ_FILE_FAILED" = "Failed to read file: %@";
"CANNOT_CONVERT_CUBE" = "Cannot convert .cube file to image format";
"SAVE_LUT_FAILED" = "Failed to save LUT file: %@";
"CANNOT_GENERATE_PNG" = "Cannot generate PNG data";
