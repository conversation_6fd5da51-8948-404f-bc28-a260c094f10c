#!/bin/bash

# Reset Onboarding Script for MistLens
# 重置 MistLens 应用的引导页面状态

echo "🔄 正在重置 MistLens 引导页面状态..."

# 获取应用的Bundle ID
BUNDLE_ID="com.yourcompany.TrueFilm"  # 请根据实际Bundle ID修改

# 方法1: 使用xcrun simctl (适用于模拟器)
if command -v xcrun &> /dev/null; then
    echo "📱 尝试在模拟器中重置..."
    xcrun simctl privacy booted reset all $BUNDLE_ID 2>/dev/null
    echo "✅ 已重置模拟器中的应用数据"
else
    echo "⚠️  xcrun 不可用，跳过模拟器重置"
fi

# 方法2: 提示用户手动操作
echo ""
echo "📋 手动重置步骤:"
echo "1. 在设备上长按 MistLens 应用图标"
echo "2. 选择'移除App' -> '删除App'"
echo "3. 重新安装应用"
echo ""
echo "或者在代码中临时修改:"
echo "@AppStorage(\"firstAppLaunch\") private var firstAppLaunch: Bool = true"
echo ""

# 方法3: 提供开发用的 UserDefaults 重置代码
echo "🛠️  开发者选项: 在应用中添加以下代码来重置状态"
echo ""
echo "UserDefaults.standard.removeObject(forKey: \"firstAppLaunch\")"
echo "UserDefaults.standard.synchronize()"
echo ""

echo "✨ 重置完成！下次启动应用将显示引导页面。" 